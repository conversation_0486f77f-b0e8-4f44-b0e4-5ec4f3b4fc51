spi_lcd_320x240_2.0inch/lv_draw_sw_dither.o: \
  ..\lvgl\src\draw\sw\lv_draw_sw_dither.c \
  ..\lvgl\src\draw\sw\lv_draw_sw_dither.h \
  ..\lvgl\src\draw\sw\..\..\core\lv_obj_pos.h \
  ..\lvgl\src\draw\sw\..\..\core\..\misc\lv_area.h \
  ..\lvgl\src\draw\sw\..\..\core\..\misc\..\lv_conf_internal.h \
  ..\lvgl\src\draw\sw\..\..\core\..\misc\..\lv_conf_kconfig.h \
  ..\lvgl\lv_conf.h ..\lvgl\src\draw\sw\lv_draw_sw_gradient.h \
  ..\lvgl\src\draw\sw\..\..\misc\lv_color.h \
  ..\lvgl\src\draw\sw\..\..\misc\..\lv_conf_internal.h \
  ..\lvgl\src\draw\sw\..\..\misc\lv_assert.h \
  ..\lvgl\src\draw\sw\..\..\misc\lv_log.h \
  ..\lvgl\src\draw\sw\..\..\misc\lv_types.h \
  ..\lvgl\src\draw\sw\..\..\misc\lv_mem.h \
  ..\lvgl\src\draw\sw\..\..\misc\lv_math.h \
  ..\lvgl\src\draw\sw\..\..\misc\lv_style.h \
  ..\lvgl\src\draw\sw\..\..\misc\..\font\lv_font.h \
  ..\lvgl\src\draw\sw\..\..\misc\..\font\..\lv_conf_internal.h \
  ..\lvgl\src\draw\sw\..\..\misc\..\font\lv_symbol_def.h \
  ..\lvgl\src\draw\sw\..\..\misc\..\font\..\misc\lv_area.h \
  ..\lvgl\src\draw\sw\..\..\misc\lv_area.h \
  ..\lvgl\src\draw\sw\..\..\misc\lv_anim.h \
  ..\lvgl\src\draw\sw\..\..\misc\lv_txt.h \
  ..\lvgl\src\draw\sw\..\..\misc\lv_printf.h \
  ..\lvgl\src\draw\sw\..\..\misc\lv_bidi.h \
  ..\lvgl\src\draw\sw\..\..\misc\lv_style_gen.h
