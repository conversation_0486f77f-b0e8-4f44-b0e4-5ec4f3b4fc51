spi_lcd_320x240_2.0inch/lv_hal_disp.o: ..\lvgl\src\hal\lv_hal_disp.c \
  ..\lvgl\src\hal\lv_hal.h ..\lvgl\src\hal\lv_hal_disp.h \
  ..\lvgl\src\hal\..\draw\lv_draw.h \
  ..\lvgl\src\hal\..\draw\..\lv_conf_internal.h \
  ..\lvgl\src\hal\..\draw\..\lv_conf_kconfig.h ..\lvgl\lv_conf.h \
  ..\lvgl\src\hal\..\draw\..\misc\lv_style.h \
  ..\lvgl\src\hal\..\draw\..\misc\..\font\lv_font.h \
  ..\lvgl\src\hal\..\draw\..\misc\..\font\..\lv_conf_internal.h \
  ..\lvgl\src\hal\..\draw\..\misc\..\font\lv_symbol_def.h \
  ..\lvgl\src\hal\..\draw\..\misc\..\font\..\misc\lv_area.h \
  ..\lvgl\src\hal\..\draw\..\misc\..\font\..\misc\..\lv_conf_internal.h \
  ..\lvgl\src\hal\..\draw\..\misc\lv_color.h \
  ..\lvgl\src\hal\..\draw\..\misc\..\lv_conf_internal.h \
  ..\lvgl\src\hal\..\draw\..\misc\lv_assert.h \
  ..\lvgl\src\hal\..\draw\..\misc\lv_log.h \
  ..\lvgl\src\hal\..\draw\..\misc\lv_types.h \
  ..\lvgl\src\hal\..\draw\..\misc\lv_mem.h \
  ..\lvgl\src\hal\..\draw\..\misc\lv_math.h \
  ..\lvgl\src\hal\..\draw\..\misc\lv_area.h \
  ..\lvgl\src\hal\..\draw\..\misc\lv_anim.h \
  ..\lvgl\src\hal\..\draw\..\misc\lv_txt.h \
  ..\lvgl\src\hal\..\draw\..\misc\lv_printf.h \
  ..\lvgl\src\hal\..\draw\..\misc\lv_bidi.h \
  ..\lvgl\src\hal\..\draw\..\misc\lv_style_gen.h \
  ..\lvgl\src\hal\..\draw\lv_img_decoder.h \
  ..\lvgl\src\hal\..\draw\lv_img_buf.h \
  ..\lvgl\src\hal\..\draw\..\misc\lv_fs.h \
  ..\lvgl\src\hal\..\draw\lv_img_cache.h \
  ..\lvgl\src\hal\..\draw\lv_draw_rect.h \
  ..\lvgl\src\hal\..\draw\sw\lv_draw_sw_gradient.h \
  ..\lvgl\src\hal\..\draw\sw\..\..\misc\lv_color.h \
  ..\lvgl\src\hal\..\draw\sw\..\..\misc\lv_style.h \
  ..\lvgl\src\hal\..\draw\sw\lv_draw_sw_dither.h \
  ..\lvgl\src\hal\..\draw\sw\..\..\core\lv_obj_pos.h \
  ..\lvgl\src\hal\..\draw\sw\..\..\core\..\misc\lv_area.h \
  ..\lvgl\src\hal\..\draw\lv_draw_label.h \
  ..\lvgl\src\hal\..\draw\lv_draw_img.h \
  ..\lvgl\src\hal\..\draw\lv_draw_line.h \
  ..\lvgl\src\hal\..\draw\lv_draw_triangle.h \
  ..\lvgl\src\hal\..\draw\lv_draw_arc.h \
  ..\lvgl\src\hal\..\draw\lv_draw_mask.h \
  ..\lvgl\src\hal\..\draw\lv_draw_transform.h \
  ..\lvgl\src\hal\..\draw\lv_draw_layer.h \
  ..\lvgl\src\hal\..\misc\lv_color.h ..\lvgl\src\hal\..\misc\lv_area.h \
  ..\lvgl\src\hal\..\misc\lv_ll.h ..\lvgl\src\hal\..\misc\lv_timer.h \
  ..\lvgl\src\hal\..\misc\..\lv_conf_internal.h \
  ..\lvgl\src\hal\..\misc\..\hal\lv_hal_tick.h \
  ..\lvgl\src\hal\..\misc\..\hal\..\lv_conf_internal.h \
  ..\lvgl\src\hal\lv_hal_indev.h ..\lvgl\src\hal\..\lv_conf_internal.h \
  ..\lvgl\src\hal\lv_hal_tick.h ..\lvgl\src\hal\..\misc\lv_mem.h \
  ..\lvgl\src\hal\..\misc\lv_gc.h ..\lvgl\src\hal\..\misc\lv_types.h \
  ..\lvgl\src\hal\..\misc\..\draw\lv_img_cache.h \
  ..\lvgl\src\hal\..\misc\..\draw\lv_draw_mask.h \
  ..\lvgl\src\hal\..\misc\..\core\lv_obj_pos.h \
  ..\lvgl\src\hal\..\misc\lv_assert.h ..\lvgl\src\hal\..\core\lv_obj.h \
  ..\lvgl\src\hal\..\core\..\lv_conf_internal.h \
  ..\lvgl\src\hal\..\core\..\misc\lv_style.h \
  ..\lvgl\src\hal\..\core\..\misc\lv_types.h \
  ..\lvgl\src\hal\..\core\..\misc\lv_area.h \
  ..\lvgl\src\hal\..\core\..\misc\lv_color.h \
  ..\lvgl\src\hal\..\core\..\misc\lv_assert.h \
  ..\lvgl\src\hal\..\core\..\hal\lv_hal.h \
  ..\lvgl\src\hal\..\core\lv_obj_tree.h \
  ..\lvgl\src\hal\..\core\lv_obj_pos.h \
  ..\lvgl\src\hal\..\core\lv_obj_scroll.h \
  ..\lvgl\src\hal\..\core\..\misc\lv_anim.h \
  ..\lvgl\src\hal\..\core\lv_obj_style.h \
  ..\lvgl\src\hal\..\core\..\misc\lv_bidi.h \
  ..\lvgl\src\hal\..\core\lv_obj_style_gen.h \
  ..\lvgl\src\hal\..\core\lv_obj_draw.h \
  ..\lvgl\src\hal\..\core\..\draw\lv_draw.h \
  ..\lvgl\src\hal\..\core\lv_obj_class.h \
  ..\lvgl\src\hal\..\core\lv_event.h ..\lvgl\src\hal\..\core\lv_group.h \
  ..\lvgl\src\hal\..\core\..\misc\lv_ll.h \
  ..\lvgl\src\hal\..\core\lv_refr.h ..\lvgl\src\hal\..\core\lv_theme.h \
  ..\lvgl\src\hal\..\core\..\core\lv_obj.h \
  ..\lvgl\src\hal\..\draw\sdl\lv_draw_sdl.h \
  ..\lvgl\src\hal\..\draw\sdl\..\..\lv_conf_internal.h \
  ..\lvgl\src\hal\..\draw\sw\lv_draw_sw.h \
  ..\lvgl\src\hal\..\draw\sw\lv_draw_sw_blend.h \
  ..\lvgl\src\hal\..\draw\sw\..\..\misc\lv_area.h \
  ..\lvgl\src\hal\..\draw\sw\..\lv_draw_mask.h \
  ..\lvgl\src\hal\..\draw\sw\..\lv_draw.h \
  ..\lvgl\src\hal\..\draw\sw\..\..\hal\lv_hal_disp.h \
  ..\lvgl\src\hal\..\draw\stm32_dma2d\lv_gpu_stm32_dma2d.h \
  ..\lvgl\src\hal\..\draw\stm32_dma2d\..\..\misc\lv_color.h \
  ..\lvgl\src\hal\..\draw\stm32_dma2d\..\..\hal\lv_hal_disp.h \
  ..\lvgl\src\hal\..\draw\stm32_dma2d\..\sw\lv_draw_sw.h \
  ..\lvgl\src\hal\..\draw\swm341_dma2d\lv_gpu_swm341_dma2d.h \
  ..\lvgl\src\hal\..\draw\swm341_dma2d\..\..\misc\lv_color.h \
  ..\lvgl\src\hal\..\draw\swm341_dma2d\..\..\hal\lv_hal_disp.h \
  ..\lvgl\src\hal\..\draw\swm341_dma2d\..\sw\lv_draw_sw.h \
  ..\lvgl\src\hal\..\draw\arm2d\lv_gpu_arm2d.h \
  ..\lvgl\src\hal\..\draw\arm2d\..\..\misc\lv_color.h \
  ..\lvgl\src\hal\..\draw\arm2d\..\..\hal\lv_hal_disp.h \
  ..\lvgl\src\hal\..\draw\arm2d\..\sw\lv_draw_sw.h \
  ..\lvgl\src\hal\..\draw\nxp\vglite\lv_draw_vglite.h \
  ..\lvgl\src\hal\..\draw\nxp\vglite\..\..\..\lv_conf_internal.h \
  ..\lvgl\src\hal\..\draw\nxp\pxp\lv_draw_pxp.h \
  ..\lvgl\src\hal\..\draw\nxp\pxp\..\..\..\lv_conf_internal.h \
  ..\lvgl\src\hal\..\draw\renesas\lv_gpu_d2_ra6m3.h \
  ..\lvgl\src\hal\..\draw\renesas\..\..\misc\lv_color.h \
  ..\lvgl\src\hal\..\extra\themes\default\lv_theme_default.h \
  ..\lvgl\src\hal\..\extra\themes\default\..\..\..\core\lv_obj.h
