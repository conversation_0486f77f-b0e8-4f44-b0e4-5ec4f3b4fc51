/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2024 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "iwdg.h"
#include "rtc.h"
#include "spi.h"
#include "tim.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "lcd.h"
#include "lvgl.h"                // 它为整个LVGL提供了更完整的头文件引用
#include "lv_port_disp.h"        // LVGL的显示支持
#include "lv_port_indev.h"       // LVGL的触屏支持
#include "gui_guider.h"
#include "events_init.h"
#include "key_control.h"//按键处理
#include "rtc_clock_display.h"   // RTC时钟显示模块
#include "group_add.h"


/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */
uint16_t t3_count;
uint16_t t3_count2;
/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */
static uint16_t line_buffer[320];

lcd_io lcd_io_desc = {
    .spi = &hspi1,
    .rst = {LCD_RST_GPIO_Port, LCD_RST_Pin, 0},
    .bl  = {0, 0, 0},
    .cs  = {LCD_CS_GPIO_Port,  LCD_CS_Pin,  0},
    .dc  = {LCD_DC_GPIO_Port,  LCD_DC_Pin,  0},
    .te  = { /* TE */ }
};

lcd lcd_desc = {
    .io = &lcd_io_desc,
    .line_buffer = line_buffer,
};

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */
lv_ui guider_ui;
/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */
  int count = 0;
  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_SPI1_Init();
  MX_TIM3_Init();
  MX_IWDG_Init();
  MX_RTC_Init();
  /* USER CODE BEGIN 2 */
		__HAL_TIM_CLEAR_IT(&htim3,TIM_IT_UPDATE ); //清除标志
		HAL_TIM_Base_Start_IT(&htim3);             //打开定时器
	// 启动PWM
  HAL_TIM_PWM_Start(&htim3,TIM_CHANNEL_1);
  __HAL_TIM_SetCompare(&htim3, TIM_CHANNEL_1, 400); // 设置LCD背光亮度
  lcd_init_dev(&lcd_desc, LCD_2_00_INCH, LCD_ROTATE_270);
	lv_init();                             // LVGL 初始化
	lv_port_disp_init();                   // 注册LVGL的显示任务
  lv_port_indev_init();                  // 注册LVGL的触屏检测任务
		setup_ui(&guider_ui);
   	events_init(&guider_ui);
  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    //lcd_print(&lcd_desc, 0, 120, "> count:%d", t3_count2);
		lv_timer_handler();
		//检测当前界面
		screen_detector_update();
		// 处理挂起的按键事件（中断安全）
		key_process_pending_events();

		// 超简单的RTC时钟更新
		rtc_update_display();

    HAL_Delay(5);
		HAL_IWDG_Refresh(&hiwdg);
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_RCC_PWR_CLK_ENABLE();
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_LSI|RCC_OSCILLATORTYPE_HSE
                              |RCC_OSCILLATORTYPE_LSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
  RCC_OscInitStruct.LSEState = RCC_LSE_ON;
  RCC_OscInitStruct.LSIState = RCC_LSI_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLM = 4;
  RCC_OscInitStruct.PLL.PLLN = 168;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
  RCC_OscInitStruct.PLL.PLLQ = 4;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_5) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

//定时器开启的时候记得设置CUBE里面的NVIC
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *hitm)
{
	if(hitm ->Instance ==TIM3)//定时器3,定时时间1ms
	{
		lv_tick_inc(1);
		t3_count++;
		if(t3_count>=5)//1s
		{
			t3_count=0;
			t3_count2++;
			if(t3_count2>=10)//10s
			{
				t3_count2=0;
			}
		}
	}

}

/**
 * @brief GPIO外部中断回调函数
 * @param GPIO_Pin 触发中断的GPIO引脚
 */
void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin)
{
    key_type_t key = KEY_MAX;
    
    /* 根据GPIO引脚确定按键类型 */
    switch (GPIO_Pin) {
        case KEY_UP_Pin:      /* PE0 - 上键 */
            key = KEY_UP;
				//__HAL_TIM_SetCompare(&htim3, TIM_CHANNEL_1, 500); // 设置LCD背光亮度
            break;
        case KEY_Left_Pin:    /* PE1 - 左键 */
            key = KEY_LEFT;
				//__HAL_TIM_SetCompare(&htim3, TIM_CHANNEL_1, 100); // 设置LCD背光亮度
            break;
        case KEY_OK_Pin:      /* PE2 - 确认键 */
            key = KEY_OK;
				//__HAL_TIM_SetCompare(&htim3, TIM_CHANNEL_1, 900); // 设置LCD背光亮度
            break;
        case KEY_Right_Pin:   /* PE3 - 右键 */
            key = KEY_RIGHT;
				//__HAL_TIM_SetCompare(&htim3, TIM_CHANNEL_1, 500); // 设置LCD背光亮度
            break;
        case KEY_Return_Pin:  /* PE4 - 返回键 */
            key = KEY_BACK;
				//__HAL_TIM_SetCompare(&htim3, TIM_CHANNEL_1, 100); // 设置LCD背光亮度
            break;
        case KEY_Down_Pin:    /* PE5 - 下键 */
            key = KEY_DOWN;
				//__HAL_TIM_SetCompare(&htim3, TIM_CHANNEL_1, 900); // 设置LCD背光亮度
            break;
        case KEY_Power_Pin:   /* PE6 - 开机键 */
            key = KEY_POWER;
				//__HAL_TIM_SetCompare(&htim3, TIM_CHANNEL_1, 300); // 设置LCD背光亮度
            break;
        default:
            return;
    }
    
    /* 调用按键处理函数 */
    if (key < KEY_MAX) {
        key_interrupt_handler(key);
    }
}

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
