spi_lcd_320x240_2.0inch/lv_draw_sw_img.o: \
  ..\lvgl\src\draw\sw\lv_draw_sw_img.c ..\lvgl\src\draw\sw\lv_draw_sw.h \
  ..\lvgl\src\draw\sw\lv_draw_sw_blend.h \
  ..\lvgl\src\draw\sw\..\..\misc\lv_color.h \
  ..\lvgl\src\draw\sw\..\..\misc\..\lv_conf_internal.h \
  ..\lvgl\src\draw\sw\..\..\misc\..\lv_conf_kconfig.h ..\lvgl\lv_conf.h \
  ..\lvgl\src\draw\sw\..\..\misc\lv_assert.h \
  ..\lvgl\src\draw\sw\..\..\misc\lv_log.h \
  ..\lvgl\src\draw\sw\..\..\misc\lv_types.h \
  ..\lvgl\src\draw\sw\..\..\misc\lv_mem.h \
  ..\lvgl\src\draw\sw\..\..\misc\lv_math.h \
  ..\lvgl\src\draw\sw\..\..\misc\lv_area.h \
  ..\lvgl\src\draw\sw\..\..\misc\lv_style.h \
  ..\lvgl\src\draw\sw\..\..\misc\..\font\lv_font.h \
  ..\lvgl\src\draw\sw\..\..\misc\..\font\..\lv_conf_internal.h \
  ..\lvgl\src\draw\sw\..\..\misc\..\font\lv_symbol_def.h \
  ..\lvgl\src\draw\sw\..\..\misc\..\font\..\misc\lv_area.h \
  ..\lvgl\src\draw\sw\..\..\misc\lv_anim.h \
  ..\lvgl\src\draw\sw\..\..\misc\lv_txt.h \
  ..\lvgl\src\draw\sw\..\..\misc\lv_printf.h \
  ..\lvgl\src\draw\sw\..\..\misc\lv_bidi.h \
  ..\lvgl\src\draw\sw\..\..\misc\lv_style_gen.h \
  ..\lvgl\src\draw\sw\..\lv_draw_mask.h ..\lvgl\src\draw\sw\..\lv_draw.h \
  ..\lvgl\src\draw\sw\..\..\lv_conf_internal.h \
  ..\lvgl\src\draw\sw\..\lv_img_decoder.h \
  ..\lvgl\src\draw\sw\..\lv_img_buf.h \
  ..\lvgl\src\draw\sw\..\..\misc\lv_fs.h \
  ..\lvgl\src\draw\sw\..\lv_img_cache.h \
  ..\lvgl\src\draw\sw\..\lv_draw_rect.h \
  ..\lvgl\src\draw\sw\..\sw\lv_draw_sw_gradient.h \
  ..\lvgl\src\draw\sw\..\sw\..\..\misc\lv_color.h \
  ..\lvgl\src\draw\sw\..\sw\..\..\misc\lv_style.h \
  ..\lvgl\src\draw\sw\..\sw\lv_draw_sw_dither.h \
  ..\lvgl\src\draw\sw\..\sw\..\..\core\lv_obj_pos.h \
  ..\lvgl\src\draw\sw\..\sw\..\..\core\..\misc\lv_area.h \
  ..\lvgl\src\draw\sw\..\lv_draw_label.h \
  ..\lvgl\src\draw\sw\..\lv_draw_img.h \
  ..\lvgl\src\draw\sw\..\lv_draw_line.h \
  ..\lvgl\src\draw\sw\..\lv_draw_triangle.h \
  ..\lvgl\src\draw\sw\..\lv_draw_arc.h \
  ..\lvgl\src\draw\sw\..\lv_draw_transform.h \
  ..\lvgl\src\draw\sw\..\lv_draw_layer.h \
  ..\lvgl\src\draw\sw\..\..\hal\lv_hal_disp.h \
  ..\lvgl\src\draw\sw\..\..\hal\lv_hal.h \
  ..\lvgl\src\draw\sw\..\..\hal\lv_hal_indev.h \
  ..\lvgl\src\draw\sw\..\..\hal\..\lv_conf_internal.h \
  ..\lvgl\src\draw\sw\..\..\hal\..\misc\lv_area.h \
  ..\lvgl\src\draw\sw\..\..\hal\..\misc\lv_timer.h \
  ..\lvgl\src\draw\sw\..\..\hal\..\misc\..\lv_conf_internal.h \
  ..\lvgl\src\draw\sw\..\..\hal\..\misc\..\hal\lv_hal_tick.h \
  ..\lvgl\src\draw\sw\..\..\hal\..\misc\..\hal\..\lv_conf_internal.h \
  ..\lvgl\src\draw\sw\..\..\hal\lv_hal_tick.h \
  ..\lvgl\src\draw\sw\..\..\hal\..\draw\lv_draw.h \
  ..\lvgl\src\draw\sw\..\..\hal\..\misc\lv_color.h \
  ..\lvgl\src\draw\sw\..\..\hal\..\misc\lv_ll.h \
  ..\lvgl\src\draw\sw\..\..\core\lv_refr.h \
  ..\lvgl\src\draw\sw\..\..\core\lv_obj.h \
  ..\lvgl\src\draw\sw\..\..\core\..\lv_conf_internal.h \
  ..\lvgl\src\draw\sw\..\..\core\..\misc\lv_style.h \
  ..\lvgl\src\draw\sw\..\..\core\..\misc\lv_types.h \
  ..\lvgl\src\draw\sw\..\..\core\..\misc\lv_area.h \
  ..\lvgl\src\draw\sw\..\..\core\..\misc\lv_color.h \
  ..\lvgl\src\draw\sw\..\..\core\..\misc\lv_assert.h \
  ..\lvgl\src\draw\sw\..\..\core\..\hal\lv_hal.h \
  ..\lvgl\src\draw\sw\..\..\core\lv_obj_tree.h \
  ..\lvgl\src\draw\sw\..\..\core\lv_obj_pos.h \
  ..\lvgl\src\draw\sw\..\..\core\lv_obj_scroll.h \
  ..\lvgl\src\draw\sw\..\..\core\..\misc\lv_anim.h \
  ..\lvgl\src\draw\sw\..\..\core\lv_obj_style.h \
  ..\lvgl\src\draw\sw\..\..\core\..\misc\lv_bidi.h \
  ..\lvgl\src\draw\sw\..\..\core\lv_obj_style_gen.h \
  ..\lvgl\src\draw\sw\..\..\core\lv_obj_draw.h \
  ..\lvgl\src\draw\sw\..\..\core\..\draw\lv_draw.h \
  ..\lvgl\src\draw\sw\..\..\core\lv_obj_class.h \
  ..\lvgl\src\draw\sw\..\..\core\lv_event.h \
  ..\lvgl\src\draw\sw\..\..\core\lv_group.h \
  ..\lvgl\src\draw\sw\..\..\core\..\misc\lv_ll.h
