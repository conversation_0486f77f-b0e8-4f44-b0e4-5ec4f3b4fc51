#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
File.Version=6
GPIO.groupedBy=Group By Peripherals
IWDG.IPParameters=Prescaler
IWDG.Prescaler=IWDG_PRESCALER_64
KeepUserPlacement=false
Mcu.CPN=STM32F407VET6
Mcu.Family=STM32F4
Mcu.IP0=IWDG
Mcu.IP1=NVIC
Mcu.IP2=RCC
Mcu.IP3=RTC
Mcu.IP4=SPI1
Mcu.IP5=SYS
Mcu.IP6=TIM3
Mcu.IPNb=7
Mcu.Name=STM32F407V(E-G)Tx
Mcu.Package=LQFP100
Mcu.Pin0=PE2
Mcu.Pin1=PE3
Mcu.Pin10=PA6
Mcu.Pin11=PA7
Mcu.Pin12=PB0
Mcu.Pin13=PE7
Mcu.Pin14=PE8
Mcu.Pin15=PE9
Mcu.Pin16=PE10
Mcu.Pin17=PD12
Mcu.Pin18=PD13
Mcu.Pin19=PD14
Mcu.Pin2=PE4
Mcu.Pin20=PC7
Mcu.Pin21=PA13
Mcu.Pin22=PA14
Mcu.Pin23=PB3
Mcu.Pin24=PB4
Mcu.Pin25=PB5
Mcu.Pin26=PE0
Mcu.Pin27=PE1
Mcu.Pin28=VP_IWDG_VS_IWDG
Mcu.Pin29=VP_RTC_VS_RTC_Activate
Mcu.Pin3=PE5
Mcu.Pin30=VP_RTC_VS_RTC_Calendar
Mcu.Pin31=VP_SYS_VS_Systick
Mcu.Pin32=VP_TIM3_VS_ClockSourceINT
Mcu.Pin4=PE6
Mcu.Pin5=PC14-OSC32_IN
Mcu.Pin6=PC15-OSC32_OUT
Mcu.Pin7=PH0-OSC_IN
Mcu.Pin8=PH1-OSC_OUT
Mcu.Pin9=PA5
Mcu.PinsNb=33
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F407VETx
MxCube.Version=6.14.1
MxDb.Version=DB.6.0.141
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.EXTI0_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.EXTI1_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.EXTI2_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.EXTI3_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.EXTI4_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.EXTI9_5_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.TIM3_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA5.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PA5.GPIO_Label=LCD_SCK
PA5.GPIO_PuPd=GPIO_PULLUP
PA5.GPIO_Speed=GPIO_SPEED_FREQ_MEDIUM
PA5.Locked=true
PA5.Mode=Simplex_Bidirectional_Master
PA5.Signal=SPI1_SCK
PA6.GPIOParameters=GPIO_Label
PA6.GPIO_Label=LCD_DC
PA6.Locked=true
PA6.Signal=GPIO_Output
PA7.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PA7.GPIO_Label=LCD_SDA
PA7.GPIO_PuPd=GPIO_PULLUP
PA7.GPIO_Speed=GPIO_SPEED_FREQ_MEDIUM
PA7.Locked=true
PA7.Mode=Simplex_Bidirectional_Master
PA7.Signal=SPI1_MOSI
PB0.GPIOParameters=GPIO_Label
PB0.GPIO_Label=Motor_switch
PB0.Locked=true
PB0.Signal=GPIO_Output
PB3.GPIOParameters=GPIO_Label
PB3.GPIO_Label=LCD_RST
PB3.Locked=true
PB3.Signal=GPIO_Output
PB4.GPIOParameters=GPIO_PuPd,GPIO_Label
PB4.GPIO_Label=LCD_BLK
PB4.GPIO_PuPd=GPIO_NOPULL
PB4.Locked=true
PB4.Signal=S_TIM3_CH1
PB5.GPIOParameters=GPIO_Label
PB5.GPIO_Label=LCD_CS
PB5.Locked=true
PB5.Signal=GPIO_Output
PC14-OSC32_IN.Mode=LSE-External-Oscillator
PC14-OSC32_IN.Signal=RCC_OSC32_IN
PC15-OSC32_OUT.Mode=LSE-External-Oscillator
PC15-OSC32_OUT.Signal=RCC_OSC32_OUT
PC7.GPIOParameters=GPIO_PuPd,GPIO_Label
PC7.GPIO_Label=Pump_switch
PC7.GPIO_PuPd=GPIO_NOPULL
PC7.Signal=S_TIM3_CH2
PD12.GPIOParameters=GPIO_Label
PD12.GPIO_Label=RGB_G
PD12.Locked=true
PD12.Signal=GPIO_Output
PD13.GPIOParameters=GPIO_Label
PD13.GPIO_Label=RGB_Y
PD13.Locked=true
PD13.Signal=GPIO_Output
PD14.GPIOParameters=GPIO_Label
PD14.GPIO_Label=RGB_R
PD14.Locked=true
PD14.Signal=GPIO_Output
PE0.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultEXTI
PE0.GPIO_Label=KEY_UP
PE0.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_RISING_FALLING
PE0.GPIO_PuPd=GPIO_PULLUP
PE0.Locked=true
PE0.Signal=GPXTI0
PE1.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultEXTI
PE1.GPIO_Label=KEY_Right
PE1.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_RISING_FALLING
PE1.GPIO_PuPd=GPIO_PULLUP
PE1.Locked=true
PE1.Signal=GPXTI1
PE10.GPIOParameters=GPIO_PuPd,GPIO_Label
PE10.GPIO_Label=GPIO_Charge
PE10.GPIO_PuPd=GPIO_PULLUP
PE10.Locked=true
PE10.Signal=GPIO_Input
PE2.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultEXTI
PE2.GPIO_Label=KEY_OK
PE2.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_RISING_FALLING
PE2.GPIO_PuPd=GPIO_PULLUP
PE2.Locked=true
PE2.Signal=GPXTI2
PE3.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultEXTI
PE3.GPIO_Label=KEY_Left
PE3.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_RISING_FALLING
PE3.GPIO_PuPd=GPIO_PULLUP
PE3.Locked=true
PE3.Signal=GPXTI3
PE4.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultEXTI
PE4.GPIO_Label=KEY_Down
PE4.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_RISING_FALLING
PE4.GPIO_PuPd=GPIO_PULLUP
PE4.Locked=true
PE4.Signal=GPXTI4
PE5.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultEXTI
PE5.GPIO_Label=KEY_Power
PE5.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_RISING_FALLING
PE5.GPIO_PuPd=GPIO_PULLUP
PE5.Locked=true
PE5.Signal=GPXTI5
PE6.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultEXTI
PE6.GPIO_Label=KEY_Return
PE6.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_RISING_FALLING
PE6.GPIO_PuPd=GPIO_PULLUP
PE6.Locked=true
PE6.Signal=GPXTI6
PE7.GPIOParameters=GPIO_Label
PE7.GPIO_Label=Voice_out
PE7.Locked=true
PE7.Signal=GPIO_Output
PE8.GPIOParameters=GPIO_PuPd,GPIO_Label
PE8.GPIO_Label=Voice_CLK
PE8.GPIO_PuPd=GPIO_PULLUP
PE8.Locked=true
PE8.Signal=GPIO_Input
PE9.GPIOParameters=GPIO_Label
PE9.GPIO_Label=Power_out
PE9.Locked=true
PE9.Signal=GPIO_Output
PH0-OSC_IN.Mode=HSE-External-Oscillator
PH0-OSC_IN.Signal=RCC_OSC_IN
PH1-OSC_OUT.Mode=HSE-External-Oscillator
PH1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F407VETx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.28.2
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x4000
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=SPI_LCD_320x240_2.0inch.ioc
ProjectManager.ProjectName=SPI_LCD_320x240_2.0inch
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x2000
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_SPI1_Init-SPI1-false-HAL-true,4-MX_TIM3_Init-TIM3-false-HAL-true,5-MX_IWDG_Init-IWDG-false-HAL-true,6-MX_RTC_Init-RTC-false-HAL-true
RCC.48MHZClocksFreq_Value=84000000
RCC.AHBFreq_Value=168000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=42000000
RCC.APB1TimFreq_Value=84000000
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=84000000
RCC.APB2TimFreq_Value=168000000
RCC.CortexFreq_Value=168000000
RCC.EthernetFreq_Value=168000000
RCC.FCLKCortexFreq_Value=168000000
RCC.FLatency=FLASH_LATENCY_5
RCC.FamilyName=M
RCC.HCLKFreq_Value=168000000
RCC.HSE_VALUE=8000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=192000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FLatency,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQCLKFreq_Value,PLLSourceVirtual,RCC_RTC_Clock_Source,RTCFreq_Value,RTCHSEDivFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VcooutputI2S
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=168000000
RCC.PLLCLKFreq_Value=168000000
RCC.PLLM=4
RCC.PLLN=168
RCC.PLLQCLKFreq_Value=84000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.RCC_RTC_Clock_Source=RCC_RTCCLKSOURCE_LSE
RCC.RTCFreq_Value=32768
RCC.RTCHSEDivFreq_Value=4000000
RCC.SYSCLKFreq_VALUE=168000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=384000000
RCC.VCOInputFreq_Value=2000000
RCC.VCOOutputFreq_Value=336000000
RCC.VcooutputI2S=192000000
RTC.Hours=11
RTC.IPParameters=Hours,Minutes
RTC.Minutes=32
SH.GPXTI0.0=GPIO_EXTI0
SH.GPXTI0.ConfNb=1
SH.GPXTI1.0=GPIO_EXTI1
SH.GPXTI1.ConfNb=1
SH.GPXTI2.0=GPIO_EXTI2
SH.GPXTI2.ConfNb=1
SH.GPXTI3.0=GPIO_EXTI3
SH.GPXTI3.ConfNb=1
SH.GPXTI4.0=GPIO_EXTI4
SH.GPXTI4.ConfNb=1
SH.GPXTI5.0=GPIO_EXTI5
SH.GPXTI5.ConfNb=1
SH.GPXTI6.0=GPIO_EXTI6
SH.GPXTI6.ConfNb=1
SH.S_TIM3_CH1.0=TIM3_CH1,PWM Generation1 CH1
SH.S_TIM3_CH1.ConfNb=1
SH.S_TIM3_CH2.0=TIM3_CH2,PWM Generation2 CH2
SH.S_TIM3_CH2.ConfNb=1
SPI1.BaudRatePrescaler=SPI_BAUDRATEPRESCALER_4
SPI1.CLKPhase=SPI_PHASE_2EDGE
SPI1.CLKPolarity=SPI_POLARITY_HIGH
SPI1.CRCCalculation=SPI_CRCCALCULATION_DISABLE
SPI1.CalculateBaudRate=21.0 MBits/s
SPI1.DataSize=SPI_DATASIZE_8BIT
SPI1.Direction=SPI_DIRECTION_1LINE
SPI1.FirstBit=SPI_FIRSTBIT_MSB
SPI1.IPParameters=TIMode,DataSize,FirstBit,BaudRatePrescaler,CLKPolarity,CLKPhase,CRCCalculation,NSS,VirtualType,Mode,Direction,CalculateBaudRate
SPI1.Mode=SPI_MODE_MASTER
SPI1.NSS=SPI_NSS_SOFT
SPI1.TIMode=SPI_TIMODE_DISABLE
SPI1.VirtualType=VM_MASTER
TIM3.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM3.Channel-PWM\ Generation1\ CH1=TIM_CHANNEL_1
TIM3.Channel-PWM\ Generation2\ CH2=TIM_CHANNEL_2
TIM3.IPParameters=Prescaler,Period,AutoReloadPreload,Channel-PWM Generation1 CH1,Channel-PWM Generation2 CH2
TIM3.Period=999
TIM3.Prescaler=83
VP_IWDG_VS_IWDG.Mode=IWDG_Activate
VP_IWDG_VS_IWDG.Signal=IWDG_VS_IWDG
VP_RTC_VS_RTC_Activate.Mode=RTC_Enabled
VP_RTC_VS_RTC_Activate.Signal=RTC_VS_RTC_Activate
VP_RTC_VS_RTC_Calendar.Mode=RTC_Calendar
VP_RTC_VS_RTC_Calendar.Signal=RTC_VS_RTC_Calendar
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM3_VS_ClockSourceINT.Mode=Internal
VP_TIM3_VS_ClockSourceINT.Signal=TIM3_VS_ClockSourceINT
board=custom
