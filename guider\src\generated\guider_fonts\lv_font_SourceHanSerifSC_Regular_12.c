/*
*This Font Software is licensed under the SIL Open Font License, Version 1.1. 
*This license is available with a FAQ at: http://scripts.sil.org/OFL
*/
/*******************************************************************************
 * Size: 12 px
 * Bpp: 4
 * Opts: undefined
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_SOURCEHANSERIFSC_REGULAR_12
#define LV_FONT_SOURCEHANSERIFSC_REGULAR_12 1
#endif

#if LV_FONT_SOURCEHANSERIFSC_REGULAR_12

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xa5, 0xb6, 0xa5, 0x83, 0x72, 0x61, 0x50, 0x0,
    0x52, 0xa6,

    /* U+0022 "\"" */
    0x49, 0x2c, 0x5a, 0x2d, 0x38, 0xb, 0x16, 0x8,
    0x1, 0x1,

    /* U+0023 "#" */
    0x0, 0x60, 0x6, 0x0, 0x6, 0x0, 0x60, 0x0,
    0x60, 0x15, 0x3, 0xde, 0xde, 0xe8, 0x0, 0x50,
    0x51, 0x0, 0x23, 0x6, 0x0, 0x9e, 0xdd, 0xed,
    0x20, 0x60, 0x6, 0x0, 0x6, 0x0, 0x60, 0x0,
    0x60, 0x6, 0x0,

    /* U+0024 "$" */
    0x0, 0x6, 0x0, 0x3, 0x9a, 0x81, 0xc, 0x6,
    0x49, 0xd, 0x6, 0x4, 0xd, 0x56, 0x0, 0x3,
    0xec, 0x20, 0x0, 0x1c, 0xf4, 0x0, 0x6, 0x4c,
    0x14, 0x6, 0xd, 0x1c, 0x6, 0x2a, 0x5, 0x7a,
    0x91, 0x0, 0x6, 0x0, 0x0, 0x3, 0x0,

    /* U+0025 "%" */
    0x8, 0x65, 0x0, 0x0, 0x50, 0x4, 0x70, 0xb0,
    0x0, 0x61, 0x0, 0x65, 0xb, 0x0, 0x15, 0x0,
    0x6, 0x50, 0xb0, 0x7, 0x0, 0x0, 0x28, 0xa,
    0x4, 0x21, 0x65, 0x0, 0x45, 0x10, 0x60, 0xa0,
    0x73, 0x0, 0x0, 0x60, 0xb, 0x4, 0x70, 0x0,
    0x34, 0x0, 0xc0, 0x37, 0x0, 0x7, 0x0, 0xb,
    0x5, 0x50, 0x5, 0x10, 0x0, 0x46, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0026 "&" */
    0x0, 0x97, 0x90, 0x0, 0x0, 0x66, 0x5, 0x60,
    0x0, 0x6, 0x70, 0x75, 0x0, 0x0, 0x1d, 0x48,
    0x2, 0x33, 0x0, 0xac, 0x0, 0x19, 0x20, 0xa2,
    0xa8, 0x0, 0x70, 0x59, 0x0, 0xd5, 0x33, 0x7,
    0x70, 0x2, 0xe7, 0x0, 0x3d, 0x0, 0x8, 0xe1,
    0x0, 0x6c, 0x98, 0x16, 0xc6,

    /* U+0027 "'" */
    0x49, 0x5a, 0x38, 0x16, 0x1,

    /* U+0028 "(" */
    0x0, 0x40, 0x3, 0x50, 0xa, 0x0, 0x37, 0x0,
    0x83, 0x0, 0xc0, 0x0, 0xd0, 0x0, 0xd0, 0x0,
    0xc0, 0x0, 0x93, 0x0, 0x47, 0x0, 0xa, 0x0,
    0x3, 0x50, 0x0, 0x40,

    /* U+0029 ")" */
    0x40, 0x0, 0x18, 0x0, 0x7, 0x20, 0x1, 0x90,
    0x0, 0xc0, 0x0, 0xa2, 0x0, 0x93, 0x0, 0x93,
    0x0, 0xa2, 0x0, 0xc0, 0x1, 0xa0, 0x7, 0x20,
    0x18, 0x0, 0x40, 0x0,

    /* U+002A "*" */
    0x0, 0x63, 0x0, 0x10, 0x62, 0x11, 0x7b, 0x97,
    0xc3, 0x0, 0x76, 0x0, 0x9, 0x48, 0x50, 0x5,
    0x1, 0x40,

    /* U+002B "+" */
    0x0, 0x6, 0x0, 0x0, 0x0, 0x80, 0x0, 0x0,
    0x8, 0x0, 0x4, 0x88, 0xc8, 0x84, 0x0, 0x8,
    0x0, 0x0, 0x0, 0x80, 0x0, 0x0, 0x6, 0x0,
    0x0,

    /* U+002C "," */
    0x9, 0x80, 0x48, 0x4, 0x31, 0x30,

    /* U+002D "-" */
    0x5a, 0xa6,

    /* U+002E "." */
    0x54, 0x98,

    /* U+002F "/" */
    0x0, 0x8, 0x0, 0x0, 0x80, 0x0, 0x16, 0x0,
    0x6, 0x20, 0x0, 0x80, 0x0, 0x8, 0x0, 0x3,
    0x40, 0x0, 0x70, 0x0, 0x8, 0x0, 0x1, 0x70,
    0x0, 0x53, 0x0, 0x8, 0x0, 0x0, 0x40, 0x0,
    0x0,

    /* U+0030 "0" */
    0x1, 0x97, 0x80, 0x0, 0xb2, 0x7, 0x60, 0x1c,
    0x0, 0x2c, 0x4, 0xa0, 0x0, 0xf0, 0x69, 0x0,
    0xe, 0x6, 0x90, 0x0, 0xe0, 0x4a, 0x0, 0xf,
    0x1, 0xc0, 0x2, 0xc0, 0xb, 0x20, 0x76, 0x0,
    0x18, 0x68, 0x0,

    /* U+0031 "1" */
    0x3, 0x76, 0x0, 0x4, 0x89, 0x0, 0x0, 0x68,
    0x0, 0x0, 0x68, 0x0, 0x0, 0x68, 0x0, 0x0,
    0x68, 0x0, 0x0, 0x78, 0x0, 0x0, 0x78, 0x0,
    0x0, 0x79, 0x0, 0x7, 0xbc, 0x70,

    /* U+0032 "2" */
    0x4, 0x67, 0xa1, 0x1, 0xe0, 0x8, 0x80, 0x4,
    0x0, 0x5b, 0x0, 0x0, 0x7, 0x90, 0x0, 0x0,
    0xc2, 0x0, 0x0, 0x66, 0x0, 0x0, 0x28, 0x0,
    0x0, 0x8, 0x0, 0x0, 0x7, 0x0, 0x0, 0x3,
    0xfe, 0xee, 0xe1,

    /* U+0033 "3" */
    0x6, 0x58, 0xa0, 0xd, 0x0, 0x98, 0x0, 0x0,
    0x69, 0x0, 0x0, 0xa3, 0x0, 0x6b, 0x40, 0x0,
    0x0, 0x96, 0x0, 0x0, 0x3d, 0x12, 0x0, 0x2d,
    0x3a, 0x0, 0x79, 0x5, 0x48, 0x90,

    /* U+0034 "4" */
    0x0, 0x1, 0xe0, 0x0, 0x0, 0x6e, 0x0, 0x0,
    0x32, 0xe0, 0x0, 0x6, 0xe, 0x0, 0x6, 0x0,
    0xe0, 0x2, 0x40, 0xe, 0x0, 0x7b, 0xbb, 0xfb,
    0x40, 0x0, 0xe, 0x0, 0x0, 0x0, 0xe0, 0x0,
    0x0, 0xe, 0x0,

    /* U+0035 "5" */
    0x8, 0xee, 0xeb, 0x0, 0x70, 0x0, 0x0, 0x7,
    0x0, 0x0, 0x0, 0x70, 0x0, 0x0, 0x9, 0x78,
    0x70, 0x0, 0x0, 0x8, 0x90, 0x0, 0x0, 0x1f,
    0x0, 0x0, 0x1, 0xf0, 0x39, 0x0, 0x5a, 0x0,
    0x65, 0x79, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x54, 0x0, 0x2, 0xa1, 0x0, 0x1,
    0xc0, 0x0, 0x0, 0xa4, 0x0, 0x0, 0xe, 0x58,
    0x91, 0x3, 0xd1, 0x4, 0xb0, 0x4b, 0x0, 0xf,
    0x2, 0xd0, 0x0, 0xe0, 0xc, 0x20, 0x2b, 0x0,
    0x29, 0x69, 0x10,

    /* U+0037 "7" */
    0x5e, 0xee, 0xec, 0x0, 0x0, 0x7, 0x0, 0x0,
    0x53, 0x0, 0x0, 0x90, 0x0, 0x2, 0x80, 0x0,
    0x8, 0x30, 0x0, 0xc, 0x0, 0x0, 0x48, 0x0,
    0x0, 0xb3, 0x0, 0x1, 0xd0, 0x0,

    /* U+0038 "8" */
    0x3, 0x97, 0x91, 0x0, 0xd1, 0x5, 0x80, 0xe,
    0x0, 0x39, 0x0, 0xc5, 0x8, 0x30, 0x3, 0xeb,
    0x50, 0x0, 0x17, 0x8e, 0x30, 0xc, 0x0, 0x6d,
    0x4, 0xa0, 0x0, 0xf0, 0x2d, 0x0, 0x2c, 0x0,
    0x68, 0x68, 0x20,

    /* U+0039 "9" */
    0x4, 0x87, 0x90, 0x1, 0xd0, 0x5, 0x80, 0x4a,
    0x0, 0xe, 0x4, 0xb0, 0x0, 0xf0, 0xd, 0x20,
    0x2e, 0x0, 0x29, 0x77, 0xc0, 0x0, 0x0, 0x86,
    0x0, 0x0, 0x2b, 0x0, 0x0, 0x3a, 0x0, 0x0,
    0x54, 0x0, 0x0,

    /* U+003A ":" */
    0x98, 0x54, 0x0, 0x0, 0x0, 0x54, 0x98,

    /* U+003B ";" */
    0x9, 0x80, 0x54, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x87, 0x5, 0x90, 0x34, 0x14, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x90, 0x0,
    0x19, 0x40, 0x0, 0x67, 0x0, 0x0, 0x2a, 0x10,
    0x0, 0x0, 0x8, 0x60, 0x0, 0x0, 0x3, 0x92,
    0x0, 0x0, 0x0, 0x70,

    /* U+003D "=" */
    0x48, 0x88, 0x88, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0x88, 0x88, 0x84,

    /* U+003E ">" */
    0x0, 0x0, 0x0, 0x0, 0x94, 0x0, 0x0, 0x0,
    0x59, 0x10, 0x0, 0x0, 0x8, 0x60, 0x0, 0x0,
    0x1a, 0x20, 0x0, 0x68, 0x0, 0x3, 0x93, 0x0,
    0x0, 0x70, 0x0, 0x0,

    /* U+003F "?" */
    0x0, 0x0, 0x0, 0xeb, 0x20, 0x2, 0x68, 0x10,
    0x0, 0x16, 0x0, 0x5, 0x50, 0x9, 0xb0, 0x7,
    0x70, 0x0, 0x51, 0x0, 0x0, 0x0, 0x0, 0x43,
    0x0, 0x9, 0x70, 0x0,

    /* U+0040 "@" */
    0x0, 0x2, 0x77, 0x67, 0x40, 0x0, 0x4, 0x60,
    0x0, 0x4, 0x50, 0x2, 0x50, 0x0, 0x0, 0x6,
    0x0, 0x80, 0x3, 0x88, 0x80, 0x23, 0x26, 0x2,
    0x90, 0x39, 0x0, 0x64, 0x30, 0x92, 0x6, 0x60,
    0x14, 0x71, 0xc, 0x0, 0x93, 0x5, 0x15, 0x20,
    0xb3, 0x2c, 0x11, 0x60, 0x35, 0x3, 0x94, 0x37,
    0x50, 0x0, 0x90, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x91, 0x0, 0x2, 0x0, 0x0, 0x0, 0x66, 0x65,
    0x10, 0x0,

    /* U+0041 "A" */
    0x0, 0x3, 0xc0, 0x0, 0x0, 0x0, 0x7f, 0x10,
    0x0, 0x0, 0x7, 0xb5, 0x0, 0x0, 0x1, 0x66,
    0xa0, 0x0, 0x0, 0x61, 0x2e, 0x0, 0x0, 0x8,
    0x0, 0xd3, 0x0, 0x0, 0xa6, 0x6b, 0x80, 0x0,
    0x43, 0x0, 0x3d, 0x0, 0x7, 0x0, 0x0, 0xe2,
    0x6, 0xc5, 0x0, 0x6d, 0xa3,

    /* U+0042 "B" */
    0x28, 0xf7, 0x7a, 0x30, 0x1, 0xf0, 0x2, 0xd0,
    0x1, 0xf0, 0x0, 0xf0, 0x1, 0xf0, 0x6, 0x80,
    0x1, 0xf6, 0x99, 0x0, 0x1, 0xe0, 0x3, 0xd0,
    0x1, 0xe0, 0x0, 0xa6, 0x1, 0xf0, 0x0, 0x97,
    0x1, 0xf0, 0x0, 0xd2, 0x28, 0xf6, 0x79, 0x40,

    /* U+0043 "C" */
    0x0, 0x19, 0x87, 0x93, 0x2, 0xc2, 0x0, 0x38,
    0xb, 0x50, 0x0, 0x7, 0x1f, 0x0, 0x0, 0x0,
    0x3e, 0x0, 0x0, 0x0, 0x4d, 0x0, 0x0, 0x0,
    0x1f, 0x0, 0x0, 0x0, 0xc, 0x50, 0x0, 0x8,
    0x3, 0xc1, 0x0, 0x1a, 0x0, 0x29, 0x77, 0x94,

    /* U+0044 "D" */
    0x28, 0xf6, 0x78, 0x50, 0x0, 0x1f, 0x0, 0x9,
    0x70, 0x1, 0xf0, 0x0, 0x1e, 0x10, 0x1f, 0x0,
    0x0, 0xc5, 0x1, 0xf0, 0x0, 0xa, 0x70, 0x1f,
    0x0, 0x0, 0xa7, 0x1, 0xf0, 0x0, 0xc, 0x50,
    0x1f, 0x0, 0x1, 0xe1, 0x1, 0xf0, 0x0, 0xa6,
    0x2, 0x8f, 0x77, 0x84, 0x0,

    /* U+0045 "E" */
    0x28, 0xf7, 0x77, 0xd0, 0x1, 0xf0, 0x0, 0x90,
    0x1, 0xf0, 0x0, 0x61, 0x1, 0xf0, 0x1, 0x0,
    0x1, 0xf0, 0x7, 0x0, 0x1, 0xf6, 0x69, 0x0,
    0x1, 0xf0, 0x7, 0x0, 0x1, 0xf0, 0x0, 0x0,
    0x1, 0xf0, 0x0, 0x54, 0x28, 0xf7, 0x77, 0xb3,

    /* U+0046 "F" */
    0x28, 0xf7, 0x77, 0xd0, 0x1, 0xf0, 0x0, 0x91,
    0x1, 0xf0, 0x0, 0x51, 0x1, 0xf0, 0x1, 0x0,
    0x1, 0xf0, 0x7, 0x0, 0x1, 0xf6, 0x69, 0x0,
    0x1, 0xf0, 0x7, 0x0, 0x1, 0xf0, 0x0, 0x0,
    0x1, 0xf0, 0x0, 0x0, 0x28, 0xf6, 0x10, 0x0,

    /* U+0047 "G" */
    0x0, 0x19, 0x87, 0x94, 0x0, 0x2c, 0x20, 0x1,
    0xb0, 0xb, 0x50, 0x0, 0x8, 0x1, 0xf0, 0x0,
    0x0, 0x0, 0x4e, 0x0, 0x0, 0x0, 0x3, 0xd0,
    0x0, 0x48, 0xc5, 0x1f, 0x0, 0x0, 0x3d, 0x0,
    0xb5, 0x0, 0x2, 0xd0, 0x2, 0xc1, 0x0, 0x2d,
    0x0, 0x2, 0x97, 0x78, 0x50,

    /* U+0048 "H" */
    0x29, 0xf8, 0x10, 0x7e, 0xb4, 0x1, 0xf0, 0x0,
    0xb, 0x50, 0x1, 0xf0, 0x0, 0xb, 0x50, 0x1,
    0xf0, 0x0, 0xb, 0x50, 0x1, 0xf0, 0x0, 0xb,
    0x50, 0x1, 0xf6, 0x66, 0x6d, 0x50, 0x1, 0xf0,
    0x0, 0xb, 0x50, 0x1, 0xf0, 0x0, 0xb, 0x50,
    0x1, 0xf0, 0x0, 0xb, 0x50, 0x28, 0xf6, 0x10,
    0x6d, 0xa3,

    /* U+0049 "I" */
    0x28, 0xf7, 0x10, 0x1f, 0x0, 0x1, 0xf0, 0x0,
    0x1f, 0x0, 0x1, 0xf0, 0x0, 0x1f, 0x0, 0x1,
    0xf0, 0x0, 0x1f, 0x0, 0x1, 0xf0, 0x2, 0x8f,
    0x71,

    /* U+004A "J" */
    0x2, 0x7f, 0x82, 0x0, 0xe, 0x20, 0x0, 0xe,
    0x20, 0x0, 0xe, 0x20, 0x0, 0xe, 0x10, 0x0,
    0xe, 0x10, 0x0, 0xe, 0x10, 0x0, 0xe, 0x10,
    0x0, 0xf, 0x0, 0x0, 0x1c, 0x0, 0xe, 0xa3,
    0x0, 0x1, 0x0, 0x0,

    /* U+004B "K" */
    0x28, 0xf7, 0x13, 0xb9, 0x30, 0x1e, 0x0, 0x7,
    0x0, 0x1, 0xe0, 0x7, 0x0, 0x0, 0x1e, 0x5,
    0x30, 0x0, 0x1, 0xe1, 0xe4, 0x0, 0x0, 0x1e,
    0x75, 0xc0, 0x0, 0x1, 0xf1, 0xd, 0x40, 0x0,
    0x1e, 0x0, 0x5c, 0x0, 0x1, 0xe0, 0x0, 0xd4,
    0x2, 0x8f, 0x71, 0x3b, 0xd5,

    /* U+004C "L" */
    0x28, 0xf7, 0x10, 0x0, 0x1, 0xf0, 0x0, 0x0,
    0x1, 0xf0, 0x0, 0x0, 0x1, 0xf0, 0x0, 0x0,
    0x1, 0xf0, 0x0, 0x0, 0x1, 0xf0, 0x0, 0x0,
    0x1, 0xf0, 0x0, 0x0, 0x1, 0xf0, 0x0, 0x50,
    0x1, 0xf0, 0x0, 0xa0, 0x28, 0xf7, 0x77, 0xd0,

    /* U+004D "M" */
    0x3a, 0xf0, 0x0, 0x0, 0x7e, 0x70, 0x4, 0xd5,
    0x0, 0x0, 0xad, 0x0, 0x4, 0x8a, 0x0, 0x2,
    0x8d, 0x0, 0x4, 0x3f, 0x10, 0x6, 0x3d, 0x0,
    0x4, 0x2a, 0x60, 0x7, 0x3c, 0x0, 0x4, 0x25,
    0xc0, 0x34, 0x3c, 0x0, 0x4, 0x20, 0xe1, 0x70,
    0x3d, 0x0, 0x4, 0x20, 0x97, 0x70, 0x3d, 0x0,
    0x4, 0x20, 0x3f, 0x30, 0x3d, 0x0, 0x3a, 0x93,
    0xb, 0x3, 0x9e, 0x70,

    /* U+004E "N" */
    0x39, 0xe0, 0x0, 0x6b, 0x70, 0x4, 0xc8, 0x0,
    0x6, 0x0, 0x4, 0x4e, 0x20, 0x6, 0x0, 0x4,
    0x27, 0xb0, 0x6, 0x0, 0x4, 0x20, 0xd4, 0x6,
    0x0, 0x4, 0x20, 0x4d, 0x6, 0x0, 0x4, 0x20,
    0xb, 0x76, 0x0, 0x4, 0x20, 0x2, 0xe8, 0x0,
    0x4, 0x20, 0x0, 0x8e, 0x0, 0x3a, 0x94, 0x0,
    0xc, 0x0,

    /* U+004F "O" */
    0x0, 0x39, 0x78, 0x50, 0x0, 0x3b, 0x0, 0x9,
    0x60, 0xc, 0x40, 0x0, 0x1e, 0x1, 0xf0, 0x0,
    0x0, 0xc4, 0x4d, 0x0, 0x0, 0xa, 0x74, 0xd0,
    0x0, 0x0, 0xa7, 0x1f, 0x0, 0x0, 0xc, 0x40,
    0xc4, 0x0, 0x1, 0xe0, 0x4, 0xb0, 0x0, 0x96,
    0x0, 0x3, 0x87, 0x85, 0x0,

    /* U+0050 "P" */
    0x28, 0xf7, 0x79, 0x30, 0x1, 0xf0, 0x1, 0xd0,
    0x1, 0xf0, 0x0, 0xd3, 0x1, 0xf0, 0x0, 0xe2,
    0x1, 0xf0, 0x6, 0xa0, 0x1, 0xf7, 0x75, 0x0,
    0x1, 0xf0, 0x0, 0x0, 0x1, 0xf0, 0x0, 0x0,
    0x1, 0xf0, 0x0, 0x0, 0x29, 0xf8, 0x20, 0x0,

    /* U+0051 "Q" */
    0x0, 0x39, 0x78, 0x50, 0x0, 0x3b, 0x0, 0x9,
    0x60, 0xc, 0x40, 0x0, 0x1e, 0x1, 0xf0, 0x0,
    0x0, 0xc4, 0x3d, 0x0, 0x0, 0xa, 0x64, 0xd0,
    0x0, 0x0, 0xa7, 0x2f, 0x0, 0x0, 0xc, 0x40,
    0xd3, 0x0, 0x1, 0xe1, 0x4, 0xb0, 0x0, 0x87,
    0x0, 0x4, 0x97, 0x86, 0x0, 0x0, 0x0, 0xe2,
    0x0, 0x0, 0x0, 0x9, 0x80, 0x0, 0x0, 0x0,
    0x8, 0x85, 0x0,

    /* U+0052 "R" */
    0x28, 0xf6, 0x7a, 0x30, 0x0, 0x1f, 0x0, 0x1e,
    0x10, 0x1, 0xf0, 0x0, 0xb4, 0x0, 0x1e, 0x0,
    0xd, 0x30, 0x1, 0xe0, 0x4, 0xb0, 0x0, 0x1f,
    0x6a, 0x90, 0x0, 0x1, 0xf0, 0xc, 0x30, 0x0,
    0x1f, 0x0, 0x5a, 0x0, 0x1, 0xf0, 0x1, 0xe0,
    0x2, 0x8f, 0x61, 0xa, 0x82,

    /* U+0053 "S" */
    0x3, 0xa8, 0x96, 0x0, 0xd0, 0x0, 0xb0, 0x1e,
    0x0, 0x5, 0x0, 0xe6, 0x0, 0x0, 0x4, 0xfb,
    0x30, 0x0, 0x1, 0x8f, 0x70, 0x0, 0x0, 0x2f,
    0x13, 0x30, 0x0, 0xc2, 0x56, 0x0, 0xc, 0x1,
    0x87, 0x8a, 0x20,

    /* U+0054 "T" */
    0xa8, 0x7c, 0xb7, 0x89, 0xa0, 0x9, 0x70, 0x9,
    0x50, 0x9, 0x70, 0x5, 0x0, 0x9, 0x70, 0x0,
    0x0, 0x9, 0x70, 0x0, 0x0, 0x9, 0x70, 0x0,
    0x0, 0x9, 0x70, 0x0, 0x0, 0x9, 0x70, 0x0,
    0x0, 0x9, 0x70, 0x0, 0x0, 0x6c, 0xc6, 0x0,

    /* U+0055 "U" */
    0x3a, 0xe7, 0x0, 0x7b, 0x70, 0x4, 0xc0, 0x0,
    0x7, 0x0, 0x4, 0xc0, 0x0, 0x7, 0x0, 0x4,
    0xc0, 0x0, 0x7, 0x0, 0x4, 0xc0, 0x0, 0x7,
    0x0, 0x4, 0xc0, 0x0, 0x7, 0x0, 0x3, 0xd0,
    0x0, 0x7, 0x0, 0x1, 0xf0, 0x0, 0x7, 0x0,
    0x0, 0xc7, 0x0, 0x45, 0x0, 0x0, 0x1b, 0xba,
    0x70, 0x0,

    /* U+0056 "V" */
    0x6e, 0xa4, 0x3, 0x9a, 0x30, 0x98, 0x0, 0x6,
    0x10, 0x4, 0xc0, 0x0, 0x70, 0x0, 0xf, 0x10,
    0x7, 0x0, 0x0, 0xa6, 0x4, 0x30, 0x0, 0x5,
    0xa0, 0x70, 0x0, 0x0, 0x1e, 0x7, 0x0, 0x0,
    0x0, 0xc6, 0x40, 0x0, 0x0, 0x7, 0xe0, 0x0,
    0x0, 0x0, 0x2b, 0x0, 0x0,

    /* U+0057 "W" */
    0x6d, 0xa3, 0x7, 0xf8, 0x22, 0x9a, 0x30, 0x88,
    0x0, 0x1f, 0x30, 0x6, 0x20, 0x4, 0xc0, 0x5,
    0x98, 0x0, 0x70, 0x0, 0xf, 0x0, 0x63, 0xc0,
    0x7, 0x0, 0x0, 0xc3, 0x6, 0xe, 0x1, 0x60,
    0x0, 0x8, 0x71, 0x40, 0xa4, 0x42, 0x0, 0x0,
    0x4b, 0x50, 0x6, 0x87, 0x0, 0x0, 0x1, 0xe6,
    0x0, 0x2c, 0x70, 0x0, 0x0, 0xc, 0x70, 0x0,
    0xe6, 0x0, 0x0, 0x0, 0x84, 0x0, 0x9, 0x30,
    0x0,

    /* U+0058 "X" */
    0x4c, 0xd5, 0x6, 0xc7, 0x0, 0x2e, 0x10, 0x25,
    0x0, 0x0, 0xa8, 0x7, 0x0, 0x0, 0x2, 0xe4,
    0x30, 0x0, 0x0, 0x9, 0xb0, 0x0, 0x0, 0x0,
    0x6e, 0x10, 0x0, 0x0, 0x15, 0x89, 0x0, 0x0,
    0x7, 0x1, 0xe2, 0x0, 0x2, 0x50, 0x8, 0xa0,
    0x5, 0xc7, 0x1, 0x8f, 0x71,

    /* U+0059 "Y" */
    0x4d, 0xb4, 0x5, 0xc7, 0x0, 0x5c, 0x0, 0x7,
    0x0, 0x0, 0xe3, 0x5, 0x20, 0x0, 0x7, 0xa0,
    0x70, 0x0, 0x0, 0x1f, 0x44, 0x0, 0x0, 0x0,
    0x9c, 0x0, 0x0, 0x0, 0x6, 0xa0, 0x0, 0x0,
    0x0, 0x6a, 0x0, 0x0, 0x0, 0x6, 0xa0, 0x0,
    0x0, 0x5, 0xbd, 0x70, 0x0,

    /* U+005A "Z" */
    0x6a, 0x77, 0x7c, 0x97, 0x30, 0x1, 0xe2, 0x40,
    0x0, 0xa8, 0x0, 0x0, 0x3e, 0x0, 0x0, 0xc,
    0x60, 0x0, 0x5, 0xc0, 0x0, 0x0, 0xe3, 0x0,
    0x0, 0x7a, 0x0, 0x5, 0x1e, 0x20, 0x0, 0xa9,
    0xc7, 0x77, 0x7c,

    /* U+005B "[" */
    0xb9, 0x4b, 0x20, 0xb2, 0xa, 0x20, 0xa2, 0xa,
    0x20, 0xa2, 0xa, 0x20, 0xa2, 0xb, 0x20, 0xb2,
    0xb, 0x20, 0x57, 0x40,

    /* U+005C "\\" */
    0x80, 0x0, 0x7, 0x10, 0x0, 0x35, 0x0, 0x0,
    0x80, 0x0, 0x8, 0x0, 0x0, 0x52, 0x0, 0x1,
    0x70, 0x0, 0x8, 0x0, 0x0, 0x80, 0x0, 0x4,
    0x40, 0x0, 0x8, 0x0, 0x0, 0x80, 0x0, 0x3,
    0x0,

    /* U+005D "]" */
    0x38, 0xd0, 0xd, 0x0, 0xc0, 0xc, 0x0, 0xc0,
    0xc, 0x0, 0xc0, 0xc, 0x0, 0xc0, 0xc, 0x0,
    0xc0, 0x1d, 0x37, 0x60,

    /* U+005E "^" */
    0x1, 0xc1, 0x0, 0x82, 0x80, 0x26, 0x6, 0x28,
    0x0, 0x8, 0x0, 0x0, 0x0,

    /* U+005F "_" */
    0x9, 0x99, 0x99, 0x98,

    /* U+0060 "`" */
    0xa2, 0x2, 0xa0, 0x2, 0x20,

    /* U+0061 "a" */
    0x5, 0x58, 0x90, 0x0, 0xd0, 0xb, 0x30, 0x0,
    0x0, 0xb5, 0x0, 0x38, 0x5a, 0x50, 0x2c, 0x0,
    0x95, 0x4, 0xb0, 0x1c, 0x50, 0xb, 0xa6, 0x4b,
    0x30,

    /* U+0062 "b" */
    0x39, 0x60, 0x0, 0x0, 0x7, 0x80, 0x0, 0x0,
    0x7, 0x80, 0x0, 0x0, 0x7, 0x70, 0x0, 0x0,
    0x7, 0x97, 0xab, 0x10, 0x7, 0xa0, 0x7, 0xa0,
    0x7, 0x80, 0x1, 0xf0, 0x7, 0x80, 0x0, 0xf0,
    0x7, 0x80, 0x1, 0xe0, 0x7, 0xa0, 0x7, 0x90,
    0x4d, 0x88, 0xaa, 0x0,

    /* U+0063 "c" */
    0x1, 0x97, 0x72, 0xc, 0x20, 0x2b, 0x3c, 0x0,
    0x0, 0x6a, 0x0, 0x0, 0x4c, 0x0, 0x0, 0xe,
    0x40, 0x3, 0x2, 0xcb, 0x82,

    /* U+0064 "d" */
    0x0, 0x0, 0x7b, 0x10, 0x0, 0x0, 0xd, 0x10,
    0x0, 0x0, 0xd, 0x10, 0x0, 0x0, 0xd, 0x10,
    0x2, 0xb9, 0x6d, 0x10, 0xd, 0x20, 0xe, 0x10,
    0x4c, 0x0, 0xd, 0x10, 0x6a, 0x0, 0xd, 0x10,
    0x4b, 0x0, 0xd, 0x10, 0xd, 0x10, 0xe, 0x10,
    0x3, 0xa7, 0x5d, 0x81,

    /* U+0065 "e" */
    0x2, 0x96, 0x91, 0x0, 0xd1, 0x3, 0xb0, 0x4b,
    0x0, 0xf, 0x6, 0xc6, 0x67, 0xa0, 0x4b, 0x0,
    0x0, 0x0, 0xd3, 0x0, 0x30, 0x2, 0xba, 0x93,
    0x0,

    /* U+0066 "f" */
    0x0, 0x0, 0x0, 0x0, 0x37, 0xe3, 0x0, 0xb0,
    0x10, 0x3, 0x90, 0x0, 0x6, 0x80, 0x0, 0x4b,
    0xc7, 0x20, 0x7, 0x80, 0x0, 0x7, 0x80, 0x0,
    0x7, 0x80, 0x0, 0x7, 0x80, 0x0, 0x7, 0x80,
    0x0, 0x3b, 0xc5, 0x0,

    /* U+0067 "g" */
    0x5, 0x87, 0x99, 0x50, 0xe0, 0xa, 0x30, 0x2c,
    0x0, 0x76, 0x0, 0xd1, 0xc, 0x30, 0x6, 0x76,
    0x40, 0x0, 0xa0, 0x0, 0x0, 0xb, 0xff, 0xea,
    0x2, 0x60, 0x1, 0xb5, 0x76, 0x0, 0x9, 0x21,
    0xa8, 0x77, 0x30,

    /* U+0068 "h" */
    0x39, 0x60, 0x0, 0x0, 0x7, 0x80, 0x0, 0x0,
    0x7, 0x80, 0x0, 0x0, 0x7, 0x70, 0x0, 0x0,
    0x7, 0x86, 0xbd, 0x10, 0x7, 0xb2, 0x8, 0x70,
    0x7, 0x80, 0x6, 0x90, 0x7, 0x80, 0x6, 0x90,
    0x7, 0x80, 0x6, 0x90, 0x7, 0x80, 0x6, 0x90,
    0x3b, 0xb2, 0x2a, 0xc3,

    /* U+0069 "i" */
    0x9, 0x70, 0x3, 0x20, 0x0, 0x0, 0x39, 0x70,
    0x7, 0x80, 0x7, 0x80, 0x7, 0x80, 0x7, 0x80,
    0x7, 0x80, 0x2b, 0xc2,

    /* U+006A "j" */
    0x0, 0x97, 0x0, 0x42, 0x0, 0x0, 0x3, 0xa7,
    0x0, 0x87, 0x0, 0x87, 0x0, 0x87, 0x0, 0x77,
    0x0, 0x87, 0x0, 0x87, 0x0, 0x85, 0x0, 0xa1,
    0xba, 0x50,

    /* U+006B "k" */
    0x39, 0x60, 0x0, 0x0, 0x7, 0x80, 0x0, 0x0,
    0x7, 0x80, 0x0, 0x0, 0x7, 0x80, 0x0, 0x0,
    0x7, 0x80, 0x7c, 0x60, 0x7, 0x80, 0x70, 0x0,
    0x7, 0x89, 0x40, 0x0, 0x7, 0xc7, 0xb0, 0x0,
    0x7, 0x80, 0xd4, 0x0, 0x7, 0x80, 0x4c, 0x0,
    0x3b, 0xc3, 0xc, 0x81,

    /* U+006C "l" */
    0x39, 0x60, 0x7, 0x80, 0x7, 0x80, 0x7, 0x80,
    0x7, 0x80, 0x7, 0x80, 0x7, 0x80, 0x7, 0x80,
    0x7, 0x80, 0x7, 0x80, 0x3b, 0xc3,

    /* U+006D "m" */
    0x3b, 0x67, 0xbb, 0x8, 0xcb, 0x0, 0x8, 0xb1,
    0xb, 0x90, 0xc, 0x30, 0x7, 0x80, 0x8, 0x60,
    0x9, 0x50, 0x7, 0x80, 0x8, 0x60, 0x9, 0x50,
    0x7, 0x80, 0x8, 0x60, 0x9, 0x50, 0x7, 0x80,
    0x8, 0x60, 0xa, 0x50, 0x3b, 0xb2, 0x3c, 0xa2,
    0x4c, 0xa1,

    /* U+006E "n" */
    0x3b, 0x66, 0xbd, 0x10, 0x7, 0xb2, 0x8, 0x70,
    0x7, 0x80, 0x6, 0x90, 0x7, 0x80, 0x6, 0x90,
    0x7, 0x80, 0x6, 0x90, 0x7, 0x80, 0x6, 0x90,
    0x3b, 0xb2, 0x2a, 0xc3,

    /* U+006F "o" */
    0x2, 0x96, 0x93, 0x0, 0xd1, 0x0, 0xd1, 0x4b,
    0x0, 0xa, 0x66, 0xa0, 0x0, 0x88, 0x4b, 0x0,
    0x9, 0x60, 0xd1, 0x0, 0xd1, 0x2, 0x96, 0x83,
    0x0,

    /* U+0070 "p" */
    0x3b, 0x77, 0xab, 0x10, 0x7, 0xa0, 0x7, 0xa0,
    0x7, 0x80, 0x1, 0xf0, 0x7, 0x80, 0x0, 0xf0,
    0x7, 0x80, 0x1, 0xe0, 0x7, 0x90, 0x7, 0x90,
    0x7, 0xa8, 0xaa, 0x0, 0x7, 0x80, 0x0, 0x0,
    0x7, 0x80, 0x0, 0x0, 0x3b, 0xb4, 0x0, 0x0,

    /* U+0071 "q" */
    0x2, 0xb9, 0x75, 0x20, 0xd, 0x20, 0xe, 0x20,
    0x4c, 0x0, 0xd, 0x10, 0x6a, 0x0, 0xd, 0x10,
    0x4b, 0x0, 0xd, 0x10, 0xe, 0x20, 0x1e, 0x10,
    0x3, 0xc9, 0x6d, 0x10, 0x0, 0x0, 0xd, 0x20,
    0x0, 0x0, 0xd, 0x20, 0x0, 0x0, 0x6e, 0x70,

    /* U+0072 "r" */
    0x4b, 0x67, 0xe4, 0x7, 0xb2, 0x21, 0x7, 0xa0,
    0x0, 0x7, 0x80, 0x0, 0x7, 0x80, 0x0, 0x7,
    0x80, 0x0, 0x3b, 0xc4, 0x0,

    /* U+0073 "s" */
    0x7, 0x87, 0x70, 0x2b, 0x0, 0x80, 0x1e, 0x50,
    0x0, 0x4, 0xdd, 0x40, 0x0, 0x5, 0xe0, 0x54,
    0x0, 0xd0, 0x39, 0x78, 0x50,

    /* U+0074 "t" */
    0x5, 0x60, 0x0, 0x85, 0x0, 0x5c, 0xa7, 0x0,
    0x95, 0x0, 0x9, 0x50, 0x0, 0x95, 0x0, 0xa,
    0x50, 0x0, 0x86, 0x0, 0x3, 0xc8, 0x10,

    /* U+0075 "u" */
    0x0, 0x0, 0x0, 0x0, 0x4d, 0x60, 0x3c, 0x70,
    0x9, 0x50, 0x8, 0x60, 0xa, 0x50, 0x8, 0x60,
    0xa, 0x50, 0x8, 0x60, 0x9, 0x60, 0x8, 0x60,
    0x8, 0x90, 0x1c, 0x60, 0x2, 0xdb, 0x78, 0xc2,

    /* U+0076 "v" */
    0x8, 0xf6, 0x5, 0xb3, 0x0, 0xe2, 0x1, 0x50,
    0x0, 0x97, 0x5, 0x0, 0x0, 0x3c, 0x6, 0x0,
    0x0, 0xd, 0x35, 0x0, 0x0, 0x8, 0xc0, 0x0,
    0x0, 0x2, 0xa0, 0x0,

    /* U+0077 "w" */
    0x8, 0xe5, 0x8, 0xd4, 0x2a, 0x70, 0xe, 0x10,
    0x5d, 0x0, 0x70, 0x0, 0xa5, 0x6, 0x94, 0x6,
    0x0, 0x5, 0x91, 0x44, 0x92, 0x40, 0x0, 0xd,
    0x50, 0xd, 0x50, 0x0, 0x0, 0xb8, 0x0, 0xa8,
    0x0, 0x0, 0x6, 0x50, 0x5, 0x50, 0x0,

    /* U+0078 "x" */
    0x5e, 0x90, 0x5b, 0x30, 0x5c, 0x6, 0x10, 0x0,
    0xc7, 0x50, 0x0, 0x3, 0xf1, 0x0, 0x0, 0x79,
    0x90, 0x0, 0x42, 0xe, 0x30, 0x5b, 0x31, 0xbd,
    0x40,

    /* U+0079 "y" */
    0x8, 0xe5, 0x5, 0xb3, 0x0, 0xe2, 0x0, 0x60,
    0x0, 0x87, 0x5, 0x10, 0x0, 0x2d, 0x6, 0x0,
    0x0, 0xc, 0x35, 0x0, 0x0, 0x5, 0xd0, 0x0,
    0x0, 0x0, 0x90, 0x0, 0x0, 0x1, 0x40, 0x0,
    0x2, 0x7, 0x0, 0x0, 0xd, 0xa2, 0x0, 0x0,

    /* U+007A "z" */
    0x69, 0x66, 0xe4, 0x62, 0x5, 0xb0, 0x0, 0xd,
    0x20, 0x0, 0x97, 0x0, 0x3, 0xd0, 0x1, 0xc,
    0x40, 0x8, 0x5d, 0x66, 0x88,

    /* U+007B "{" */
    0x1, 0xa8, 0x0, 0x76, 0x0, 0x6, 0x60, 0x0,
    0x38, 0x0, 0x0, 0xa0, 0x0, 0x37, 0x0, 0x3a,
    0x10, 0x0, 0x18, 0x0, 0x0, 0xa0, 0x0, 0x48,
    0x0, 0x7, 0x50, 0x0, 0x67, 0x0, 0x0, 0x77,
    0x0,

    /* U+007C "|" */
    0x31, 0x62, 0x62, 0x62, 0x62, 0x62, 0x62, 0x62,
    0x62, 0x62, 0x62, 0x62, 0x62, 0x62, 0x62,

    /* U+007D "}" */
    0x49, 0x60, 0x0, 0xd0, 0x0, 0xc0, 0x0, 0xa0,
    0x2, 0x70, 0x0, 0xa0, 0x0, 0x69, 0x1, 0x80,
    0x2, 0x80, 0x0, 0xb0, 0x0, 0xc0, 0x0, 0xd0,
    0x49, 0x30,

    /* U+007E "~" */
    0x7, 0x95, 0x3, 0x11, 0x30, 0x59, 0x70,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x12, 0x0, 0x0,
    0x0, 0x3, 0x7c, 0xff, 0x0, 0x0, 0x59, 0xef,
    0xff, 0xff, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xf, 0xff, 0xfd, 0x84, 0x8f, 0x0, 0xf,
    0xd7, 0x20, 0x0, 0x8f, 0x0, 0xf, 0x80, 0x0,
    0x0, 0x8f, 0x0, 0xf, 0x80, 0x0, 0x0, 0x8f,
    0x0, 0xf, 0x80, 0x0, 0x7b, 0xdf, 0x2, 0x3f,
    0x80, 0x6, 0xff, 0xff, 0xaf, 0xff, 0x80, 0x2,
    0xef, 0xf9, 0xef, 0xff, 0x60, 0x0, 0x2, 0x10,
    0x29, 0xa7, 0x0, 0x0, 0x0, 0x0,

    /* U+F008 "" */
    0xb4, 0xdf, 0xff, 0xff, 0xfd, 0x4b, 0xe8, 0xe7,
    0x22, 0x22, 0x7e, 0x8e, 0xc0, 0xc5, 0x0, 0x0,
    0x6c, 0xc, 0xfc, 0xf6, 0x11, 0x11, 0x7f, 0xcf,
    0xc0, 0xcf, 0xff, 0xff, 0xfb, 0xc, 0xfc, 0xf6,
    0x11, 0x11, 0x7f, 0xcf, 0xc0, 0xc5, 0x0, 0x0,
    0x6c, 0xc, 0xe8, 0xe7, 0x22, 0x22, 0x7e, 0x8e,
    0xb4, 0xdf, 0xff, 0xff, 0xfd, 0x4b,

    /* U+F00B "" */
    0xdf, 0xf6, 0x9f, 0xff, 0xff, 0xfd, 0xff, 0xf8,
    0xcf, 0xff, 0xff, 0xff, 0xef, 0xf6, 0xaf, 0xff,
    0xff, 0xfe, 0x13, 0x20, 0x3, 0x33, 0x33, 0x31,
    0xff, 0xf7, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xbf, 0xff,
    0xff, 0xff, 0x13, 0x20, 0x3, 0x33, 0x33, 0x31,
    0xef, 0xf6, 0xaf, 0xff, 0xff, 0xfe, 0xff, 0xf8,
    0xcf, 0xff, 0xff, 0xff, 0xdf, 0xf6, 0xaf, 0xff,
    0xff, 0xfd,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0xd4, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xfe, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xf4, 0x4d, 0x30, 0x0, 0x3f, 0xff, 0x40,
    0xef, 0xf3, 0x3, 0xff, 0xf4, 0x0, 0x4f, 0xff,
    0x6f, 0xff, 0x40, 0x0, 0x4, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x3, 0xd3, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x14, 0x0, 0x0, 0x22, 0xd, 0xf7, 0x0, 0x4f,
    0xf1, 0x9f, 0xf7, 0x4f, 0xfd, 0x0, 0xaf, 0xff,
    0xfd, 0x10, 0x0, 0xbf, 0xfe, 0x10, 0x0, 0x4f,
    0xff, 0xf7, 0x0, 0x4f, 0xfd, 0xaf, 0xf7, 0xe,
    0xfd, 0x10, 0xaf, 0xf2, 0x5b, 0x10, 0x0, 0x99,
    0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x7, 0x70, 0x0, 0x0, 0x0, 0x32,
    0xf, 0xf0, 0x24, 0x0, 0x5, 0xfc, 0xf, 0xf0,
    0xcf, 0x50, 0x1f, 0xf4, 0xf, 0xf0, 0x5f, 0xf1,
    0x7f, 0x80, 0xf, 0xf0, 0x8, 0xf7, 0xbf, 0x20,
    0xf, 0xf0, 0x2, 0xfb, 0xcf, 0x10, 0xe, 0xe0,
    0x1, 0xfc, 0xaf, 0x40, 0x1, 0x10, 0x4, 0xfa,
    0x5f, 0xb0, 0x0, 0x0, 0xb, 0xf6, 0xd, 0xfa,
    0x10, 0x1, 0xaf, 0xd0, 0x2, 0xdf, 0xfc, 0xcf,
    0xfd, 0x20, 0x0, 0x8, 0xef, 0xfe, 0x91, 0x0,
    0x0, 0x0, 0x1, 0x10, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x14, 0x41, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf7, 0x0, 0x0, 0x3, 0x43, 0xdf, 0xfd,
    0x34, 0x30, 0xe, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x6f, 0xff, 0xfb, 0xbf, 0xff, 0xf6, 0x1b, 0xff,
    0x70, 0x7, 0xff, 0xb1, 0x7, 0xff, 0x20, 0x2,
    0xff, 0x70, 0x1b, 0xff, 0x70, 0x7, 0xff, 0xb1,
    0x6f, 0xff, 0xfb, 0xbf, 0xff, 0xf6, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x3, 0x42, 0xcf, 0xfc,
    0x23, 0x30, 0x0, 0x0, 0x7f, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x4, 0x41, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x73, 0x3, 0x83, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0x67, 0xf7, 0x0, 0x0, 0x3,
    0xee, 0x5a, 0xfe, 0xf7, 0x0, 0x0, 0x6f, 0xd3,
    0xb5, 0x7f, 0xf7, 0x0, 0x9, 0xfb, 0x3d, 0xff,
    0x85, 0xfe, 0x30, 0xbf, 0x95, 0xff, 0xff, 0xfb,
    0x3e, 0xf4, 0x76, 0x6f, 0xff, 0xff, 0xff, 0xd2,
    0xa1, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xcf, 0xfa, 0x2, 0xff, 0xf4, 0x0, 0x0,
    0xcf, 0xfa, 0x2, 0xff, 0xf4, 0x0, 0x0, 0xaf,
    0xf8, 0x1, 0xff, 0xf3, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x27, 0x72, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf8, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x4, 0xff, 0xff, 0x40, 0x0,
    0x23, 0x33, 0x5f, 0xf5, 0x33, 0x32, 0xff, 0xff,
    0xa4, 0x4a, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5c, 0x8f,
    0x9a, 0xaa, 0xaa, 0xaa, 0xaa, 0xa8,

    /* U+F01C "" */
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x1,
    0xed, 0x88, 0x88, 0x89, 0xf8, 0x0, 0xa, 0xf2,
    0x0, 0x0, 0x0, 0xaf, 0x30, 0x5f, 0x70, 0x0,
    0x0, 0x0, 0x1e, 0xd0, 0xef, 0x88, 0x60, 0x0,
    0x28, 0x8b, 0xf6, 0xff, 0xff, 0xf3, 0x0, 0xbf,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,

    /* U+F021 "" */
    0x0, 0x0, 0x1, 0x10, 0x0, 0x59, 0x0, 0x19,
    0xef, 0xfd, 0x70, 0x9f, 0x3, 0xef, 0xda, 0x9d,
    0xfe, 0xbf, 0xe, 0xf6, 0x0, 0x0, 0x5f, 0xff,
    0x7f, 0x70, 0x0, 0x3f, 0xff, 0xff, 0x69, 0x0,
    0x0, 0x2a, 0xaa, 0xa9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaa, 0xaa, 0xa2, 0x0, 0x0, 0xa6,
    0xff, 0xfe, 0xf3, 0x0, 0x7, 0xf7, 0xff, 0xf5,
    0x0, 0x0, 0x7f, 0xe0, 0xfb, 0xef, 0xd9, 0xad,
    0xfe, 0x30, 0xfa, 0x8, 0xef, 0xfe, 0x91, 0x0,
    0x95, 0x0, 0x1, 0x10, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x2a, 0x0, 0x2, 0xef, 0x78, 0x8e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xdf, 0xff, 0xff, 0x0, 0x7, 0xff,
    0x0, 0x0, 0x7f, 0x0, 0x0, 0x1,

    /* U+F027 "" */
    0x0, 0x0, 0x2a, 0x0, 0x0, 0x0, 0x2e, 0xf0,
    0x0, 0x78, 0x8e, 0xff, 0x3, 0xf, 0xff, 0xff,
    0xf0, 0xba, 0xff, 0xff, 0xff, 0x3, 0xff, 0xff,
    0xff, 0xf0, 0xaa, 0xdf, 0xff, 0xff, 0x4, 0x0,
    0x0, 0x8f, 0xf0, 0x0, 0x0, 0x0, 0x8f, 0x0,
    0x0, 0x0, 0x0, 0x10, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xd2, 0x0, 0x0, 0x0,
    0x2a, 0x0, 0x11, 0x8e, 0x10, 0x0, 0x2, 0xef,
    0x0, 0x7d, 0x2b, 0x90, 0x78, 0x8e, 0xff, 0x3,
    0xa, 0xb3, 0xf0, 0xff, 0xff, 0xff, 0xb, 0xa1,
    0xf1, 0xe3, 0xff, 0xff, 0xff, 0x3, 0xf0, 0xe3,
    0xc5, 0xff, 0xff, 0xff, 0xb, 0xa1, 0xf1, 0xe3,
    0xdf, 0xff, 0xff, 0x3, 0xa, 0xb3, 0xf0, 0x0,
    0x7, 0xff, 0x0, 0x7d, 0x2b, 0x90, 0x0, 0x0,
    0x7f, 0x0, 0x11, 0x9e, 0x10, 0x0, 0x0, 0x1,
    0x0, 0x6, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F03E "" */
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xfd, 0x5b,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0x1, 0xff, 0xff,
    0xef, 0xff, 0xfb, 0x18, 0xff, 0xf6, 0x1c, 0xff,
    0xff, 0xfc, 0xff, 0x60, 0x1, 0xdf, 0xff, 0x60,
    0x96, 0x0, 0x0, 0x8f, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xfc, 0x88, 0x88, 0x88, 0x88, 0xcf,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb,

    /* U+F043 "" */
    0x0, 0x2, 0x40, 0x0, 0x0, 0x0, 0xcf, 0x10,
    0x0, 0x0, 0x2f, 0xf7, 0x0, 0x0, 0xa, 0xff,
    0xe0, 0x0, 0x4, 0xff, 0xff, 0x80, 0x0, 0xef,
    0xff, 0xff, 0x30, 0x8f, 0xff, 0xff, 0xfc, 0xe,
    0xff, 0xff, 0xff, 0xf2, 0xf9, 0xcf, 0xff, 0xff,
    0x3d, 0xc5, 0xff, 0xff, 0xf1, 0x6f, 0xa3, 0xbf,
    0xfa, 0x0, 0x8f, 0xff, 0xfb, 0x0, 0x0, 0x26,
    0x74, 0x0, 0x0,

    /* U+F048 "" */
    0x58, 0x0, 0x0, 0x35, 0x9f, 0x10, 0x5, 0xfe,
    0x9f, 0x10, 0x6f, 0xfe, 0x9f, 0x17, 0xff, 0xfe,
    0x9f, 0x9f, 0xff, 0xfe, 0x9f, 0xff, 0xff, 0xfe,
    0x9f, 0xef, 0xff, 0xfe, 0x9f, 0x2d, 0xff, 0xfe,
    0x9f, 0x10, 0xcf, 0xfe, 0x9f, 0x10, 0xb, 0xfe,
    0x8f, 0x0, 0x0, 0x9b, 0x0, 0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x46, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0x40,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xa1, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf7, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xfd, 0x50, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xb1, 0xff, 0xff, 0xff, 0xff, 0xff, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xb1, 0xff, 0xff, 0xff, 0xfd,
    0x40, 0xf, 0xff, 0xff, 0xf7, 0x0, 0x0, 0xff,
    0xff, 0xa1, 0x0, 0x0, 0xf, 0xfd, 0x40, 0x0,
    0x0, 0x0, 0x36, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0xaf, 0xfe, 0x30, 0xaf, 0xfe, 0x3f, 0xff, 0xf7,
    0xf, 0xff, 0xf7, 0xff, 0xff, 0x80, 0xff, 0xff,
    0x8f, 0xff, 0xf8, 0xf, 0xff, 0xf8, 0xff, 0xff,
    0x80, 0xff, 0xff, 0x8f, 0xff, 0xf8, 0xf, 0xff,
    0xf8, 0xff, 0xff, 0x80, 0xff, 0xff, 0x8f, 0xff,
    0xf8, 0xf, 0xff, 0xf8, 0xff, 0xff, 0x80, 0xff,
    0xff, 0x8f, 0xff, 0xf7, 0xf, 0xff, 0xf7, 0x48,
    0x98, 0x10, 0x48, 0x98, 0x10,

    /* U+F04D "" */
    0x48, 0x88, 0x88, 0x88, 0x88, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xaf,
    0xff, 0xff, 0xff, 0xfe, 0x30,

    /* U+F051 "" */
    0x26, 0x0, 0x0, 0x58, 0x7f, 0xa0, 0x0, 0xbf,
    0x8f, 0xfb, 0x0, 0xbf, 0x8f, 0xff, 0xc1, 0xbf,
    0x8f, 0xff, 0xfd, 0xcf, 0x8f, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xef, 0x8f, 0xff, 0xf4, 0xbf,
    0x8f, 0xff, 0x40, 0xbf, 0x8f, 0xe3, 0x0, 0xbf,
    0x5d, 0x20, 0x0, 0xae, 0x0, 0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x3, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xfa, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff,
    0x90, 0x0, 0x0, 0x1e, 0xff, 0xff, 0xf8, 0x0,
    0x1, 0xdf, 0xff, 0xff, 0xff, 0x70, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x1, 0x34, 0x44, 0x44, 0x44, 0x30,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xf5,

    /* U+F053 "" */
    0x0, 0x0, 0x3, 0x10, 0x0, 0x5, 0xfb, 0x0,
    0x5, 0xff, 0x40, 0x5, 0xff, 0x40, 0x5, 0xff,
    0x50, 0x3, 0xff, 0x50, 0x0, 0xb, 0xfc, 0x10,
    0x0, 0xb, 0xfc, 0x10, 0x0, 0xb, 0xfc, 0x10,
    0x0, 0xc, 0xfb, 0x0, 0x0, 0xa, 0x50,

    /* U+F054 "" */
    0x3, 0x10, 0x0, 0x3, 0xfc, 0x10, 0x0, 0xb,
    0xfc, 0x10, 0x0, 0xb, 0xfc, 0x10, 0x0, 0xb,
    0xfc, 0x10, 0x0, 0xd, 0xfb, 0x0, 0x5, 0xff,
    0x50, 0x5, 0xff, 0x50, 0x5, 0xff, 0x50, 0x3,
    0xff, 0x50, 0x0, 0xa, 0x50, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x69, 0x10, 0x0, 0x0, 0x0, 0xd,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0xef, 0x60, 0x0,
    0x0, 0x0, 0xe, 0xf6, 0x0, 0x0, 0x58, 0x88,
    0xff, 0xb8, 0x88, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x9b, 0xbb, 0xff, 0xdb, 0xbb, 0x30, 0x0,
    0xe, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xef, 0x60,
    0x0, 0x0, 0x0, 0xe, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x9d, 0x20, 0x0, 0x0,

    /* U+F068 "" */
    0x46, 0x66, 0x66, 0x66, 0x66, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0xad, 0xdd, 0xdd, 0xdd, 0xdd,
    0x40,

    /* U+F06E "" */
    0x0, 0x3, 0xad, 0xff, 0xc7, 0x0, 0x0, 0x0,
    0x9f, 0xe6, 0x24, 0xaf, 0xe3, 0x0, 0xb, 0xff,
    0x20, 0x77, 0x9, 0xff, 0x40, 0x7f, 0xf9, 0x0,
    0xcf, 0xa1, 0xff, 0xe1, 0xef, 0xf6, 0x7f, 0xff,
    0xf0, 0xef, 0xf7, 0x8f, 0xf9, 0x3f, 0xff, 0xc1,
    0xff, 0xe1, 0xb, 0xff, 0x26, 0xca, 0x19, 0xff,
    0x40, 0x0, 0x9f, 0xe6, 0x24, 0xaf, 0xe3, 0x0,
    0x0, 0x3, 0x9d, 0xff, 0xc7, 0x0, 0x0,

    /* U+F070 "" */
    0x32, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0xf8, 0x4a, 0xef, 0xeb, 0x50, 0x0, 0x0,
    0x0, 0x9f, 0xfd, 0x52, 0x5d, 0xfc, 0x10, 0x0,
    0x0, 0x5, 0xfe, 0x4a, 0x70, 0xcf, 0xe1, 0x0,
    0xb, 0x80, 0x2d, 0xff, 0xf7, 0x4f, 0xfb, 0x0,
    0x2f, 0xfb, 0x0, 0xaf, 0xfb, 0x2f, 0xff, 0x30,
    0xb, 0xff, 0x50, 0x7, 0xfe, 0x7f, 0xfb, 0x0,
    0x1, 0xdf, 0xc0, 0x0, 0x3e, 0xff, 0xe1, 0x0,
    0x0, 0x1b, 0xfc, 0x42, 0x1, 0xbf, 0xa0, 0x0,
    0x0, 0x0, 0x5b, 0xef, 0xb0, 0x8, 0xfc, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x40,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x3, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xfd, 0xef, 0xa0, 0x0, 0x0, 0x0, 0xb,
    0xfb, 0x3, 0xff, 0x30, 0x0, 0x0, 0x4, 0xff,
    0xc0, 0x4f, 0xfc, 0x0, 0x0, 0x0, 0xdf, 0xfd,
    0x5, 0xff, 0xf6, 0x0, 0x0, 0x7f, 0xff, 0xf8,
    0xcf, 0xff, 0xe1, 0x0, 0x1f, 0xff, 0xfc, 0x4,
    0xff, 0xff, 0x90, 0xa, 0xff, 0xff, 0xd2, 0x7f,
    0xff, 0xff, 0x20, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x4, 0x78, 0x88, 0x88, 0x88, 0x88,
    0x87, 0x0,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x6, 0x10, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xc1, 0xff, 0xf8, 0x0, 0x2e,
    0xff, 0xfc, 0xcd, 0xff, 0x62, 0xef, 0xdf, 0xf9,
    0x0, 0x2c, 0x4e, 0xf9, 0xf, 0x90, 0x0, 0x2,
    0xef, 0x90, 0x7, 0x0, 0x0, 0x2e, 0xf8, 0x88,
    0xf, 0xa0, 0xcd, 0xff, 0x80, 0xdf, 0xdf, 0xf9,
    0xff, 0xf8, 0x0, 0x1e, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0x10,

    /* U+F077 "" */
    0x0, 0x0, 0x27, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0xf9, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xf9, 0x0,
    0x0, 0x2e, 0xf9, 0x2e, 0xf9, 0x0, 0x2e, 0xf9,
    0x0, 0x2e, 0xf9, 0xb, 0xf9, 0x0, 0x0, 0x2e,
    0xf4, 0x27, 0x0, 0x0, 0x0, 0x27, 0x0,

    /* U+F078 "" */
    0x26, 0x0, 0x0, 0x0, 0x27, 0xb, 0xf9, 0x0,
    0x0, 0x2e, 0xf4, 0x2e, 0xf9, 0x0, 0x2e, 0xf9,
    0x0, 0x2e, 0xf9, 0x2e, 0xf9, 0x0, 0x0, 0x2e,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x2e, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x26, 0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xc0, 0x7, 0x77, 0x77, 0x72, 0x0,
    0x3, 0xff, 0xfc, 0x2e, 0xff, 0xff, 0xf9, 0x0,
    0xf, 0xcf, 0xcf, 0xa0, 0x0, 0x0, 0xe9, 0x0,
    0x4, 0x1e, 0x93, 0x20, 0x0, 0x0, 0xe9, 0x0,
    0x0, 0xe, 0x90, 0x0, 0x0, 0x0, 0xe9, 0x0,
    0x0, 0xe, 0x90, 0x0, 0x0, 0xb5, 0xe9, 0x97,
    0x0, 0xe, 0xc7, 0x77, 0x73, 0xbf, 0xff, 0xf6,
    0x0, 0xd, 0xff, 0xff, 0xfd, 0xb, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa6, 0x0,

    /* U+F07B "" */
    0xbf, 0xff, 0xf6, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0x98, 0x88, 0x74, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb,

    /* U+F093 "" */
    0x0, 0x0, 0x2, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0xe3, 0x0, 0x0, 0x0, 0x3, 0xef, 0xfe,
    0x30, 0x0, 0x0, 0x3e, 0xff, 0xff, 0xe3, 0x0,
    0x0, 0xef, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x8f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8, 0x0, 0x0,
    0x23, 0x32, 0x8f, 0xf8, 0x23, 0x32, 0xff, 0xfd,
    0x39, 0x93, 0xef, 0xff, 0xff, 0xff, 0xc9, 0x9c,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5c, 0x8f,
    0x9a, 0xaa, 0xaa, 0xaa, 0xaa, 0xa8,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0x62, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xfd, 0x0, 0x0, 0x1,
    0x0, 0x9, 0xff, 0x40, 0x1, 0x8e, 0xe1, 0x1a,
    0xff, 0x70, 0x0, 0xef, 0xff, 0xde, 0xff, 0x90,
    0x0, 0xc, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x8f, 0xff, 0xe9, 0x10, 0x0, 0x0, 0x2, 0x76,
    0x30, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C4 "" */
    0x7, 0x93, 0x0, 0x0, 0x22, 0xa, 0xff, 0xf2,
    0x0, 0x8f, 0xf5, 0xf9, 0x1f, 0x70, 0x8f, 0xf9,
    0xc, 0xfc, 0xf8, 0x8f, 0xf9, 0x0, 0x1a, 0xef,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0xef, 0xfc, 0x0,
    0x0, 0x7, 0xbf, 0xff, 0xf6, 0x0, 0xa, 0xff,
    0xfa, 0xbf, 0xf6, 0x0, 0xf9, 0x1f, 0x70, 0xbf,
    0xf6, 0xc, 0xfc, 0xf4, 0x0, 0xbf, 0xf4, 0x1a,
    0xc6, 0x0, 0x0, 0x56, 0x0,

    /* U+F0C5 "" */
    0x0, 0x3, 0x44, 0x41, 0x20, 0x0, 0x0, 0xff,
    0xff, 0x5e, 0x40, 0x24, 0x1f, 0xff, 0xf5, 0xee,
    0x2f, 0xf4, 0xff, 0xff, 0xc8, 0x82, 0xff, 0x4f,
    0xff, 0xff, 0xff, 0x5f, 0xf4, 0xff, 0xff, 0xff,
    0xf5, 0xff, 0x4f, 0xff, 0xff, 0xff, 0x5f, 0xf4,
    0xff, 0xff, 0xff, 0xf5, 0xff, 0x4f, 0xff, 0xff,
    0xff, 0x5f, 0xf4, 0xff, 0xff, 0xff, 0xf4, 0xff,
    0x93, 0x44, 0x44, 0x43, 0xf, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x68, 0x88, 0x88, 0x71, 0x0, 0x0,

    /* U+F0C7 "" */
    0x48, 0x88, 0x88, 0x87, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0xf8, 0x0, 0x0, 0xb, 0xfb,
    0xf, 0x80, 0x0, 0x0, 0xbf, 0xf3, 0xfb, 0x77,
    0x77, 0x7d, 0xff, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0xff, 0xff, 0x42, 0xdf, 0xff, 0x4f, 0xff,
    0xc0, 0x8, 0xff, 0xf4, 0xff, 0xfe, 0x0, 0xaf,
    0xff, 0x4f, 0xff, 0xfc, 0xaf, 0xff, 0xf4, 0xaf,
    0xff, 0xff, 0xff, 0xfd, 0x10,

    /* U+F0C9 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0x9a, 0xaa, 0xaa, 0xaa, 0xaa,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x89, 0x99,
    0x99, 0x99, 0x99, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x12, 0x22, 0x22, 0x22, 0x22, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9a, 0xaa, 0xaa, 0xaa,
    0xaa, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0E0 "" */
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x4e, 0xff, 0xff, 0xff,
    0xff, 0xe4, 0xc3, 0xbf, 0xff, 0xff, 0xfb, 0x3c,
    0xff, 0x57, 0xff, 0xff, 0x75, 0xff, 0xff, 0xf9,
    0x3d, 0xd3, 0x9f, 0xff, 0xff, 0xff, 0xd5, 0x5d,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb,

    /* U+F0E7 "" */
    0x1, 0xbb, 0xba, 0x10, 0x0, 0x5f, 0xff, 0xf1,
    0x0, 0x7, 0xff, 0xfb, 0x0, 0x0, 0x9f, 0xff,
    0x60, 0x0, 0xb, 0xff, 0xff, 0xff, 0x60, 0xef,
    0xff, 0xff, 0xf1, 0xe, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xc, 0xfe, 0x0, 0x0, 0x0, 0xff, 0x50,
    0x0, 0x0, 0x3f, 0xc0, 0x0, 0x0, 0x7, 0xf3,
    0x0, 0x0, 0x0, 0xa9, 0x0, 0x0, 0x0, 0x2,
    0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x2a, 0x50, 0x0, 0x0, 0xe, 0xff, 0x8f,
    0xff, 0x20, 0x0, 0xff, 0xf8, 0xff, 0xf4, 0x0,
    0xf, 0xff, 0xeb, 0xbb, 0x30, 0x0, 0xff, 0xf4,
    0x99, 0x92, 0x60, 0xf, 0xff, 0x5f, 0xff, 0x4f,
    0xa0, 0xff, 0xf5, 0xff, 0xf5, 0x56, 0x1f, 0xff,
    0x5f, 0xff, 0xff, 0xf4, 0xff, 0xf5, 0xff, 0xff,
    0xff, 0x4e, 0xff, 0x5f, 0xff, 0xff, 0xf4, 0x0,
    0x5, 0xff, 0xff, 0xff, 0x40, 0x0, 0x5f, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x44, 0x44, 0x44, 0x0,

    /* U+F0F3 "" */
    0x0, 0x0, 0x15, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xf1, 0x0, 0x0, 0x0, 0x2d, 0xff, 0xf9, 0x0,
    0x0, 0xe, 0xff, 0xff, 0xf7, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xd0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xf2, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0x50, 0x6f, 0xff, 0xff, 0xff,
    0xfd, 0xe, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x24,
    0x44, 0x44, 0x44, 0x43, 0x0, 0x0, 0x2f, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x46, 0x0, 0x0, 0x0,

    /* U+F11C "" */
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xfc,
    0x8e, 0x8e, 0x8e, 0x88, 0xe8, 0xf7, 0xf8, 0xc,
    0xc, 0xb, 0x0, 0xb0, 0xf8, 0xff, 0xec, 0xfc,
    0xec, 0xee, 0xcf, 0xf8, 0xff, 0xa0, 0xc0, 0xa0,
    0x77, 0x2f, 0xf8, 0xff, 0xec, 0xfc, 0xec, 0xee,
    0xcf, 0xf8, 0xf8, 0xc, 0x0, 0x0, 0x0, 0xb0,
    0xf8, 0xfc, 0x8e, 0x88, 0x88, 0x88, 0xe8, 0xf7,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x18, 0xef, 0xe0, 0x0, 0x0,
    0x0, 0x29, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x3a,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x4c, 0xff, 0xff,
    0xff, 0xfc, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x0, 0x1, 0x34, 0x44, 0xdf, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x26,
    0x0, 0x0, 0x0,

    /* U+F15B "" */
    0x9b, 0xbb, 0xb2, 0x70, 0xf, 0xff, 0xff, 0x4f,
    0x90, 0xff, 0xff, 0xf4, 0xff, 0x9f, 0xff, 0xff,
    0x54, 0x44, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x34, 0x44,
    0x44, 0x44, 0x30,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0x9b, 0xcb, 0x95, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0x80, 0x3, 0xef,
    0xfa, 0x53, 0x23, 0x5a, 0xff, 0xe3, 0xdf, 0xa1,
    0x0, 0x0, 0x0, 0x1, 0xaf, 0xd2, 0x60, 0x5,
    0xbe, 0xfe, 0xb5, 0x0, 0x52, 0x0, 0x1c, 0xff,
    0xfe, 0xff, 0xfc, 0x10, 0x0, 0x2, 0xec, 0x40,
    0x0, 0x4c, 0xe2, 0x0, 0x0, 0x1, 0x0, 0x1,
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xd6, 0x0,
    0x0, 0x0,

    /* U+F240 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x34, 0x44, 0x44, 0x44, 0x44, 0x4f, 0xdf, 0x8c,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0xcf, 0xf8, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0x8, 0xff, 0x89, 0xcc,
    0xcc, 0xcc, 0xcc, 0xc3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F241 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x34, 0x44, 0x44, 0x43, 0x0, 0x4f, 0xdf, 0x8c,
    0xff, 0xff, 0xff, 0xc0, 0x2, 0xcf, 0xf8, 0xcf,
    0xff, 0xff, 0xfc, 0x0, 0x8, 0xff, 0x89, 0xcc,
    0xcc, 0xcc, 0x90, 0x3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F242 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x34, 0x44, 0x42, 0x0, 0x0, 0x4f, 0xdf, 0x8c,
    0xff, 0xff, 0x80, 0x0, 0x2, 0xcf, 0xf8, 0xcf,
    0xff, 0xf8, 0x0, 0x0, 0x8, 0xff, 0x89, 0xcc,
    0xcc, 0x60, 0x0, 0x3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F243 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x34, 0x41, 0x0, 0x0, 0x0, 0x4f, 0xdf, 0x8c,
    0xff, 0x40, 0x0, 0x0, 0x2, 0xcf, 0xf8, 0xcf,
    0xf4, 0x0, 0x0, 0x0, 0x8, 0xff, 0x89, 0xcc,
    0x30, 0x0, 0x0, 0x3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F244 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xdf, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xcf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x25, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xcb, 0xfe, 0x0, 0x0, 0x0,
    0x1, 0x0, 0xd, 0x10, 0x42, 0x0, 0x0, 0x0,
    0x9f, 0xd1, 0x68, 0x0, 0x0, 0x0, 0x68, 0x0,
    0xff, 0xfe, 0xee, 0xed, 0xdd, 0xdd, 0xef, 0xc0,
    0x9f, 0xd1, 0x0, 0xb3, 0x0, 0x0, 0x68, 0x0,
    0x1, 0x0, 0x0, 0x3b, 0x5, 0x74, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xbe, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2d, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x34, 0x20, 0x0, 0x0, 0x6e, 0xfe,
    0xfd, 0x20, 0x4, 0xff, 0xf3, 0xff, 0xd0, 0xc,
    0xff, 0xf0, 0x4f, 0xf5, 0xf, 0xd5, 0xf2, 0x95,
    0xf8, 0x2f, 0xf7, 0x41, 0x3c, 0xfa, 0x3f, 0xff,
    0x60, 0xaf, 0xfb, 0x3f, 0xfe, 0x20, 0x4f, 0xfb,
    0x2f, 0xe2, 0x92, 0x75, 0xfa, 0xf, 0xeb, 0xf1,
    0x49, 0xf8, 0x9, 0xff, 0xf0, 0x9f, 0xf2, 0x1,
    0xdf, 0xf9, 0xff, 0x90, 0x0, 0x6, 0xab, 0x95,
    0x0,

    /* U+F2ED "" */
    0x0, 0x4, 0x88, 0x70, 0x0, 0xb, 0xcc, 0xff,
    0xff, 0xdc, 0xc5, 0xbc, 0xcc, 0xcc, 0xcc, 0xcc,
    0x52, 0x88, 0x88, 0x88, 0x88, 0x60, 0x4f, 0xff,
    0xff, 0xff, 0xfc, 0x4, 0xfa, 0xae, 0x6f, 0x5f,
    0xc0, 0x4f, 0xaa, 0xe6, 0xf4, 0xfc, 0x4, 0xfa,
    0xae, 0x6f, 0x4f, 0xc0, 0x4f, 0xaa, 0xe6, 0xf4,
    0xfc, 0x4, 0xfa, 0xae, 0x6f, 0x4f, 0xc0, 0x4f,
    0xaa, 0xe6, 0xf5, 0xfc, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0x6, 0x88, 0x88, 0x88, 0x72, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x71, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xef, 0xd1, 0x0, 0x0, 0x0,
    0x1, 0x5f, 0xff, 0xc0, 0x0, 0x0, 0x1, 0xea,
    0x5f, 0xfd, 0x0, 0x0, 0x1, 0xef, 0xfa, 0x5d,
    0x10, 0x0, 0x1, 0xef, 0xff, 0xf8, 0x0, 0x0,
    0x1, 0xef, 0xff, 0xfe, 0x20, 0x0, 0x1, 0xef,
    0xff, 0xfe, 0x20, 0x0, 0x1, 0xef, 0xff, 0xfe,
    0x20, 0x0, 0x0, 0xbf, 0xff, 0xfe, 0x20, 0x0,
    0x0, 0xd, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0,
    0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x6, 0x64,
    0x10, 0x0, 0x0, 0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x5, 0xef, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5,
    0xff, 0xff, 0x91, 0xdd, 0x19, 0xff, 0xf5, 0xff,
    0xff, 0xfd, 0x11, 0x11, 0xdf, 0xff, 0xef, 0xff,
    0xff, 0xfb, 0x0, 0xbf, 0xff, 0xf5, 0xff, 0xff,
    0xfd, 0x11, 0x11, 0xdf, 0xff, 0x5, 0xff, 0xff,
    0x91, 0xdd, 0x19, 0xff, 0xf0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x4, 0xef, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+F7C2 "" */
    0x0, 0x17, 0x88, 0x87, 0x20, 0x2d, 0xff, 0xff,
    0xfd, 0x2e, 0xa0, 0xb3, 0x78, 0xfe, 0xfa, 0xb,
    0x37, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xfc, 0x4, 0x44,
    0x44, 0x44, 0x0,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xf0, 0x0, 0x69, 0x0,
    0x0, 0x0, 0xdf, 0x0, 0x7f, 0xc0, 0x0, 0x0,
    0xd, 0xf0, 0x8f, 0xff, 0xdd, 0xdd, 0xdd, 0xff,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xb,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 49, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 59, .box_w = 2, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 10, .adv_w = 72, .box_w = 4, .box_h = 5, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 20, .adv_w = 110, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 55, .adv_w = 106, .box_w = 6, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 94, .adv_w = 177, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 155, .adv_w = 150, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 200, .adv_w = 38, .box_w = 2, .box_h = 5, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 205, .adv_w = 70, .box_w = 4, .box_h = 14, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 233, .adv_w = 70, .box_w = 4, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 261, .adv_w = 92, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 279, .adv_w = 111, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 304, .adv_w = 63, .box_w = 3, .box_h = 4, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 310, .adv_w = 66, .box_w = 4, .box_h = 1, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 312, .adv_w = 63, .box_w = 2, .box_h = 2, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 314, .adv_w = 68, .box_w = 5, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 347, .adv_w = 107, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 382, .adv_w = 90, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 412, .adv_w = 107, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 447, .adv_w = 107, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 477, .adv_w = 107, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 512, .adv_w = 107, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 547, .adv_w = 107, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 582, .adv_w = 105, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 612, .adv_w = 107, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 647, .adv_w = 108, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 682, .adv_w = 63, .box_w = 2, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 689, .adv_w = 63, .box_w = 3, .box_h = 9, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 703, .adv_w = 111, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 731, .adv_w = 111, .box_w = 7, .box_h = 4, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 745, .adv_w = 111, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 773, .adv_w = 83, .box_w = 5, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 801, .adv_w = 175, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 867, .adv_w = 138, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 912, .adv_w = 129, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 952, .adv_w = 133, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 992, .adv_w = 148, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1037, .adv_w = 125, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1077, .adv_w = 121, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1117, .adv_w = 142, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1162, .adv_w = 164, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1212, .adv_w = 78, .box_w = 5, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1237, .adv_w = 77, .box_w = 6, .box_h = 12, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 1273, .adv_w = 140, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1318, .adv_w = 120, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1358, .adv_w = 187, .box_w = 12, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1418, .adv_w = 153, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1468, .adv_w = 147, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1513, .adv_w = 123, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1553, .adv_w = 147, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1612, .adv_w = 137, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1657, .adv_w = 109, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1692, .adv_w = 127, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1732, .adv_w = 153, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1782, .adv_w = 137, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1827, .adv_w = 202, .box_w = 13, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1892, .adv_w = 134, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1937, .adv_w = 132, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1982, .adv_w = 116, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2017, .adv_w = 66, .box_w = 3, .box_h = 13, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 2037, .adv_w = 68, .box_w = 5, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2070, .adv_w = 66, .box_w = 3, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2090, .adv_w = 111, .box_w = 5, .box_h = 5, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 2103, .adv_w = 108, .box_w = 8, .box_h = 1, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 2107, .adv_w = 84, .box_w = 3, .box_h = 3, .ofs_x = 1, .ofs_y = 8},
    {.bitmap_index = 2112, .adv_w = 107, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2137, .adv_w = 122, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2181, .adv_w = 103, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2202, .adv_w = 121, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2246, .adv_w = 105, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2271, .adv_w = 74, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2307, .adv_w = 109, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2342, .adv_w = 127, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2386, .adv_w = 64, .box_w = 4, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2406, .adv_w = 60, .box_w = 4, .box_h = 13, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 2432, .adv_w = 117, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2476, .adv_w = 64, .box_w = 4, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2498, .adv_w = 187, .box_w = 12, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2540, .adv_w = 127, .box_w = 8, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2568, .adv_w = 114, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2593, .adv_w = 122, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2633, .adv_w = 117, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2673, .adv_w = 89, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2694, .adv_w = 91, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2715, .adv_w = 70, .box_w = 5, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2738, .adv_w = 125, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2770, .adv_w = 105, .box_w = 8, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 2798, .adv_w = 161, .box_w = 11, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 2837, .adv_w = 108, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2862, .adv_w = 106, .box_w = 8, .box_h = 10, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 2902, .adv_w = 96, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2923, .adv_w = 72, .box_w = 5, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2956, .adv_w = 61, .box_w = 2, .box_h = 15, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 2971, .adv_w = 72, .box_w = 4, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2997, .adv_w = 111, .box_w = 7, .box_h = 2, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 3004, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3082, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3136, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3202, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3256, .adv_w = 132, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3297, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3375, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3453, .adv_w = 216, .box_w = 14, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3530, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3608, .adv_w = 216, .box_w = 14, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3671, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3749, .adv_w = 96, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3779, .adv_w = 144, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3824, .adv_w = 216, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3915, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3969, .adv_w = 132, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4028, .adv_w = 168, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 4076, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4148, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4209, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4270, .adv_w = 168, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 4318, .adv_w = 168, .box_w = 12, .box_h = 11, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 4384, .adv_w = 120, .box_w = 7, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4423, .adv_w = 120, .box_w = 7, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4462, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4523, .adv_w = 168, .box_w = 11, .box_h = 3, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 4540, .adv_w = 216, .box_w = 14, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4603, .adv_w = 240, .box_w = 16, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4707, .adv_w = 216, .box_w = 15, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 4805, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4871, .adv_w = 168, .box_w = 11, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 4910, .adv_w = 168, .box_w = 11, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 4949, .adv_w = 240, .box_w = 16, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 5029, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5083, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5161, .adv_w = 192, .box_w = 13, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5246, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5307, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5379, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5440, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5501, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5555, .adv_w = 120, .box_w = 9, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5614, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5686, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5758, .adv_w = 216, .box_w = 14, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5821, .adv_w = 192, .box_w = 14, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5912, .adv_w = 144, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5971, .adv_w = 240, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6061, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6129, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6197, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6265, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6333, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6401, .adv_w = 240, .box_w = 16, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6489, .adv_w = 168, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6554, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6626, .adv_w = 192, .box_w = 13, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 6711, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6779, .adv_w = 144, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6838, .adv_w = 193, .box_w = 13, .box_h = 9, .ofs_x = 0, .ofs_y = 0}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x7, 0xa, 0xb, 0xc, 0x10, 0x12, 0x14,
    0x18, 0x1b, 0x20, 0x25, 0x26, 0x27, 0x3d, 0x42,
    0x47, 0x4a, 0x4b, 0x4c, 0x50, 0x51, 0x52, 0x53,
    0x66, 0x67, 0x6d, 0x6f, 0x70, 0x73, 0x76, 0x77,
    0x78, 0x7a, 0x92, 0x94, 0xc3, 0xc4, 0xc6, 0xc8,
    0xdf, 0xe6, 0xe9, 0xf2, 0x11b, 0x123, 0x15a, 0x1ea,
    0x23f, 0x240, 0x241, 0x242, 0x243, 0x286, 0x292, 0x2ec,
    0x303, 0x559, 0x7c1, 0x8a1
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 61441, .range_length = 2210, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 60, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 0, 1, 2, 0, 0, 3,
    1, 4, 5, 6, 0, 7, 8, 7,
    9, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 10, 10, 0, 0, 0,
    11, 12, 13, 14, 15, 16, 17, 18,
    19, 20, 20, 21, 22, 23, 20, 24,
    25, 26, 25, 27, 28, 29, 30, 31,
    32, 33, 34, 35, 4, 36, 5, 0,
    37, 0, 38, 39, 40, 41, 42, 43,
    44, 45, 46, 47, 48, 41, 45, 45,
    39, 39, 49, 50, 51, 52, 53, 54,
    54, 55, 54, 56, 4, 0, 5, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 0, 1, 2, 3, 0, 0, 4,
    2, 5, 6, 7, 0, 8, 9, 10,
    11, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 12, 13, 0, 0, 0,
    14, 15, 16, 17, 18, 17, 17, 17,
    18, 17, 17, 19, 17, 17, 20, 20,
    18, 17, 18, 17, 21, 22, 23, 24,
    25, 26, 27, 28, 5, 29, 6, 0,
    30, 0, 31, 32, 33, 33, 33, 34,
    35, 32, 36, 37, 32, 32, 38, 38,
    33, 39, 33, 38, 40, 41, 42, 43,
    43, 44, 45, 46, 5, 0, 6, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 0, 0, 0, 0, 0, 0, -22,
    0, -22, -17, 0, 0, 0, 0, -26,
    0, -5, -2, 0, -2, 6, -1, 0,
    0, 0, 0, 0, 0, 0, -6, 0,
    -8, -1, -11, 0, 0, -2, -4, -6,
    0, 0, 0, 0, 0, -2, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -6, 0, 0, 0, 0, -8, 0, 3,
    0, 0, 0, 0, 0, -4, -4, 0,
    -1, 0, 0, 0, 0, 0, 0, -1,
    -1, 0, 0, 0, 0, 0, 2, 0,
    1, 0, 1, 0, 0, 0, 0, 0,
    0, -4, 0, -1, -5, -1, 0, 0,
    0, 0, 0, 0, 0, 0, -2, 0,
    0, -11, -6, -13, -12, 0, -12, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -4, -4, -10, 2,
    0, 0, -8, 0, 0, -8, -8, 0,
    0, -6, 0, -4, 7, 0, -2, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -6, -1, 0, 0, 20, -3,
    0, -4, -2, -4, -4, -2, 3, -2,
    0, 0, 0, 0, 0, -10, 0, -8,
    0, -8, 0, 0, 0, 0, 0, -6,
    0, 0, 0, 0, 0, -4, -4, -10,
    -8, -4, -8, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -21, 0, -21,
    0, 0, 0, 0, 0, -23, 0, -2,
    -4, 0, 0, 4, 0, 0, 0, -1,
    0, 0, 0, 0, -8, 0, -5, -2,
    -9, 0, 0, -1, -2, -5, 0, 0,
    0, 0, 0, -3, 0, -17, 0, 0,
    -10, -9, -27, 0, -8, 0, 0, 0,
    0, 0, 0, 0, 0, -11, 0, 0,
    0, -17, -12, -28, -25, 0, -24, 0,
    -27, 0, -4, 0, -6, 0, -4, 0,
    2, 0, -6, -3, -8, -9, -19, 0,
    -8, -4, 0, 0, 0, 0, 0, -8,
    0, -10, 0, -10, -6, 0, 0, 0,
    0, -6, 0, 2, -4, -2, 0, -8,
    -2, -16, -14, -8, -15, -2, -8, 0,
    0, -1, 2, -1, 1, 0, 0, 0,
    0, 0, 0, 0, -1, -4, -2, 0,
    0, 0, -4, 0, 0, 0, 0, -28,
    -12, -28, -16, 0, 0, 0, 0, -15,
    0, 0, 0, 0, 0, 1, 0, 2,
    2, 0, 0, 0, 0, 0, -7, 0,
    -6, 0, -7, 0, 0, -4, -4, -4,
    0, -3, 0, -2, 0, -4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -1, -2, -6, -6, -1,
    -10, 0, 0, 0, 0, 0, -1, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -6, 0, -23, 0, -23, -15, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -3, 0, -3, 0, 0, 0, 0,
    0, -1, 0, 1, 0, 0, 0, 0,
    0, -1, -1, -1, -1, 0, 0, -17,
    0, -2, 0, -1, -2, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, 1, 0,
    0, -23, 0, 0, -4, -6, -22, 0,
    -7, 0, 0, 0, 0, 0, 0, 2,
    -2, -7, -2, -1, 0, -12, -14, -25,
    -21, 0, -19, 0, -12, 0, -1, 0,
    -4, 0, -1, 0, -2, 0, -6, 0,
    -4, -4, -11, 0, -9, 0, 0, -4,
    0, 0, 0, -3, -2, -6, 0, -6,
    -4, 0, 0, 0, 0, -6, -5, -1,
    -2, -3, 0, -4, -6, -10, -8, -6,
    -10, -1, -3, -5, 0, -2, 0, -3,
    0, 0, 0, 0, -2, 0, 0, -3,
    -3, -2, -2, 0, 0, 4, 0, 0,
    0, 1, 1, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, 0, -2, -2, -2, 0, -4, 0,
    0, -1, 0, 0, 0, 0, 0, 0,
    0, 0, -2, 0, 0, -2, 0, 0,
    0, 0, 0, -1, 0, 0, 0, -4,
    0, -9, 1, -9, -6, 0, 0, 0,
    3, -11, -5, 2, -2, -4, 0, -4,
    -4, -9, -10, -8, -12, -2, -3, -15,
    0, -2, 0, 0, -2, 0, 0, 0,
    -2, -2, 0, 0, 0, -2, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -3, -3,
    -3, 0, -3, 0, 0, 0, 0, 0,
    0, 0, -1, 0, 0, 0, -4, 0,
    0, -2, -4, 0, -4, 0, 0, 2,
    0, -2, -2, 0, 2, -20, -4, -20,
    -12, -1, -1, 0, -4, -15, -2, -2,
    0, 0, 0, 2, 0, 0, 0, 0,
    0, 0, 3, -25, -8, -1, -9, -6,
    -13, -2, 0, -4, -8, -9, 0, -4,
    -4, -6, -4, -6, 0, 0, 0, 0,
    2, -1, 0, -2, 4, -2, -3, 0,
    0, 0, 2, -2, -2, 2, 0, 0,
    0, -2, -3, -7, -7, -4, -9, -2,
    0, -8, 0, 0, 0, 0, 0, 0,
    0, 0, -2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -4, 0, -5, -3, -3, 0, 0, 0,
    -7, -4, -2, -5, -8, 0, -6, 0,
    0, 0, 0, 0, 0, 0, 0, -11,
    -2, -11, -6, -4, -4, 0, -3, -8,
    0, -2, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, 0, -15, -5, 0,
    -5, -8, -6, -2, -2, -5, -8, -6,
    -4, -6, -6, -4, -6, -4, 0, 0,
    0, 0, 0, 0, 0, 0, -6, 0,
    0, 0, 0, 0, 0, 2, 0, -6,
    -3, 0, 0, 0, -2, -2, -2, 0,
    -2, 2, 0, 0, -2, 0, -4, 0,
    -2, 0, 0, 0, -6, 0, -4, -4,
    -11, 0, -10, 0, 0, -27, 0, 0,
    0, -5, -21, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, -3, -3, -1,
    -3, -15, -9, -22, -19, -3, -21, -3,
    -11, 0, 0, 0, 0, -1, 0, 0,
    0, 0, -3, 0, -2, -4, -8, 0,
    -6, 0, 0, 0, 0, 0, 0, 0,
    0, -6, -1, -6, -6, -3, -3, 0,
    0, -7, -2, -3, 0, 0, -2, 0,
    -2, 0, 0, -2, 0, -1, 0, -11,
    -3, 0, -2, -4, -4, 0, 0, -2,
    -4, -2, -2, -4, -4, -4, -4, -4,
    0, -5, 0, 0, 0, -4, -2, -8,
    2, -8, -4, 0, 0, 0, 4, -7,
    -4, 2, -2, -3, 0, -2, -4, -7,
    -6, -6, -8, 0, -1, -11, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 2,
    0, -1, 0, -2, 4, -21, -1, -21,
    -10, 2, 2, 0, 0, -13, -2, 0,
    -2, 0, 1, 2, -4, -2, -2, -4,
    -4, 2, 2, -30, -1, -1, -4, 0,
    -4, 0, 0, -1, 0, -1, 3, 0,
    2, 0, 3, 0, 0, -8, 0, 0,
    0, 0, -3, 0, -3, 0, 0, 0,
    0, 0, 0, 2, 0, -4, -2, 0,
    0, -6, -7, -11, -10, 1, -10, 1,
    -4, 3, 0, 0, -2, 0, 0, 0,
    0, 0, -3, 0, -2, -3, -4, 0,
    -4, 0, 0, 0, 0, 0, 0, -2,
    0, -4, 0, -4, -3, 0, 0, 0,
    1, -4, -1, 0, -2, -1, -2, -2,
    -5, -6, -5, -2, -6, 0, 0, -8,
    0, -1, 0, -3, -1, 0, 0, 0,
    -2, 0, -2, -2, -4, -2, -4, 0,
    0, 6, 0, 0, -4, 0, 4, -17,
    -8, -17, -10, -1, -1, 0, -4, -13,
    0, -2, 0, 0, 0, 2, -2, 0,
    0, -1, 0, 1, 5, -17, -8, 0,
    -14, -2, -8, 0, 0, -6, -14, -10,
    0, -6, -8, -7, -8, -12, 0, 0,
    0, -3, -4, -2, 0, -17, -3, -17,
    -12, -3, -3, 0, 0, -15, -4, -5,
    -3, -2, -4, -2, -4, -3, -1, -2,
    0, -2, 0, -17, -8, -1, -6, -4,
    -8, -4, -2, -5, -9, -8, -4, -6,
    -5, -7, -6, -7, 0, 0, 0, -8,
    -10, 0, 0, -32, -15, -32, -18, -8,
    -8, 0, -10, -23, 0, -9, 0, 0,
    -4, 0, -2, 0, 0, 0, 0, -2,
    2, -33, -17, -1, -15, -10, -17, -6,
    -4, -12, -15, -18, -6, -12, -8, -15,
    -12, -13, 0, 0, 0, -6, -8, 0,
    0, -26, -13, -26, -19, -8, -8, 0,
    -10, -22, 0, -8, 0, -1, -4, 0,
    -2, 0, 0, 0, 0, -2, 2, -27,
    -15, -1, -16, -8, -17, -4, -4, -10,
    -15, -15, -5, -11, -9, -11, -9, -13,
    0, 0, 0, 0, -4, 0, -1, 0,
    -9, 0, 0, -1, -1, 0, 0, 0,
    0, -6, 0, 0, 0, -2, -2, 0,
    0, 0, 0, 1, 0, 0, -1, 0,
    -6, 0, 0, 0, -3, 0, -7, -2,
    -6, -8, -13, 0, -10, 0, 0, 0,
    0, -8, -10, 0, 0, -22, -17, -22,
    -19, -12, -12, 0, -10, -18, 0, -10,
    0, 0, -8, 0, 0, 0, 0, 0,
    0, 0, 3, -23, -16, -1, -19, -12,
    -20, -8, -5, -14, -19, -17, -12, -17,
    -15, -13, -15, -18, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, -1, 0, 0,
    0, -2, -4, -2, -2, 0, -2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -2, 0, 0, -3, -5, 0,
    -5, 0, 0, -17, 0, 0, 0, 0,
    0, 3, -4, 3, 0, 0, 0, 0,
    0, 0, 0, -6, 15, 0, 0, -12,
    -11, -20, -17, 0, -17, 0, -16, 0,
    0, 0, 0, 0, 1, 0, 18, 0,
    0, 0, -2, -1, -4, 0, 6, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -11, 7, 0, -4, -17, -21, -31,
    -29, 0, -23, 0, 0, 0, -3, 0,
    -10, 0, 0, 0, 0, 0, -4, -4,
    -10, -10, -15, 0, -12, 0, 0, -13,
    0, 0, 0, 0, -9, 0, -2, 0,
    0, 0, 0, -1, 0, 1, -1, -3,
    -4, 0, 0, -11, -11, -20, -19, 0,
    -17, 0, -9, 0, 0, 0, 0, 0,
    -2, 0, 0, -1, -4, 0, 0, 0,
    -4, 0, -2, 0, 0, -8, 0, 0,
    0, -3, -5, -5, 2, -5, -4, -1,
    -1, -1, 0, -8, -4, 0, -5, -2,
    -1, -12, -8, -16, -13, -4, -16, 0,
    -4, -10, 0, -1, 0, 0, 0, 0,
    0, 0, -2, 0, -1, -1, 0, -4,
    -2, 0, 0, -1, 0, 0, 0, 0,
    -1, -1, 0, -1, 0, 0, 0, 0,
    0, -2, -3, 0, -4, 0, 0, -8,
    -6, -14, -12, -4, -12, 0, -1, -5,
    0, -2, 0, 0, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, -4, 0, -2, 0, -1, -4, 0,
    -1, 0, 0, 0, 0, -1, 0, 0,
    0, 0, -1, 0, 0, -4, -5, -8,
    -7, 0, -9, 0, -1, 0, -2, 0,
    0, 0, -2, 0, 0, 0, -3, 0,
    0, -2, -2, 0, -2, 0, 0, -4,
    0, 0, 0, -3, -4, -6, 0, -6,
    -2, 0, 0, -1, 0, -4, -2, 0,
    -4, 0, 0, -10, -6, -14, -12, -4,
    -15, 0, -4, -8, -1, -2, 0, -1,
    0, 0, 0, 0, -2, 0, 0, 0,
    -1, 0, -1, 0, 8, 16, 0, 0,
    0, 19, 11, -7, -2, -7, -3, 0,
    0, 7, 0, -1, 3, 0, 5, 8,
    5, 11, 8, 11, 11, 10, 11, 8,
    20, -4, 0, -2, -3, 0, -1, 5,
    5, 0, 0, 0, 0, -2, 2, 0,
    2, 0, 0, 0, 0, -4, 0, 0,
    0, 0, -1, 0, 3, 0, 0, -1,
    0, 0, -1, 0, 10, -1, 0, -5,
    -7, -8, -8, 0, -14, 0, -3, 0,
    -4, -2, -2, 0, -2, 0, 4, 0,
    0, 0, 0, 0, 1, 0, 2, 0,
    0, -11, 0, 0, 0, -3, -14, 0,
    0, 0, 0, 0, 0, -1, 0, 0,
    -1, -1, -4, 0, 0, -12, -7, -18,
    -16, 0, -15, 0, -10, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -3, 0,
    -1, -1, -5, 0, -5, 0, -1, -1,
    0, -1, 0, 0, -1, 0, 0, 0,
    0, 0, 0, -1, 0, 0, -1, 0,
    0, 0, 0, -4, -4, -4, -4, 0,
    -4, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    -3, 0, -3, 0, 0, -1, 0, -1,
    0, 0, -1, -3, 0, -3, -2, -3,
    -3, -1, 0, -3, -1, -1, 0, 0,
    0, 0, -5, -3, -3, -2, -4, 0,
    0, -7, 0, -1, 0, 0, -2, 0,
    0, 0, -1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -2,
    -1, 0, -4, 0, 0, -1, -1, -1,
    0, 3, -1, -1, -4, 0, 0, -8,
    -8, -14, -11, 1, -15, 2, -1, 0,
    0, 0, -4, 0, 0, 0, 0, 0,
    -2, -1, 0, 0, 0, 0, 0, 0,
    0, -5, 0, -1, 0, 2, -4, 6,
    0, 2, 2, 0, 6, -1, 0, -3,
    -4, 0, 6, -3, 0, -10, -9, -17,
    -14, -1, -17, 0, -3, -1, -2, -1,
    0, 0, 0, 0, 7, 0, 0, 0,
    0, 0, 0, 0, 4, 0, 0, 1,
    0, -2, 0, -5, 3, -18, -6, -18,
    -10, -2, -2, -1, -2, -12, -5, 0,
    -6, -3, 0, -1, -6, -10, -8, -11,
    -10, 0, 0, -14, -2, -2, -3, 0,
    -3, 0, 0, 0, 0, -2, 3, 0,
    2, 0, 2, 0, 0, -4, 0, 0,
    0, -3, -3, -4, 0, -4, 0, 0,
    0, 0, 0, -2, -4, 0, -5, -2,
    0, -7, -5, -15, -12, 0, -18, 0,
    -5, -8, 0, -2, 0, -1, 0, 0,
    0, 0, -2, 0, 0, 0, 0, 0,
    0, 0, 0, 1, 0, 0, 0, 0,
    0, 0, -2, 0, 0, 0, 0, 0,
    0, 3, 0, 0, -2, 0, 0, 0,
    -2, -8, -6, 0, -9, 1, 0, 2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 2, 0, 0, 0,
    0, -8, 0, 0, 0, -4, -7, 0,
    0, 0, 0, -2, -2, 0, 0, 0,
    0, -2, -6, 0, 0, -8, -8, -16,
    -15, 0, -15, 0, -4, 0, 0, 0,
    -1, 0, -2, 0, -1, 0, -2, 0,
    0, 0, -4, 0, -4, 0, 0, -1,
    0, -3, 0, -7, -1, -19, -3, -19,
    -8, -1, -1, -1, -2, -14, -5, 0,
    -8, -4, 0, -3, -5, -10, -9, -10,
    -11, -1, 0, -16, -3, -4, -4, 0,
    -4, 0, 0, 0, 0, -2, 1, 0,
    2, 0, 2, 0, 0, 0, 0, 0,
    0, -3, 0, 0, -5, 0, 0, 0,
    0, 0, 0, 1, 0, -2, -4, 0,
    0, -6, -6, -15, -10, 0, -13, 0,
    0, 0, -2, 0, -4, 0, -2, 0,
    0, 0, -4, 0, 0, 0, 0, 1,
    0, 0, 0, 0, 0, 0, 0, -2,
    -1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -3,
    -5, -12, -11, 0, -12, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, 0, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 56,
    .right_class_cnt     = 46,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t lv_font_SourceHanSerifSC_Regular_12 = {
#else
lv_font_t lv_font_SourceHanSerifSC_Regular_12 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 12,          /*The maximum line height required by the font*/
    .base_line = 1,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_FONT_SOURCEHANSERIFSC_REGULAR_12*/

