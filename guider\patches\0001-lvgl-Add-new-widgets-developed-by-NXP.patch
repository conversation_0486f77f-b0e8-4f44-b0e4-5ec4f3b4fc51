From 1183d0a8ef2cb58fe6949bc2a868b55baaf134a1 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
Date: Mon, 17 Jun 2024 17:28:19 +0800
Subject: [PATCH] lvgl: Add new widgets developed by NXP

Signed-off-by: <PERSON><PERSON><PERSON><PERSON> Yu <<EMAIL>>
---
 Kconfig                                       |   44 +
 .../widgets/animimg/lv_example_animimg_1.c    |    2 +-
 .../widgets/animimg/lv_example_animimg_1.py   |    2 +-
 lv_conf_template.h                            |   25 +
 src/extra/libs/barcode/code128.c              |  582 ++++++++
 src/extra/libs/barcode/code128.h              |   47 +
 src/extra/libs/barcode/lv_barcode.c           |  215 +++
 src/extra/libs/barcode/lv_barcode.h           |  108 ++
 src/extra/libs/fsdrv/lv_fs_rawfs.c            |  322 +++++
 src/extra/libs/fsdrv/lv_fsdrv.h               |   16 +
 src/extra/libs/lv_libs.h                      |    1 +
 src/extra/lv_extra.c                          |    4 +
 src/extra/others/ime/lv_ime_pinyin.c          |    2 +-
 src/extra/themes/default/lv_theme_default.c   |   88 +-
 .../widgets/analogclock/lv_analogclock.c      |  803 +++++++++++
 .../widgets/analogclock/lv_analogclock.h      |  317 +++++
 src/extra/widgets/animimg/lv_animimg.c        |   53 +-
 src/extra/widgets/animimg/lv_animimg.h        |   37 +-
 src/extra/widgets/carousel/lv_carousel.c      |  208 +++
 src/extra/widgets/carousel/lv_carousel.h      |   75 +
 src/extra/widgets/dclock/lv_dclock.c          |  437 ++++++
 src/extra/widgets/dclock/lv_dclock.h          |  130 ++
 src/extra/widgets/keyboard/chinese_library.h  | 1237 +++++++++++++++++
 src/extra/widgets/keyboard/lv_zh_keyboard.c   |  751 ++++++++++
 src/extra/widgets/keyboard/lv_zh_keyboard.h   |  190 +++
 src/extra/widgets/lv_widgets.h                |    7 +
 src/extra/widgets/radiobtn/lv_radiobtn.c      |  385 +++++
 src/extra/widgets/radiobtn/lv_radiobtn.h      |  137 ++
 .../widgets/textprogress/lv_textprogress.c    |  140 ++
 .../widgets/textprogress/lv_textprogress.h    |  100 ++
 src/extra/widgets/video/lv_video.c            |  602 ++++++++
 src/extra/widgets/video/lv_video.h            |  114 ++
 src/lv_conf_internal.h                        |  134 ++
 src/misc/lv_anim.c                            |   43 +-
 src/misc/lv_anim.h                            |   19 +
 src/widgets/lv_arc.c                          |    5 +
 src/widgets/lv_btnmatrix.c                    |   21 +-
 37 files changed, 7382 insertions(+), 21 deletions(-)
 create mode 100644 src/extra/libs/barcode/code128.c
 create mode 100644 src/extra/libs/barcode/code128.h
 create mode 100644 src/extra/libs/barcode/lv_barcode.c
 create mode 100644 src/extra/libs/barcode/lv_barcode.h
 create mode 100644 src/extra/libs/fsdrv/lv_fs_rawfs.c
 create mode 100644 src/extra/widgets/analogclock/lv_analogclock.c
 create mode 100644 src/extra/widgets/analogclock/lv_analogclock.h
 create mode 100644 src/extra/widgets/carousel/lv_carousel.c
 create mode 100644 src/extra/widgets/carousel/lv_carousel.h
 create mode 100644 src/extra/widgets/dclock/lv_dclock.c
 create mode 100644 src/extra/widgets/dclock/lv_dclock.h
 create mode 100644 src/extra/widgets/keyboard/chinese_library.h
 create mode 100644 src/extra/widgets/keyboard/lv_zh_keyboard.c
 create mode 100644 src/extra/widgets/keyboard/lv_zh_keyboard.h
 create mode 100644 src/extra/widgets/radiobtn/lv_radiobtn.c
 create mode 100644 src/extra/widgets/radiobtn/lv_radiobtn.h
 create mode 100644 src/extra/widgets/textprogress/lv_textprogress.c
 create mode 100644 src/extra/widgets/textprogress/lv_textprogress.h
 create mode 100644 src/extra/widgets/video/lv_video.c
 create mode 100644 src/extra/widgets/video/lv_video.h

diff --git a/Kconfig b/Kconfig
index e4867bf02..13f8c6551 100644
--- a/Kconfig
+++ b/Kconfig
@@ -811,6 +811,9 @@ menu "LVGL configuration"
     endmenu
 
     menu "Extra Widgets"
+        config LV_USE_ANALOGCLOCK
+            bool "Analogclock."
+            default y if !LV_CONF_MINIMAL
         config LV_USE_ANIMIMG
             bool "Anim image."
             default y if !LV_CONF_MINIMAL
@@ -828,18 +831,31 @@ menu "LVGL configuration"
             bool "Use calendar header dropdown"
             depends on LV_USE_CALENDAR
             default y
+        config LV_USE_CAROUSEL
+            bool "Carousel."
+            default y if !LV_CONF_MINIMAL
         config LV_USE_CHART
             bool "Chart."
             default y if !LV_CONF_MINIMAL
         config LV_USE_COLORWHEEL
             bool "Colorwheel."
             default y if !LV_CONF_MINIMAL
+        config LV_USE_DCLOCK
+            bool "Dclock."
+            default y if !LV_CONF_MINIMAL
         config LV_USE_IMGBTN
             bool "Imgbtn."
             default y if !LV_CONF_MINIMAL
         config LV_USE_KEYBOARD
             bool "Keyboard."
             default y if !LV_CONF_MINIMAL
+        config LV_USE_ZH_KEYBOARD
+            bool "Chinese Keyboard."
+            default n
+        config LV_ZH_KEYBOARD_MINI
+            bool "Use minimal chinese library."
+            default y
+            depends on LV_USE_ZH_KEYBOARD
         config LV_USE_LED
             bool "LED."
             default y if !LV_CONF_MINIMAL
@@ -855,6 +871,9 @@ menu "LVGL configuration"
         config LV_USE_MSGBOX
             bool "Msgbox."
             default y if !LV_CONF_MINIMAL
+        config LV_USE_RADIOBTN
+            bool "Radiobtn."
+            default y if !LV_CONF_MINIMAL
         config LV_USE_SPAN
             bool "span"
             default y if !LV_CONF_MINIMAL
@@ -871,9 +890,15 @@ menu "LVGL configuration"
         config LV_USE_TABVIEW
             bool "Tabview."
             default y if !LV_CONF_MINIMAL
+        config LV_USE_TEXTPROGRESS
+            bool "Textprogress."
+            default y if !LV_CONF_MINIMAL
         config LV_USE_TILEVIEW
             bool "Tileview"
             default y if !LV_CONF_MINIMAL
+        config LV_USE_VIDEO
+            bool "Video"
+            default n
         config LV_USE_WIN
             bool "Win"
             default y if !LV_CONF_MINIMAL
@@ -976,6 +1001,20 @@ menu "LVGL configuration"
             default 0
             depends on LV_USE_FS_LITTLEFS
 
+        config LV_USE_FS_RAWFS
+            bool "File system on top of Raw binary"
+        config LV_FS_RAWFS_LETTER
+            int "Set an upper cased letter on which the drive will accessible (e.g. 'A' i.e. 65)"
+            default 70
+            depends on LV_USE_FS_RAWFS
+        config LV_FS_RAWFS_XIP
+            bool "RAW binaries can be copied directly"
+            depends on LV_USE_FS_RAWFS
+        config LV_FS_RAWFS_XIP_BASE_ADDR
+            hex "Base address from where raw binaries can be copied directly"
+            default 0xFFFFFFFF
+            depends on LV_FS_RAWFS_XIP
+
         config LV_USE_PNG
             bool "PNG decoder library"
 
@@ -990,6 +1029,11 @@ menu "LVGL configuration"
 
         config LV_USE_QRCODE
             bool "QR code library"
+            default y if !LV_CONF_MINIMAL
+
+        config LV_USE_BARCODE
+            bool "Barcode library"
+            default y if !LV_CONF_MINIMAL
 
         config LV_USE_FREETYPE
             bool "FreeType library"
diff --git a/examples/widgets/animimg/lv_example_animimg_1.c b/examples/widgets/animimg/lv_example_animimg_1.c
index 4341b6fec..4f814578c 100644
--- a/examples/widgets/animimg/lv_example_animimg_1.c
+++ b/examples/widgets/animimg/lv_example_animimg_1.c
@@ -14,7 +14,7 @@ void lv_example_animimg_1(void)
 {
     lv_obj_t * animimg0 = lv_animimg_create(lv_scr_act());
     lv_obj_center(animimg0);
-    lv_animimg_set_src(animimg0, (const void **) anim_imgs, 3);
+    lv_animimg_set_src(animimg0, (const void **) anim_imgs, 3, false);
     lv_animimg_set_duration(animimg0, 1000);
     lv_animimg_set_repeat_count(animimg0, LV_ANIM_REPEAT_INFINITE);
     lv_animimg_start(animimg0);
diff --git a/examples/widgets/animimg/lv_example_animimg_1.py b/examples/widgets/animimg/lv_example_animimg_1.py
index f3a31fd02..25dba785a 100644
--- a/examples/widgets/animimg/lv_example_animimg_1.py
+++ b/examples/widgets/animimg/lv_example_animimg_1.py
@@ -45,7 +45,7 @@ anim_imgs[2] = lv.img_dsc_t({
 
 animimg0 = lv.animimg(lv.scr_act())
 animimg0.center()
-animimg0.set_src(anim_imgs, 3)
+animimg0.set_src(anim_imgs, 3, False)
 animimg0.set_duration(1000)
 animimg0.set_repeat_count(lv.ANIM_REPEAT.INFINITE)
 animimg0.start()
diff --git a/lv_conf_template.h b/lv_conf_template.h
index bed77fb8f..05ec7ce7d 100644
--- a/lv_conf_template.h
+++ b/lv_conf_template.h
@@ -518,6 +518,8 @@
 /*-----------
  * Widgets
  *----------*/
+#define LV_USE_ANALOGCLOCK 1
+
 #define LV_USE_ANIMIMG    1
 
 #define LV_USE_CALENDAR   1
@@ -534,10 +536,14 @@
     #define LV_USE_CALENDAR_HEADER_DROPDOWN 1
 #endif  /*LV_USE_CALENDAR*/
 
+#define LV_USE_CAROUSEL   1
+
 #define LV_USE_CHART      1
 
 #define LV_USE_COLORWHEEL 1
 
+#define LV_USE_DCLOCK     1
+
 #define LV_USE_IMGBTN     1
 
 #define LV_USE_KEYBOARD   1
@@ -552,6 +558,8 @@
 
 #define LV_USE_MSGBOX     1
 
+#define LV_USE_RADIOBTN   1
+
 #define LV_USE_SPAN       1
 #if LV_USE_SPAN
     /*A line text can contain maximum num of span descriptor */
@@ -566,8 +574,15 @@
 
 #define LV_USE_TILEVIEW   1
 
+#define LV_USE_VIDEO      1
+
 #define LV_USE_WIN        1
 
+#define LV_USE_ZH_KEYBOARD 1
+#if LV_USE_ZH_KEYBOARD
+    #define LV_ZH_KEYBOARD_MINI 1
+#endif
+
 /*-----------
  * Themes
  *----------*/
@@ -646,6 +661,16 @@
     #define LV_FS_LITTLEFS_CACHE_SIZE 0    /*>0 to cache this number of bytes in lv_fs_read()*/
 #endif
 
+/*API for RAWFS (needs to be added separately).*/
+#define LV_USE_FS_RAWFS 0
+#if LV_USE_FS_RAWFS
+    #define LV_FS_RAWFS_LETTER 'F'     /*Set an upper cased letter on which the drive will accessible (e.g. 'A')*/
+#define LV_FS_RAWFS_XIP 0
+#if LV_FS_RAWFS_XIP
+    #define LV_FS_RAWFS_XIP_BASE_ADDR 0xFFFFFFFF
+#endif
+#endif
+
 /*PNG decoder library*/
 #define LV_USE_PNG 0
 
diff --git a/src/extra/libs/barcode/code128.c b/src/extra/libs/barcode/code128.c
new file mode 100644
index 000000000..0a94ede44
--- /dev/null
+++ b/src/extra/libs/barcode/code128.c
@@ -0,0 +1,582 @@
+// Copyright (c) 2013, LKC Technologies, Inc.
+// All rights reserved.
+//
+// Redistribution and use in source and binary forms, with or without
+// modification, are permitted provided that the following conditions are met:
+//
+// Redistributions of source code must retain the above copyright notice, this
+// list of conditions and the following disclaimer. Redistributions in binary
+// form must reproduce the above copyright notice, this list of conditions and
+// the following disclaimer in the documentation and/or other materials
+// provided with the distribution. THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT
+// HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES,
+// INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND
+// FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
+// COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
+// INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
+// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA,
+// OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
+// LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
+// NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
+// EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
+
+#include "../../../lvgl.h"
+#if LV_USE_BARCODE
+
+#include "code128.h"
+#include <string.h>
+
+#define CODE128_MALLOC      lv_mem_alloc
+#define CODE128_REALLOC     lv_mem_realloc
+#define CODE128_FREE        lv_mem_free
+#define CODE128_MEMSET      lv_memset
+#define CODE128_STRLEN      strlen
+#define CODE128_ASSERT      LV_ASSERT
+
+#define CODE128_QUIET_ZONE_LEN 10
+#define CODE128_CHAR_LEN       11
+#define CODE128_STOP_CODE_LEN  13
+
+#define CODE128_START_CODE_A 103
+#define CODE128_START_CODE_B 104
+#define CODE128_START_CODE_C 105
+
+#define CODE128_MODE_A    'a'
+#define CODE128_MODE_B    'b'
+#define CODE128_MODE_C    'c'
+
+#define CODE128_MIN_ENCODE_LEN (CODE128_QUIET_ZONE_LEN * 2 + CODE128_CHAR_LEN * 2 + CODE128_STOP_CODE_LEN)
+
+static const int code128_pattern[] = {
+    // value: pattern,     bar/space widths
+    1740, //   0: 11011001100, 212222
+    1644, //   1: 11001101100, 222122
+    1638, //   2: 11001100110, 222221
+    1176, //   3: 10010011000, 121223
+    1164, //   4: 10010001100, 121322
+    1100, //   5: 10001001100, 131222
+    1224, //   6: 10011001000, 122213
+    1220, //   7: 10011000100, 122312
+    1124, //   8: 10001100100, 132212
+    1608, //   9: 11001001000, 221213
+    1604, //  10: 11001000100, 221312
+    1572, //  11: 11000100100, 231212
+    1436, //  12: 10110011100, 112232
+    1244, //  13: 10011011100, 122132
+    1230, //  14: 10011001110, 122231
+    1484, //  15: 10111001100, 113222
+    1260, //  16: 10011101100, 123122
+    1254, //  17: 10011100110, 123221
+    1650, //  18: 11001110010, 223211
+    1628, //  19: 11001011100, 221132
+    1614, //  20: 11001001110, 221231
+    1764, //  21: 11011100100, 213212
+    1652, //  22: 11001110100, 223112
+    1902, //  23: 11101101110, 312131
+    1868, //  24: 11101001100, 311222
+    1836, //  25: 11100101100, 321122
+    1830, //  26: 11100100110, 321221
+    1892, //  27: 11101100100, 312212
+    1844, //  28: 11100110100, 322112
+    1842, //  29: 11100110010, 322211
+    1752, //  30: 11011011000, 212123
+    1734, //  31: 11011000110, 212321
+    1590, //  32: 11000110110, 232121
+    1304, //  33: 10100011000, 111323
+    1112, //  34: 10001011000, 131123
+    1094, //  35: 10001000110, 131321
+    1416, //  36: 10110001000, 112313
+    1128, //  37: 10001101000, 132113
+    1122, //  38: 10001100010, 132311
+    1672, //  39: 11010001000, 211313
+    1576, //  40: 11000101000, 231113
+    1570, //  41: 11000100010, 231311
+    1464, //  42: 10110111000, 112133
+    1422, //  43: 10110001110
+    1134, //  44: 10001101110
+    1496, //  45: 10111011000, 113123
+    1478, //  46: 10111000110, 113321
+    1142, //  47: 10001110110, 133121
+    1910, //  48: 11101110110, 313121
+    1678, //  49: 11010001110, 211331
+    1582, //  50: 11000101110, 231131
+    1768, //  51: 11011101000, 213113
+    1762, //  52: 11011100010, 213311
+    1774, //  53: 11011101110, 213131
+    1880, //  54: 11101011000, 311123
+    1862, //  55: 11101000110, 311321
+    1814, //  56: 11100010110, 331121
+    1896, //  57: 11101101000, 312113
+    1890, //  58: 11101100010, 312311
+    1818, //  59: 11100011010, 332111
+    1914, //  60: 11101111010, 314111
+    1602, //  61: 11001000010, 221411
+    1930, //  62: 11110001010, 431111
+    1328, //  63: 10100110000, 111224
+    1292, //  64: 10100001100, 111422
+    1200, //  65: 10010110000, 121124
+    1158, //  66: 10010000110, 121421
+    1068, //  67: 10000101100, 141122
+    1062, //  68: 10000100110, 141221
+    1424, //  69: 10110010000, 112214
+    1412, //  70: 10110000100, 112412
+    1232, //  71: 10011010000, 122114
+    1218, //  72: 10011000010, 122411
+    1076, //  73: 10000110100, 142112
+    1074, //  74: 10000110010, 142211
+    1554, //  75: 11000010010, 241211
+    1616, //  76: 11001010000, 221114
+    1978, //  77: 11110111010, 413111
+    1556, //  78: 11000010100, 241112
+    1146, //  79: 10001111010, 134111
+    1340, //  80: 10100111100, 111242
+    1212, //  81: 10010111100, 121142
+    1182, //  82: 10010011110, 121241
+    1508, //  83: 10111100100, 114212
+    1268, //  84: 10011110100, 124112
+    1266, //  85: 10011110010, 124211
+    1956, //  86: 11110100100, 411212
+    1940, //  87: 11110010100, 421112
+    1938, //  88: 11110010010, 421211
+    1758, //  89: 11011011110, 212141
+    1782, //  90: 11011110110, 214121
+    1974, //  91: 11110110110, 412121
+    1400, //  92: 10101111000, 111143
+    1310, //  93: 10100011110, 111341
+    1118, //  94: 10001011110, 131141
+    1512, //  95: 10111101000, 114113
+    1506, //  96: 10111100010, 114311
+    1960, //  97: 11110101000, 411113
+    1954, //  98: 11110100010, 411311
+    1502, //  99: 10111011110, 113141
+    1518, // 100: 10111101110, 114131
+    1886, // 101: 11101011110, 311141
+    1966, // 102: 11110101110, 411131
+    1668, // 103: 11010000100, 211412
+    1680, // 104: 11010010000, 211214
+    1692  // 105: 11010011100, 211232
+};
+
+static const int code128_stop_pattern = 6379; // 1100011101011, 2331112
+
+struct code128_step {
+    int prev_ix;                // Index of previous step, if any
+    const char * next_input;    // Remaining input
+    unsigned short len;         // The length of the pattern so far (includes this step)
+    char mode;                  // State for the current encoding
+    signed char code;           // What code should be written for this step
+};
+
+struct code128_state {
+    struct code128_step * steps;
+    int allocated_steps;
+    int current_ix;
+    int todo_ix;
+    int best_ix;
+
+    size_t maxlength;
+};
+
+size_t code128_estimate_len(const char * s)
+{
+    return CODE128_QUIET_ZONE_LEN
+           + CODE128_CHAR_LEN // start code
+           + CODE128_CHAR_LEN * (CODE128_STRLEN(s) * 11 / 10) // contents + 10% padding
+           + CODE128_CHAR_LEN // checksum
+           + CODE128_STOP_CODE_LEN
+           + CODE128_QUIET_ZONE_LEN;
+}
+
+static void code128_append_pattern(int pattern, int pattern_length, char * out)
+{
+    // All patterns have their first bit set by design
+    CODE128_ASSERT(pattern & (1 << (pattern_length - 1)));
+
+    int i;
+    for(i = pattern_length - 1; i >= 0; i--) {
+        // cast avoids warning: implicit conversion from 'int' to 'char' changes value from 255 to -1 [-Wconstant-conversion]
+        *out++ = (unsigned char)((pattern & (1 << i)) ? 255 : 0);
+    }
+}
+
+static int code128_append_code(int code, char * out)
+{
+    CODE128_ASSERT(code >= 0 && code < (int)(sizeof(code128_pattern) / sizeof(code128_pattern[0])));
+    code128_append_pattern(code128_pattern[code], CODE128_CHAR_LEN, out);
+    return CODE128_CHAR_LEN;
+}
+
+static int code128_append_stop_code(char * out)
+{
+    code128_append_pattern(code128_stop_pattern, CODE128_STOP_CODE_LEN, out);
+    return CODE128_STOP_CODE_LEN;
+}
+
+static signed char code128_switch_code(char from_mode, char to_mode)
+{
+    switch(from_mode) {
+        case CODE128_MODE_A:
+            switch(to_mode) {
+                case CODE128_MODE_B:
+                    return 100;
+                case CODE128_MODE_C:
+                    return 99;
+            }
+            break;
+
+        case CODE128_MODE_B:
+            switch(to_mode) {
+                case CODE128_MODE_A:
+                    return 101;
+                case CODE128_MODE_C:
+                    return 99;
+            }
+            break;
+
+        case CODE128_MODE_C:
+            switch(to_mode) {
+                case CODE128_MODE_B:
+                    return 100;
+                case CODE128_MODE_A:
+                    return 101;
+            }
+            break;
+        default:
+            break;
+    }
+
+    CODE128_ASSERT(0); // Invalid mode switch
+    return -1;
+}
+
+static signed char code128a_ascii_to_code(signed char value)
+{
+    if(value >= ' ' && value <= '_')
+        return (signed char)(value - ' ');
+    else if(value >= 0 && value < ' ')
+        return (signed char)(value + 64);
+    else if(value == (signed char)CODE128_FNC1)
+        return 102;
+    else if(value == (signed char)CODE128_FNC2)
+        return 97;
+    else if(value == (signed char)CODE128_FNC3)
+        return 96;
+    else if(value == (signed char)CODE128_FNC4)
+        return 101;
+    else
+        return -1;
+}
+
+static signed char code128b_ascii_to_code(signed char value)
+{
+    if(value >= ' ')  // value <= 127 is implied
+        return (signed char)(value - ' ');
+    else if(value == (signed char)CODE128_FNC1)
+        return 102;
+    else if(value == (signed char)CODE128_FNC2)
+        return 97;
+    else if(value == (signed char)CODE128_FNC3)
+        return 96;
+    else if(value == (signed char)CODE128_FNC4)
+        return 100;
+    else
+        return -1;
+}
+
+static signed char code128c_ascii_to_code(const char * values)
+{
+    if(values[0] == CODE128_FNC1)
+        return 102;
+
+    if(values[0] >= '0' && values[0] <= '9' &&
+       values[1] >= '0' && values[1] <= '9') {
+        char code = 10 * (values[0] - '0') + (values[1] - '0');
+        return code;
+    }
+
+    return -1;
+}
+
+static int code128_do_a_step(struct code128_step * base, int prev_ix, int ix)
+{
+    struct code128_step * previous_step = &base[prev_ix];
+    struct code128_step * step = &base[ix];
+
+    char value = *previous_step->next_input;
+    // NOTE: Currently we can't encode NULL
+    if(value == 0)
+        return 0;
+
+    step->code = code128a_ascii_to_code(value);
+    if(step->code < 0)
+        return 0;
+
+    step->prev_ix = prev_ix;
+    step->next_input = previous_step->next_input + 1;
+    step->mode = CODE128_MODE_A;
+    step->len = previous_step->len + CODE128_CHAR_LEN;
+    if(step->mode != previous_step->mode)
+        step->len += CODE128_CHAR_LEN; // Need to switch modes
+
+    return 1;
+}
+
+static int code128_do_b_step(struct code128_step * base, int prev_ix, int ix)
+{
+    struct code128_step * previous_step = &base[prev_ix];
+    struct code128_step * step = &base[ix];
+
+    char value = *previous_step->next_input;
+    // NOTE: Currently we can't encode NULL
+    if(value == 0)
+        return 0;
+
+    step->code = code128b_ascii_to_code(value);
+    if(step->code < 0)
+        return 0;
+
+    step->prev_ix = prev_ix;
+    step->next_input = previous_step->next_input + 1;
+    step->mode = CODE128_MODE_B;
+    step->len = previous_step->len + CODE128_CHAR_LEN;
+    if(step->mode != previous_step->mode)
+        step->len += CODE128_CHAR_LEN; // Need to switch modes
+
+    return 1;
+}
+
+static int code128_do_c_step(struct code128_step * base, int prev_ix, int ix)
+{
+    struct code128_step * previous_step = &base[prev_ix];
+    struct code128_step * step = &base[ix];
+
+    char value = *previous_step->next_input;
+    // NOTE: Currently we can't encode NULL
+    if(value == 0)
+        return 0;
+
+    step->code = code128c_ascii_to_code(previous_step->next_input);
+    if(step->code < 0)
+        return 0;
+
+    step->prev_ix = prev_ix;
+    step->next_input = previous_step->next_input + 1;
+
+    // Mode C consumes 2 characters for codes 0-99
+    if(step->code < 100)
+        step->next_input++;
+
+    step->mode = CODE128_MODE_C;
+    step->len = previous_step->len + CODE128_CHAR_LEN;
+    if(step->mode != previous_step->mode)
+        step->len += CODE128_CHAR_LEN; // Need to switch modes
+
+    return 1;
+}
+
+static struct code128_step * code128_alloc_step(struct code128_state * state)
+{
+    if(state->todo_ix >= state->allocated_steps) {
+        state->allocated_steps += 1024;
+        state->steps = (struct code128_step *) CODE128_REALLOC(state->steps,
+                                                               state->allocated_steps * sizeof(struct code128_step));
+    }
+
+    struct code128_step * step = &state->steps[state->todo_ix];
+
+    CODE128_MEMSET(step, 0, sizeof(*step));
+    return step;
+}
+
+static void code128_do_step(struct code128_state * state)
+{
+    struct code128_step * step = &state->steps[state->current_ix];
+    if(*step->next_input == 0) {
+        // Done, so see if we have a new shortest encoding.
+        if((step->len < state->maxlength) ||
+           (state->best_ix < 0 && step->len == state->maxlength)) {
+            state->best_ix = state->current_ix;
+
+            // Update maxlength to avoid considering anything longer
+            state->maxlength = step->len;
+        }
+        return;
+    }
+
+    // Don't try if we're already at or beyond the max acceptable
+    // length;
+    if(step->len >= state->maxlength)
+        return;
+    char mode = step->mode;
+
+    code128_alloc_step(state);
+    int mode_c_worked = 0;
+
+    // Always try mode C
+    if(code128_do_c_step(state->steps, state->current_ix, state->todo_ix)) {
+        state->todo_ix++;
+        code128_alloc_step(state);
+        mode_c_worked = 1;
+    }
+
+    if(mode == CODE128_MODE_A) {
+        // If A works, stick with A. There's no advantage to switching
+        // to B proactively if A still works.
+        if(code128_do_a_step(state->steps, state->current_ix, state->todo_ix) ||
+           code128_do_b_step(state->steps, state->current_ix, state->todo_ix))
+            state->todo_ix++;
+    }
+    else if(mode == CODE128_MODE_B) {
+        // The same logic applies here. There's no advantage to switching
+        // proactively to A if B still works.
+        if(code128_do_b_step(state->steps, state->current_ix, state->todo_ix) ||
+           code128_do_a_step(state->steps, state->current_ix, state->todo_ix))
+            state->todo_ix++;
+    }
+    else if(!mode_c_worked) {
+        // In mode C. If mode C worked and we're in mode C, trying anything
+        // else is pointless since the mode C encoding will be shorter and
+        // there won't be any mode switches.
+
+        // If we're leaving mode C, though, try both in case one ends up
+        // better than the other.
+        if(code128_do_a_step(state->steps, state->current_ix, state->todo_ix)) {
+            state->todo_ix++;
+            code128_alloc_step(state);
+        }
+        if(code128_do_b_step(state->steps, state->current_ix, state->todo_ix))
+            state->todo_ix++;
+    }
+}
+
+size_t code128_encode_raw(const char * s, char * out, size_t maxlength)
+{
+    struct code128_state state;
+
+    const size_t overhead = CODE128_QUIET_ZONE_LEN
+                            + CODE128_CHAR_LEN // checksum
+                            + CODE128_STOP_CODE_LEN
+                            + CODE128_QUIET_ZONE_LEN;
+    if(maxlength < overhead + CODE128_CHAR_LEN + CODE128_CHAR_LEN) {
+        // Need space to encode the start character and one additional
+        // character.
+        return 0;
+    }
+
+    state.allocated_steps = 256;
+    state.steps = (struct code128_step *) CODE128_MALLOC(state.allocated_steps * sizeof(struct code128_step));
+    state.current_ix = 0;
+    state.todo_ix = 0;
+    state.maxlength = maxlength - overhead;
+    state.best_ix = -1;
+
+    // Initialize the first 3 steps for the 3 encoding routes (A, B, C)
+    state.steps[0].prev_ix = -1;
+    state.steps[0].next_input = s;
+    state.steps[0].len = CODE128_CHAR_LEN;
+    state.steps[0].mode = CODE128_MODE_C;
+    state.steps[0].code = CODE128_START_CODE_C;
+
+    state.steps[1].prev_ix = -1;
+    state.steps[1].next_input = s;
+    state.steps[1].len = CODE128_CHAR_LEN;
+    state.steps[1].mode = CODE128_MODE_A;
+    state.steps[1].code = CODE128_START_CODE_A;
+
+    state.steps[2].prev_ix = -1;
+    state.steps[2].next_input = s;
+    state.steps[2].len = CODE128_CHAR_LEN;
+    state.steps[2].mode = CODE128_MODE_B;
+    state.steps[2].code = CODE128_START_CODE_B;
+
+    state.todo_ix = 3;
+
+    // Keep going until no more work
+    do {
+        code128_do_step(&state);
+        state.current_ix++;
+    } while(state.current_ix != state.todo_ix);
+
+    // If no best_step, then fail.
+    if(state.best_ix < 0) {
+        CODE128_FREE(state.steps);
+        return 0;
+    }
+
+    // Determine the list of codes
+    size_t num_codes = state.maxlength / CODE128_CHAR_LEN;
+    char * codes = CODE128_MALLOC(num_codes);
+    CODE128_ASSERT(codes);
+
+    struct code128_step * step = &state.steps[state.best_ix];
+    size_t i;
+    for(i = num_codes - 1; i > 0; --i) {
+        struct code128_step * prev_step = &state.steps[step->prev_ix];
+        codes[i] = step->code;
+        if(step->mode != prev_step->mode) {
+            --i;
+            codes[i] = code128_switch_code(prev_step->mode, step->mode);
+        }
+        step = prev_step;
+    }
+    codes[0] = step->code;
+
+    // Encode everything up to the checksum
+    size_t actual_length = state.maxlength + overhead;
+    CODE128_MEMSET(out, 0, CODE128_QUIET_ZONE_LEN);
+    out += CODE128_QUIET_ZONE_LEN;
+    for(i = 0; i < num_codes; i++)
+        out += code128_append_code(codes[i], out);
+
+    // Compute the checksum
+    int sum = codes[0];
+    for(i = 1; i < num_codes; i++)
+        sum += codes[i] * i;
+    out += code128_append_code(sum % 103, out);
+
+    // Finalize the code.
+    out += code128_append_stop_code(out);
+    CODE128_MEMSET(out, 0, CODE128_QUIET_ZONE_LEN);
+
+    CODE128_FREE(codes);
+    CODE128_FREE(state.steps);
+    return actual_length;
+}
+
+/**
+ * @brief Encode the GS1 string
+ *
+ * This converts [FNC1] sequences to raw FNC1 characters and
+ * removes spaces before encoding the barcodes.
+ *
+ * @return the length of barcode data in bytes
+ */
+size_t code128_encode_gs1(const char * s, char * out, size_t maxlength)
+{
+    size_t raw_size = CODE128_STRLEN(s) + 1;
+    char * raw = CODE128_MALLOC(raw_size);
+    CODE128_ASSERT(raw);
+    if(!raw) {
+        return 0;
+    }
+
+    char * p = raw;
+    for(; *s != '\0'; s++) {
+        if(strncmp(s, "[FNC1]", 6) == 0) {
+            *p++ = CODE128_FNC1;
+            s += 5;
+        }
+        else if(*s != ' ') {
+            *p++ = *s;
+        }
+    }
+    *p = '\0';
+
+    size_t length = code128_encode_raw(raw, out, maxlength);
+
+    CODE128_FREE(raw);
+
+    return length;
+}
+
+#endif /*LV_USE_BARCODE*/
diff --git a/src/extra/libs/barcode/code128.h b/src/extra/libs/barcode/code128.h
new file mode 100644
index 000000000..51d53d0cc
--- /dev/null
+++ b/src/extra/libs/barcode/code128.h
@@ -0,0 +1,47 @@
+// Copyright (c) 2013-15, LKC Technologies, Inc.
+// All rights reserved.
+//
+// Redistribution and use in source and binary forms, with or without
+// modification, are permitted provided that the following conditions are met:
+//
+// Redistributions of source code must retain the above copyright notice, this
+// list of conditions and the following disclaimer. Redistributions in binary
+// form must reproduce the above copyright notice, this list of conditions and
+// the following disclaimer in the documentation and/or other materials
+// provided with the distribution. THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT
+// HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES,
+// INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND
+// FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
+// COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
+// INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
+// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA,
+// OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
+// LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
+// NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
+// EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
+
+#ifndef CODE128_H
+#define CODE128_H
+
+#include <stddef.h>
+
+#ifdef __cplusplus
+extern "C" {
+#endif
+
+// Since the FNCn characters are not ASCII, define versions here to
+// simplify encoding strings that include them.
+#define CODE128_FNC1 '\xf1'
+#define CODE128_FNC2 '\xf2'
+#define CODE128_FNC3 '\xf3'
+#define CODE128_FNC4 '\xf4'
+
+size_t code128_estimate_len(const char * s);
+size_t code128_encode_gs1(const char * s, char * out, size_t maxlength);
+size_t code128_encode_raw(const char * s, char * out, size_t maxlength);
+
+#ifdef __cplusplus
+}
+#endif
+
+#endif // CODE128_H
diff --git a/src/extra/libs/barcode/lv_barcode.c b/src/extra/libs/barcode/lv_barcode.c
new file mode 100644
index 000000000..ff63fa18d
--- /dev/null
+++ b/src/extra/libs/barcode/lv_barcode.c
@@ -0,0 +1,215 @@
+/**
+ * @file lv_barcode.c
+ *
+ */
+
+/*********************
+ *      INCLUDES
+ *********************/
+#include "lv_barcode.h"
+#if LV_USE_BARCODE
+
+#include "code128.h"
+
+/*********************
+ *      DEFINES
+ *********************/
+#define MY_CLASS &lv_barcode_class
+
+/**********************
+ *      TYPEDEFS
+ **********************/
+
+/**********************
+ *  STATIC PROTOTYPES
+ **********************/
+static void lv_barcode_constructor(const lv_obj_class_t * class_p, lv_obj_t * obj);
+static void lv_barcode_destructor(const lv_obj_class_t * class_p, lv_obj_t * obj);
+static bool lv_barcode_change_buf_size(lv_obj_t * obj, lv_coord_t w);
+
+/**********************
+ *  STATIC VARIABLES
+ **********************/
+
+const lv_obj_class_t lv_barcode_class = {
+    .constructor_cb = lv_barcode_constructor,
+    .destructor_cb = lv_barcode_destructor,
+    .width_def = LV_SIZE_CONTENT,
+    .instance_size = sizeof(lv_barcode_t),
+    .base_class = &lv_canvas_class
+};
+
+/**********************
+ *      MACROS
+ **********************/
+
+/**********************
+ *   GLOBAL FUNCTIONS
+ **********************/
+
+lv_obj_t * lv_barcode_create(lv_obj_t * parent)
+{
+    LV_LOG_INFO("begin");
+    lv_obj_t * obj = lv_obj_class_create_obj(MY_CLASS, parent);
+    lv_obj_class_init_obj(obj);
+    return obj;
+}
+
+void lv_barcode_set_dark_color(lv_obj_t * obj, lv_color_t color)
+{
+    LV_ASSERT_OBJ(obj, MY_CLASS);
+
+    lv_barcode_t * barcode = (lv_barcode_t *)obj;
+    barcode->dark_color = color;
+}
+
+void lv_barcode_set_light_color(lv_obj_t * obj, lv_color_t color)
+{
+    LV_ASSERT_OBJ(obj, MY_CLASS);
+
+    lv_barcode_t * barcode = (lv_barcode_t *)obj;
+    barcode->light_color = color;
+}
+
+void lv_barcode_set_scale(lv_obj_t * obj, uint16_t scale)
+{
+    LV_ASSERT_OBJ(obj, MY_CLASS);
+
+    if(scale == 0) {
+        scale = 1;
+    }
+
+    lv_barcode_t * barcode = (lv_barcode_t *)obj;
+    barcode->scale = scale;
+}
+
+lv_res_t lv_barcode_update(lv_obj_t * obj, const char * data)
+{
+    LV_ASSERT_OBJ(obj, MY_CLASS);
+    LV_ASSERT_NULL(data);
+
+    lv_res_t res = LV_RES_INV;
+    lv_barcode_t * barcode = (lv_barcode_t *)obj;
+
+    if(data == NULL || strlen(data) == 0) {
+        LV_LOG_WARN("data is empty");
+        return LV_RES_INV;
+    }
+
+    size_t len = code128_estimate_len(data);
+    LV_LOG_INFO("data: %s, len = %zu", data, len);
+
+    char * out_buf = lv_mem_alloc(len);
+    LV_ASSERT_MALLOC(out_buf);
+    if(!out_buf) {
+        LV_LOG_ERROR("malloc failed for out_buf");
+        return LV_RES_INV;
+    }
+
+    lv_coord_t barcode_w = code128_encode_gs1(data, out_buf, len);
+    LV_LOG_INFO("barcode width = %d", (int)barcode_w);
+
+    LV_ASSERT(barcode->scale > 0);
+    uint16_t scale = barcode->scale;
+
+    if(!lv_barcode_change_buf_size(obj, barcode_w * scale)) {
+        goto failed;
+    }
+
+    lv_canvas_set_palette(obj, 0, barcode->dark_color);
+    lv_canvas_set_palette(obj, 1, barcode->light_color);
+
+    for(lv_coord_t x = 0; x < barcode_w; x++) {
+        lv_color_t color;
+        color.full = out_buf[x] ? 0 : 1;
+        for(uint16_t i = 0; i < scale; i++) {
+            lv_canvas_set_px_color(obj, x * scale + i, 0, color);
+        }
+    }
+
+    res = LV_RES_OK;
+
+failed:
+    lv_mem_free(out_buf);
+    return res;
+}
+
+lv_color_t lv_barcode_get_dark_color(lv_obj_t * obj)
+{
+    LV_ASSERT_OBJ(obj, MY_CLASS);
+
+    lv_barcode_t * barcode = (lv_barcode_t *)obj;
+    return barcode->dark_color;
+}
+
+lv_color_t lv_barcode_get_light_color(lv_obj_t * obj)
+{
+    LV_ASSERT_OBJ(obj, MY_CLASS);
+
+    lv_barcode_t * barcode = (lv_barcode_t *)obj;
+    return barcode->light_color;
+}
+
+uint16_t lv_barcode_get_scale(lv_obj_t * obj)
+{
+    LV_ASSERT_OBJ(obj, MY_CLASS);
+
+    lv_barcode_t * barcode = (lv_barcode_t *)obj;
+    return barcode->scale;
+}
+
+/**********************
+ *   STATIC FUNCTIONS
+ **********************/
+
+static void lv_barcode_constructor(const lv_obj_class_t * class_p, lv_obj_t * obj)
+{
+    LV_UNUSED(class_p);
+
+    lv_barcode_t * barcode = (lv_barcode_t *)obj;
+    barcode->dark_color = lv_color_black();
+    barcode->light_color = lv_color_white();
+    barcode->scale = 1;
+}
+
+static void lv_barcode_destructor(const lv_obj_class_t * class_p, lv_obj_t * obj)
+{
+    LV_UNUSED(class_p);
+
+    lv_img_dsc_t * img = lv_canvas_get_img(obj);
+    lv_img_cache_invalidate_src(img);
+
+    if(!img->data) {
+        LV_LOG_INFO("canvas buffer is NULL");
+        return;
+    }
+
+    LV_LOG_INFO("free canvas buffer: %p", img->data);
+
+    lv_mem_free((void *)img->data);
+    img->data = NULL;
+}
+
+static bool lv_barcode_change_buf_size(lv_obj_t * obj, lv_coord_t w)
+{
+    LV_ASSERT_NULL(obj);
+    LV_ASSERT(w > 0);
+
+    lv_img_dsc_t * img = lv_canvas_get_img(obj);
+    void * buf = (void *)img->data;
+
+    uint32_t buf_size = LV_CANVAS_BUF_SIZE_INDEXED_1BIT(w, 1);
+    buf = lv_mem_realloc(buf, buf_size);
+    LV_ASSERT_MALLOC(buf);
+
+    if(!buf) {
+        LV_LOG_ERROR("malloc failed for canvas buffer");
+        return false;
+    }
+
+    lv_canvas_set_buffer(obj, buf, w, 1, LV_IMG_CF_INDEXED_1BIT);
+    LV_LOG_INFO("set canvas buffer: %p, width = %d", buf, (int)w);
+    return true;
+}
+
+#endif /*LV_USE_BARCODE*/
diff --git a/src/extra/libs/barcode/lv_barcode.h b/src/extra/libs/barcode/lv_barcode.h
new file mode 100644
index 000000000..3b534bdb3
--- /dev/null
+++ b/src/extra/libs/barcode/lv_barcode.h
@@ -0,0 +1,108 @@
+/**
+ * @file lv_barcode.c
+ *
+ */
+
+#ifndef LV_BARCODE_H
+#define LV_BARCODE_H
+
+#ifdef __cplusplus
+extern "C" {
+#endif
+
+/*********************
+ *      INCLUDES
+ *********************/
+#include "../../../lvgl.h"
+#if LV_USE_BARCODE
+
+/*********************
+ *      DEFINES
+ *********************/
+
+/**********************
+ *      TYPEDEFS
+ **********************/
+
+/*Data of barcode*/
+typedef struct {
+    lv_canvas_t canvas;
+    lv_color_t dark_color;
+    lv_color_t light_color;
+    uint16_t scale;
+} lv_barcode_t;
+
+extern const lv_obj_class_t lv_barcode_class;
+
+/**********************
+ * GLOBAL PROTOTYPES
+ **********************/
+
+/**
+ * Create an empty barcode (an `lv_canvas`) object.
+ * @param parent point to an object where to create the barcode
+ * @return pointer to the created barcode object
+ */
+lv_obj_t * lv_barcode_create(lv_obj_t * parent);
+
+/**
+ * Set the dark color of a barcode object
+ * @param obj pointer to barcode object
+ * @param color dark color of the barcode
+ */
+void lv_barcode_set_dark_color(lv_obj_t * obj, lv_color_t color);
+
+/**
+ * Set the light color of a barcode object
+ * @param obj pointer to barcode object
+ * @param color light color of the barcode
+ */
+void lv_barcode_set_light_color(lv_obj_t * obj, lv_color_t color);
+
+/**
+ * Set the scale of a barcode object
+ * @param obj pointer to barcode object
+ * @param scale scale factor
+ */
+void lv_barcode_set_scale(lv_obj_t * obj, uint16_t scale);
+
+/**
+ * Set the data of a barcode object
+ * @param obj pointer to barcode object
+ * @param data data to display
+ * @return LV_RES_OK: if no error; LV_RES_INV: on error
+ */
+lv_res_t lv_barcode_update(lv_obj_t * obj, const char * data);
+
+/**
+ * Get the dark color of a barcode object
+ * @param obj pointer to barcode object
+ * @return dark color of the barcode
+ */
+lv_color_t lv_barcode_get_dark_color(lv_obj_t * obj);
+
+/**
+ * Get the light color of a barcode object
+ * @param obj pointer to barcode object
+ * @return light color of the barcode
+ */
+lv_color_t lv_barcode_get_light_color(lv_obj_t * obj);
+
+/**
+ * Get the scale of a barcode object
+ * @param obj pointer to barcode object
+ * @return scale factor
+ */
+uint16_t lv_barcode_get_scale(lv_obj_t * obj);
+
+/**********************
+ *      MACROS
+ **********************/
+
+#endif /*LV_USE_BARCODE*/
+
+#ifdef __cplusplus
+} /* extern "C" */
+#endif
+
+#endif /*LV_BARCODE_H*/
diff --git a/src/extra/libs/fsdrv/lv_fs_rawfs.c b/src/extra/libs/fsdrv/lv_fs_rawfs.c
new file mode 100644
index 000000000..a6056c539
--- /dev/null
+++ b/src/extra/libs/fsdrv/lv_fs_rawfs.c
@@ -0,0 +1,322 @@
+/*
+ * @file lv_fs_rawfs.c
+ *
+ */
+
+/*********************
+ *      INCLUDES
+ *********************/
+#include "../../../lvgl.h"
+
+#if LV_USE_FS_RAWFS
+
+/*********************
+ *      DEFINES
+ *********************/
+
+#if LV_FS_RAWFS_LETTER == '\0'
+    #error "LV_FS_RAWFS_LETTER must be an upper case ASCII letter"
+#endif
+
+#if LV_FS_RAWFS_XIP
+    #if LV_FS_RAWFS_XIP_BASE_ADDR == 0xFFFFFFFF
+        #error "Base address for image binary (LV_FS_RAWFS_XIP_BASE_ADDR) must be valid"
+    #endif
+#endif
+
+/**********************
+ *      TYPEDEFS
+ **********************/
+/* Info for raw binaries: base, offset, size, name*/
+/*
+const rawfs_size_t rawfs_file_count = 3;
+rawfs_file_t rawfs_files[3] = {
+    0x0, 0, 1688704, "/apic33695.bin",
+    0x19c480, 0, 1204, "/green_left_icon.bin",
+    0x19c934, 0, 1204, "/red_right_icon.bin",
+};
+*/
+extern rawfs_size_t rawfs_file_count;
+extern rawfs_file_t rawfs_files[];
+
+/**********************
+ *  STATIC PROTOTYPES
+ **********************/
+static lv_fs_res_t rawfs_file_find(const char * path, rawfs_file_t * file_p);
+
+static void fs_init(void);
+
+static void * fs_open(lv_fs_drv_t * drv, const char * path, lv_fs_mode_t mode);
+static lv_fs_res_t fs_close(lv_fs_drv_t * drv, void * file_p);
+static lv_fs_res_t fs_read(lv_fs_drv_t * drv, void * file_p, void * buf, uint32_t btr, uint32_t * br);
+static lv_fs_res_t fs_write(lv_fs_drv_t * drv, void * file_p, const void * buf, uint32_t btw, uint32_t * bw);
+static lv_fs_res_t fs_seek(lv_fs_drv_t * drv, void * file_p, uint32_t pos, lv_fs_whence_t whence);
+static lv_fs_res_t fs_size(lv_fs_drv_t * drv, void * file_p, uint32_t * size_p);
+static lv_fs_res_t fs_tell(lv_fs_drv_t * drv, void * file_p, uint32_t * pos_p);
+
+static void * fs_dir_open(lv_fs_drv_t * drv, const char * path);
+static lv_fs_res_t fs_dir_read(lv_fs_drv_t * drv, void * rddir_p, char * fn);
+static lv_fs_res_t fs_dir_close(lv_fs_drv_t * drv, void * rddir_p);
+
+/**********************
+ *  STATIC VARIABLES
+ **********************/
+static rawfs_file_t FIL;
+
+/**********************
+ * GLOBAL PROTOTYPES
+ **********************/
+
+/**********************
+ *      MACROS
+ **********************/
+
+/**********************
+ *   GLOBAL FUNCTIONS
+ **********************/
+
+void lv_fs_rawfs_init(void)
+{
+    /*----------------------------------------------------
+     * Initialize your storage device and File System
+     * -------------------------------------------------*/
+    fs_init();
+
+    /*---------------------------------------------------
+     * Register the file system interface in LVGL
+     *--------------------------------------------------*/
+
+    /*Add a simple drive to open images*/
+    static lv_fs_drv_t fs_drv;
+    lv_fs_drv_init(&fs_drv);
+
+    /*Set up fields...*/
+    fs_drv.letter = LV_FS_RAWFS_LETTER;
+    fs_drv.open_cb = fs_open;
+    fs_drv.close_cb = fs_close;
+    fs_drv.read_cb = fs_read;
+    fs_drv.write_cb = fs_write;
+    fs_drv.seek_cb = fs_seek;
+    fs_drv.tell_cb = fs_tell;
+
+    fs_drv.dir_close_cb = fs_dir_close;
+    fs_drv.dir_open_cb = fs_dir_open;
+    fs_drv.dir_read_cb = fs_dir_read;
+
+    lv_fs_drv_register(&fs_drv);
+}
+
+/**********************
+ *   STATIC FUNCTIONS
+ **********************/
+
+/**
+ * Find file based on path
+ * @param path      path to the file (e.g. /folder/file.txt)
+ * @param file_p    pointer to a file_t variable. (opened with fs_open)
+ * @return          LV_FS_RES_OK: no error or  any error from @lv_fs_res_t enum
+ */
+static lv_fs_res_t rawfs_file_find(const char * path, rawfs_file_t * file_p)
+{
+    lv_fs_res_t res = LV_FS_RES_FS_ERR;
+    /*Find file*/
+    for(int i = 0; i < rawfs_file_count; i++) {
+        if(0 == strcmp(path, rawfs_files[i].name)) {
+            *file_p = rawfs_files[i];
+            res = LV_FS_RES_OK;
+            break;
+        }
+    }
+    return res;
+}
+
+/*Initialize your Storage device and File system.*/
+static void fs_init(void)
+{
+    FIL.base = 0;
+    FIL.offset = 0;
+}
+
+/**
+ * Open a file
+ * @param drv       pointer to a driver where this function belongs
+ * @param path      path to the file (e.g. /folder/file.txt)
+ * @param mode      read: FS_MODE_RD, write: FS_MODE_WR, both: FS_MODE_RD | FS_MODE_WR
+ * @return          a file descriptor or NULL on error
+ */
+static void * fs_open(lv_fs_drv_t * drv, const char * path, lv_fs_mode_t mode)
+{
+    lv_fs_res_t res = LV_FS_RES_NOT_IMP;
+
+    void * f = NULL;
+
+    if(mode == LV_FS_MODE_RD) {
+        /*Open a file for read*/
+        res = rawfs_file_find(path, &FIL);
+    }
+    else if(mode == (LV_FS_MODE_WR | LV_FS_MODE_RD)) {
+        /*Open a file for read and write*/
+        res = rawfs_file_find(path, &FIL);
+    }
+
+    if(res == LV_FS_RES_OK) {
+        f = &FIL;
+    }
+
+    return f;
+}
+
+/**
+ * Close an opened file
+ * @param drv       pointer to a driver where this function belongs
+ * @param file_p    pointer to a file_t variable. (opened with fs_open)
+ * @return          LV_FS_RES_OK: no error or  any error from @lv_fs_res_t enum
+ */
+static lv_fs_res_t fs_close(lv_fs_drv_t * drv, void * file_p)
+{
+    lv_fs_res_t res = LV_FS_RES_OK;
+
+    fs_init();
+
+    return res;
+}
+
+/**
+ * Read data from an opened file
+ * @param drv       pointer to a driver where this function belongs
+ * @param file_p    pointer to a file_t variable.
+ * @param buf       pointer to a memory block where to store the read data
+ * @param btr       number of Bytes To Read
+ * @param br        the real number of read bytes (Byte Read)
+ * @return          LV_FS_RES_OK: no error or  any error from @lv_fs_res_t enum
+ */
+static lv_fs_res_t fs_read(lv_fs_drv_t * drv, void * file_p, void * buf, uint32_t btr, uint32_t * br)
+{
+    lv_fs_res_t res = LV_FS_RES_OK;
+
+    rawfs_file_t * p = (rawfs_file_t *)file_p;
+#if LV_FS_RAWFS_XIP
+    /*For XIP flash, copy directly*/
+    lv_memcpy((uint8_t *)buf, (uint8_t *)(LV_FS_RAWFS_XIP_BASE_ADDR + p->base + p->offset), btr);
+#else
+    /*For Non-XIP flash, use driver API*/
+    //W25QXX_Read((unit8_t *)buf, (unit8_t *)(p->base + p->offset), btr);
+    res = LV_FS_RES_NOT_IMP;
+#endif
+    p->offset += btr;
+    *br = btr;
+
+    return res;
+}
+
+/**
+ * Write into a file
+ * @param drv       pointer to a driver where this function belongs
+ * @param file_p    pointer to a file_t variable
+ * @param buf       pointer to a buffer with the bytes to write
+ * @param btw       Bytes To Write
+ * @param bw        the number of real written bytes (Bytes Written). NULL if unused.
+ * @return          LV_FS_RES_OK: no error or  any error from @lv_fs_res_t enum
+ */
+static lv_fs_res_t fs_write(lv_fs_drv_t * drv, void * file_p, const void * buf, uint32_t btw, uint32_t * bw)
+{
+    lv_fs_res_t res = LV_FS_RES_NOT_IMP;
+
+    /*Add your code here*/
+
+    return res;
+}
+
+/**
+ * Set the read write pointer. Also expand the file size if necessary.
+ * @param drv       pointer to a driver where this function belongs
+ * @param file_p    pointer to a file_t variable. (opened with fs_open )
+ * @param pos       the new position of read write pointer
+ * @param whence    tells from where to interpret the `pos`. See @lv_fs_whence_t
+ * @return          LV_FS_RES_OK: no error or  any error from @lv_fs_res_t enum
+ */
+static lv_fs_res_t fs_seek(lv_fs_drv_t * drv, void * file_p, uint32_t pos, lv_fs_whence_t whence)
+{
+    lv_fs_res_t res = LV_FS_RES_OK;
+
+    rawfs_file_t * p = (rawfs_file_t *)file_p;
+    if(whence == LV_FS_SEEK_SET) {
+        p->offset = pos;
+    }
+    else if(whence == LV_FS_SEEK_CUR) {
+        p->offset += pos;
+    }
+    else if(whence == LV_FS_SEEK_END) {
+        p->offset = p->size + pos;
+    }
+    else {
+        res = LV_FS_RES_NOT_IMP;
+    }
+
+    return res;
+}
+
+/**
+ * Give the position of the read write pointer
+ * @param drv       pointer to a driver where this function belongs
+ * @param file_p    pointer to a file_t variable.
+ * @param pos_p     pointer to to store the result
+ * @return          LV_FS_RES_OK: no error or  any error from @lv_fs_res_t enum
+ */
+static lv_fs_res_t fs_tell(lv_fs_drv_t * drv, void * file_p, uint32_t * pos_p)
+{
+    lv_fs_res_t res = LV_FS_RES_OK;
+
+    rawfs_file_t * p = (rawfs_file_t *)file_p;
+    *pos_p = p->offset;
+
+    return res;
+}
+
+/**
+ * Initialize a 'lv_fs_dir_t' variable for directory reading
+ * @param drv       pointer to a driver where this function belongs
+ * @param path      path to a directory
+ * @return          pointer to the directory read descriptor or NULL on error
+ */
+static void * fs_dir_open(lv_fs_drv_t * drv, const char * path)
+{
+    void * dir = NULL;
+    /*Add your code here*/
+    return dir;
+}
+
+/**
+ * Read the next filename form a directory.
+ * The name of the directories will begin with '/'
+ * @param drv       pointer to a driver where this function belongs
+ * @param rddir_p   pointer to an initialized 'lv_fs_dir_t' variable
+ * @param fn        pointer to a buffer to store the filename
+ * @return          LV_FS_RES_OK: no error or  any error from @lv_fs_res_t enum
+ */
+static lv_fs_res_t fs_dir_read(lv_fs_drv_t * drv, void * rddir_p, char * fn)
+{
+    lv_fs_res_t res = LV_FS_RES_NOT_IMP;
+
+    /*Add your code here*/
+
+    return res;
+}
+
+/**
+ * Close the directory reading
+ * @param drv       pointer to a driver where this function belongs
+ * @param rddir_p   pointer to an initialized 'lv_fs_dir_t' variable
+ * @return          LV_FS_RES_OK: no error or  any error from @lv_fs_res_t enum
+ */
+static lv_fs_res_t fs_dir_close(lv_fs_drv_t * drv, void * rddir_p)
+{
+    lv_fs_res_t res = LV_FS_RES_NOT_IMP;
+
+    /*Add your code here*/
+
+    return res;
+}
+
+#endif /*LV_USE_FS_RAWFS*/
+
diff --git a/src/extra/libs/fsdrv/lv_fsdrv.h b/src/extra/libs/fsdrv/lv_fsdrv.h
index b864ad6f3..0d94465b8 100644
--- a/src/extra/libs/fsdrv/lv_fsdrv.h
+++ b/src/extra/libs/fsdrv/lv_fsdrv.h
@@ -36,6 +36,22 @@ void lv_fs_littlefs_init(void);
 lv_fs_drv_t * lv_fs_littlefs_set_driver(char label, void * lfs_p);
 #endif
 
+#if LV_USE_FS_RAWFS != '\0'
+#include "stdint.h"
+
+typedef uint32_t rawfs_addr_t;
+typedef uint32_t rawfs_size_t;
+
+typedef struct _rawfs_file_t {
+    rawfs_addr_t base;
+    rawfs_addr_t offset;
+    rawfs_size_t size;
+    char * name;
+} rawfs_file_t;
+
+void lv_fs_rawfs_init(void);
+#endif
+
 #if LV_USE_FS_STDIO != '\0'
 void lv_fs_stdio_init(void);
 #endif
diff --git a/src/extra/libs/lv_libs.h b/src/extra/libs/lv_libs.h
index 1fefe6c11..749b553df 100644
--- a/src/extra/libs/lv_libs.h
+++ b/src/extra/libs/lv_libs.h
@@ -13,6 +13,7 @@ extern "C" {
 /*********************
  *      INCLUDES
  *********************/
+#include "barcode/lv_barcode.h"
 #include "bmp/lv_bmp.h"
 #include "fsdrv/lv_fsdrv.h"
 #include "png/lv_png.h"
diff --git a/src/extra/lv_extra.c b/src/extra/lv_extra.c
index bf9f68448..ee1a4623f 100644
--- a/src/extra/lv_extra.c
+++ b/src/extra/lv_extra.c
@@ -54,6 +54,10 @@ void lv_extra_init(void)
     lv_fs_littlefs_init();
 #endif
 
+#if LV_USE_FS_RAWFS != '\0'
+    lv_fs_rawfs_init();
+#endif
+
 #if LV_USE_FS_STDIO != '\0'
     lv_fs_stdio_init();
 #endif
diff --git a/src/extra/others/ime/lv_ime_pinyin.c b/src/extra/others/ime/lv_ime_pinyin.c
index 9834154fb..3939e6c19 100644
--- a/src/extra/others/ime/lv_ime_pinyin.c
+++ b/src/extra/others/ime/lv_ime_pinyin.c
@@ -572,7 +572,7 @@ static void lv_ime_pinyin_constructor(const lv_obj_class_t * class_p, lv_obj_t *
 #endif
 
     /* Init pinyin_ime->cand_panel */
-    pinyin_ime->cand_panel = lv_btnmatrix_create(lv_scr_act());
+    pinyin_ime->cand_panel = lv_btnmatrix_create(obj);
     lv_btnmatrix_set_map(pinyin_ime->cand_panel, (const char **)lv_btnm_def_pinyin_sel_map);
     lv_obj_set_size(pinyin_ime->cand_panel, LV_PCT(100), LV_PCT(5));
     lv_obj_add_flag(pinyin_ime->cand_panel, LV_OBJ_FLAG_HIDDEN);
diff --git a/src/extra/themes/default/lv_theme_default.c b/src/extra/themes/default/lv_theme_default.c
index 47392b0d1..34d6370e6 100644
--- a/src/extra/themes/default/lv_theme_default.c
+++ b/src/extra/themes/default/lv_theme_default.c
@@ -97,6 +97,10 @@ typedef struct {
     lv_style_t cb_marker, cb_marker_checked;
 #endif
 
+#if LV_USE_RADIOBTN
+    lv_style_t rb_marker, rb_marker_checked, rb_marker_inner, rb_marker_inner_checked;
+#endif
+
 #if LV_USE_SWITCH
     lv_style_t switch_knob;
 #endif
@@ -109,7 +113,7 @@ typedef struct {
     lv_style_t table_cell;
 #endif
 
-#if LV_USE_METER
+#if LV_USE_METER || LV_USE_ANALOGCLOCK
     lv_style_t meter_marker, meter_indic;
 #endif
 
@@ -125,6 +129,10 @@ typedef struct {
     lv_style_t colorwheel_main;
 #endif
 
+#if LV_USE_CAROUSEL
+    lv_style_t outline_focus, outline_disabled;
+#endif
+
 #if LV_USE_MENU
     lv_style_t menu_bg, menu_cont, menu_sidebar_cont, menu_main_cont, menu_page, menu_header_cont, menu_header_btn,
                menu_section, menu_pressed, menu_separator;
@@ -427,6 +435,30 @@ static void style_init(void)
     lv_style_set_text_font(&styles->cb_marker_checked, theme.font_small);
 #endif
 
+#if LV_USE_RADIOBTN
+    style_init_reset(&styles->rb_marker);
+    lv_style_set_pad_all(&styles->rb_marker, lv_disp_dpx(theme.disp, 3));
+    lv_style_set_border_width(&styles->rb_marker, BORDER_WIDTH);
+    lv_style_set_border_color(&styles->rb_marker, theme.color_primary);
+    lv_style_set_bg_color(&styles->rb_marker, color_card);
+    lv_style_set_bg_opa(&styles->rb_marker, LV_OPA_COVER);
+    lv_style_set_radius(&styles->rb_marker, LV_RADIUS_CIRCLE);
+
+    style_init_reset(&styles->rb_marker_checked);
+    lv_style_set_bg_color(&styles->rb_marker_checked, color_card);
+    lv_style_set_bg_opa(&styles->rb_marker_checked, LV_OPA_COVER);
+
+    style_init_reset(&styles->rb_marker_inner);
+    lv_style_set_border_opa(&styles->rb_marker_inner, LV_OPA_TRANSP);
+    lv_style_set_bg_opa(&styles->rb_marker_inner, LV_OPA_TRANSP);
+    lv_style_set_radius(&styles->rb_marker_inner, LV_RADIUS_CIRCLE);
+
+    style_init_reset(&styles->rb_marker_inner_checked);
+    lv_style_set_border_opa(&styles->rb_marker_inner_checked, LV_OPA_TRANSP);
+    lv_style_set_bg_color(&styles->rb_marker_inner_checked, theme.color_primary);
+    lv_style_set_bg_opa(&styles->rb_marker_inner_checked, LV_OPA_COVER);
+#endif
+
 #if LV_USE_SWITCH
     style_init_reset(&styles->switch_knob);
     lv_style_set_pad_all(&styles->switch_knob, - lv_disp_dpx(theme.disp, 4));
@@ -525,7 +557,7 @@ static void style_init(void)
     lv_style_set_pad_ver(&styles->menu_separator, PAD_TINY);
 #endif
 
-#if LV_USE_METER
+#if LV_USE_METER || LV_USE_ANALOGCLOCK
     style_init_reset(&styles->meter_marker);
     lv_style_set_line_width(&styles->meter_marker, lv_disp_dpx(theme.disp, 5));
     lv_style_set_line_color(&styles->meter_marker, color_text);
@@ -582,6 +614,17 @@ static void style_init(void)
     lv_style_set_arc_width(&styles->colorwheel_main, lv_disp_dpx(theme.disp, 10));
 #endif
 
+#if LV_USE_CAROUSEL
+    style_init_reset(&styles->outline_focus);
+    lv_style_set_outline_color(&styles->outline_focus, lv_palette_main(LV_PALETTE_BLUE));
+    lv_style_set_outline_width(&styles->outline_focus, OUTLINE_WIDTH);
+    lv_style_set_outline_opa(&styles->outline_focus, LV_OPA_50);
+    style_init_reset(&styles->outline_disabled);
+    lv_style_set_outline_color(&styles->outline_disabled, lv_palette_main(LV_PALETTE_GREY));
+    lv_style_set_outline_width(&styles->outline_disabled, OUTLINE_WIDTH);
+    lv_style_set_outline_opa(&styles->outline_disabled, LV_OPA_50);
+#endif
+
 #if LV_USE_MSGBOX
     /*To add space for for the button shadow*/
     style_init_reset(&styles->msgbox_btn_bg);
@@ -899,6 +942,25 @@ static void theme_apply(lv_theme_t * th, lv_obj_t * obj)
     }
 #endif
 
+#if LV_USE_RADIOBTN
+    else if(lv_obj_check_type(obj, &lv_radiobtn_item_class)) {
+        lv_obj_add_style(obj, &styles->pad_gap, 0);
+        lv_obj_add_style(obj, &styles->outline_primary, LV_STATE_FOCUS_KEY);
+        lv_obj_add_style(obj, &styles->disabled, LV_PART_INDICATOR | LV_STATE_DISABLED);
+        lv_obj_add_style(obj, &styles->rb_marker, LV_PART_INDICATOR);
+        lv_obj_add_style(obj, &styles->rb_marker_inner, LV_PART_CUSTOM_FIRST);
+        lv_obj_add_style(obj, &styles->rb_marker_checked, LV_PART_INDICATOR | LV_STATE_CHECKED);
+        lv_obj_add_style(obj, &styles->rb_marker_inner_checked, LV_PART_CUSTOM_FIRST | LV_STATE_CHECKED);
+        lv_obj_add_style(obj, &styles->pressed, LV_PART_INDICATOR | LV_STATE_PRESSED);
+        lv_obj_add_style(obj, &styles->pressed, LV_PART_CUSTOM_FIRST | LV_STATE_PRESSED);
+#if LV_THEME_DEFAULT_GROW
+        lv_obj_add_style(obj, &styles->grow, LV_PART_INDICATOR | LV_STATE_PRESSED);
+#endif
+        lv_obj_add_style(obj, &styles->transition_normal, LV_PART_INDICATOR | LV_STATE_PRESSED);
+        lv_obj_add_style(obj, &styles->transition_delayed, LV_PART_INDICATOR);
+    }
+#endif
+
 #if LV_USE_SWITCH
     else if(lv_obj_check_type(obj, &lv_switch_class)) {
         lv_obj_add_style(obj, &styles->bg_color_grey, 0);
@@ -995,6 +1057,14 @@ static void theme_apply(lv_theme_t * th, lv_obj_t * obj)
     }
 #endif
 
+#if LV_USE_ANALOGCLOCK
+    else if(lv_obj_check_type(obj, &lv_analogclock_class)) {
+        lv_obj_add_style(obj, &styles->card, 0);
+        lv_obj_add_style(obj, &styles->circle, 0);
+        lv_obj_add_style(obj, &styles->meter_indic, LV_PART_INDICATOR);
+    }
+#endif
+
 #if LV_USE_TEXTAREA
     else if(lv_obj_check_type(obj, &lv_textarea_class)) {
         lv_obj_add_style(obj, &styles->card, 0);
@@ -1135,6 +1205,20 @@ static void theme_apply(lv_theme_t * th, lv_obj_t * obj)
     }
 #endif
 
+#if LV_USE_CAROUSEL
+    else if(lv_obj_check_type(obj, &lv_carousel_class)) {
+        lv_obj_add_style(obj, &styles->scr, 0);
+        lv_obj_add_style(obj, &styles->scrollbar, LV_PART_SCROLLBAR);
+        lv_obj_add_style(obj, &styles->scrollbar_scrolled, LV_PART_SCROLLBAR | LV_STATE_SCROLLED);
+    }
+    else if(lv_obj_check_type(obj, &lv_carousel_element_class)) {
+        lv_obj_add_style(obj, &styles->scrollbar, LV_PART_SCROLLBAR);
+        lv_obj_add_style(obj, &styles->scrollbar_scrolled, LV_PART_SCROLLBAR | LV_STATE_SCROLLED);
+        lv_obj_add_style(obj, &styles->outline_focus, LV_STATE_FOCUSED);
+        lv_obj_add_style(obj, &styles->outline_disabled, LV_STATE_DEFAULT);
+    }
+#endif
+
 #if LV_USE_TABVIEW
     else if(lv_obj_check_type(obj, &lv_tabview_class)) {
         lv_obj_add_style(obj, &styles->scr, 0);
diff --git a/src/extra/widgets/analogclock/lv_analogclock.c b/src/extra/widgets/analogclock/lv_analogclock.c
new file mode 100644
index 000000000..b7e6c3e78
--- /dev/null
+++ b/src/extra/widgets/analogclock/lv_analogclock.c
@@ -0,0 +1,803 @@
+/**
+ * @file lv_analogclock.c
+ *
+ */
+
+/*********************
+ *      INCLUDES
+ *********************/
+#include "lv_analogclock.h"
+#if LV_USE_ANALOGCLOCK != 0
+
+#include "../../../misc/lv_assert.h"
+
+/*********************
+ *      DEFINES
+ *********************/
+#define MY_CLASS &lv_analogclock_class
+
+/**********************
+ *      TYPEDEFS
+ **********************/
+
+/**********************
+ *  STATIC PROTOTYPES
+ **********************/
+static void lv_analogclock_constructor(const lv_obj_class_t * class_p, lv_obj_t * obj);
+static void lv_analogclock_destructor(const lv_obj_class_t * class_p, lv_obj_t * obj);
+static void lv_analogclock_event(const lv_obj_class_t * class_p, lv_event_t * e);
+static void draw_arcs(lv_obj_t * obj, lv_draw_ctx_t * draw_ctx, const lv_area_t * scale_area);
+static void draw_ticks_and_labels(lv_obj_t * obj, lv_draw_ctx_t * draw_ctx, const lv_area_t * scale_area);
+static void draw_needles(lv_obj_t * obj, lv_draw_ctx_t * draw_ctx, const lv_area_t * scale_area);
+static void inv_arc(lv_obj_t * obj, lv_analogclock_indicator_t * indic, int32_t old_value, int32_t new_value);
+static void inv_line(lv_obj_t * obj, lv_analogclock_indicator_t * indic, int32_t value);
+
+/**********************
+ *  STATIC VARIABLES
+ **********************/
+const lv_obj_class_t lv_analogclock_class = {
+    .constructor_cb = lv_analogclock_constructor,
+    .destructor_cb = lv_analogclock_destructor,
+    .event_cb = lv_analogclock_event,
+    .instance_size = sizeof(lv_analogclock_t),
+    .base_class = &lv_obj_class
+};
+
+/**********************
+ *      MACROS
+ **********************/
+
+/**********************
+ *   GLOBAL FUNCTIONS
+ **********************/
+
+lv_obj_t * lv_analogclock_create(lv_obj_t * parent)
+{
+    LV_LOG_INFO("begin");
+    lv_obj_t * obj = lv_obj_class_create_obj(MY_CLASS, parent);
+    lv_obj_class_init_obj(obj);
+    return obj;
+}
+
+/*=====================
+ * Add scale
+ *====================*/
+
+lv_analogclock_scale_t * lv_analogclock_add_scale(lv_obj_t * obj)
+{
+    LV_ASSERT_OBJ(obj, MY_CLASS);
+    lv_analogclock_t * analogclock = (lv_analogclock_t *)obj;
+
+    lv_analogclock_scale_t * scale = _lv_ll_ins_head(&analogclock->scale_ll);
+    LV_ASSERT_MALLOC(scale);
+    lv_memset_00(scale, sizeof(lv_analogclock_scale_t));
+
+    // scale->angle_range = 270;
+    // scale->rotation = 90 + (360 - scale->angle_range) / 2;
+    // scale->min = 0;
+    // scale->max = 100;
+    // scale->tick_cnt = 6;
+    // scale->tick_length = 8;
+    // scale->tick_width = 2;
+    // scale->label_gap = 2;
+    scale->angle_range = 360;
+    // scale->rotation = 0;
+    // scale->min = 0;
+    // scale->max = 100;
+    scale->tick_cnt = 61;
+    scale->tick_length = 8;
+    scale->tick_width = 2;
+    // scale->tick_color = color;
+    scale->hide_label = false;
+    scale->label_gap = 10;
+    scale->tick_major_nth = 5;
+    scale->tick_major_width = 3;
+    scale->tick_major_length = 12;
+    // scale->tick_major_color = color;
+
+    scale->min = 0;
+    scale->max = 60;
+    scale->rotation = 270;
+
+    return scale;
+}
+
+void lv_analogclock_set_ticks(lv_obj_t * obj, uint16_t width, uint16_t len, lv_color_t color)
+{
+    lv_analogclock_t * analogclock = (lv_analogclock_t *)obj;
+    lv_analogclock_scale_t * scale = analogclock->scale;
+    scale->tick_width = width;
+    scale->tick_length = len;
+    scale->tick_color = color;
+    lv_obj_invalidate(obj);
+}
+
+void lv_analogclock_set_major_ticks(lv_obj_t * obj, uint16_t width, uint16_t len, lv_color_t color, int16_t label_gap)
+{
+    lv_analogclock_t * analogclock = (lv_analogclock_t *)obj;
+    lv_analogclock_scale_t * scale = analogclock->scale;
+    scale->tick_major_width = width;
+    scale->tick_major_length = len;
+    scale->tick_major_color = color;
+    scale->label_gap = label_gap;
+    lv_obj_invalidate(obj);
+}
+
+void lv_analogclock_set_scale_range(lv_obj_t * obj, lv_analogclock_scale_t * scale, int32_t min, int32_t max,
+                                    uint32_t angle_range,
+                                    uint32_t rotation)
+{
+    scale->min = min;
+    scale->max = max;
+    scale->angle_range = angle_range;
+    scale->rotation = rotation;
+    lv_obj_invalidate(obj);
+}
+
+/*=====================
+ * Add indicator
+ *====================*/
+void lv_analogclock_set_hour_needle_line(lv_obj_t * obj,  uint16_t width,
+                                         lv_color_t color, int16_t r_mod)
+{
+    lv_analogclock_t * analogclock = (lv_analogclock_t *)obj;
+    analogclock->hour_indic = lv_analogclock_add_needle_line(obj, analogclock->scale, width, color, r_mod);
+}
+
+void lv_analogclock_set_min_needle_line(lv_obj_t * obj, uint16_t width,
+                                        lv_color_t color, int16_t r_mod)
+{
+    lv_analogclock_t * analogclock = (lv_analogclock_t *)obj;
+    analogclock->min_indic = lv_analogclock_add_needle_line(obj, analogclock->scale, width, color, r_mod);
+}
+
+void lv_analogclock_set_sec_needle_line(lv_obj_t * obj, uint16_t width,
+                                        lv_color_t color, int16_t r_mod)
+{
+    lv_analogclock_t * analogclock = (lv_analogclock_t *)obj;
+    analogclock->sec_indic = lv_analogclock_add_needle_line(obj, analogclock->scale, width, color, r_mod);
+}
+
+lv_analogclock_indicator_t * lv_analogclock_add_needle_line(lv_obj_t * obj, lv_analogclock_scale_t * scale,
+                                                            uint16_t width,
+                                                            lv_color_t color, int16_t r_mod)
+{
+    LV_ASSERT_OBJ(obj, MY_CLASS);
+    lv_analogclock_t * analogclock = (lv_analogclock_t *)obj;
+    lv_analogclock_indicator_t * indic = _lv_ll_ins_head(&analogclock->indicator_ll);
+    LV_ASSERT_MALLOC(indic);
+    lv_memset_00(indic, sizeof(lv_analogclock_indicator_t));
+    indic->scale = scale;
+    indic->opa = LV_OPA_COVER;
+
+    indic->type = LV_analogclock_INDICATOR_TYPE_NEEDLE_LINE;
+    indic->type_data.needle_line.width = width;
+    indic->type_data.needle_line.color = color;
+    indic->type_data.needle_line.r_mod = r_mod;
+    lv_obj_invalidate(obj);
+
+    return indic;
+}
+
+void lv_analogclock_set_hour_needle_img(lv_obj_t * obj, const void * src,
+                                        lv_coord_t pivot_x, lv_coord_t pivot_y)
+{
+    lv_analogclock_t * analogclock = (lv_analogclock_t *)obj;
+    analogclock->hour_indic = lv_analogclock_add_needle_img(obj, analogclock->scale, src, pivot_x, pivot_y);
+}
+
+void lv_analogclock_set_min_needle_img(lv_obj_t * obj, const void * src,
+                                       lv_coord_t pivot_x, lv_coord_t pivot_y)
+{
+    lv_analogclock_t * analogclock = (lv_analogclock_t *)obj;
+    analogclock->min_indic = lv_analogclock_add_needle_img(obj, analogclock->scale, src, pivot_x, pivot_y);
+}
+
+void lv_analogclock_set_sec_needle_img(lv_obj_t * obj, const void * src,
+                                       lv_coord_t pivot_x, lv_coord_t pivot_y)
+{
+    lv_analogclock_t * analogclock = (lv_analogclock_t *)obj;
+    analogclock->sec_indic = lv_analogclock_add_needle_img(obj, analogclock->scale, src, pivot_x, pivot_y);
+}
+
+lv_analogclock_indicator_t * lv_analogclock_add_needle_img(lv_obj_t * obj, lv_analogclock_scale_t * scale,
+                                                           const void * src,
+                                                           lv_coord_t pivot_x, lv_coord_t pivot_y)
+{
+    LV_ASSERT_OBJ(obj, MY_CLASS);
+    lv_analogclock_t * analogclock = (lv_analogclock_t *)obj;
+    lv_analogclock_indicator_t * indic = _lv_ll_ins_head(&analogclock->indicator_ll);
+    LV_ASSERT_MALLOC(indic);
+    lv_memset_00(indic, sizeof(lv_analogclock_indicator_t));
+    indic->scale = scale;
+    indic->opa = LV_OPA_COVER;
+
+    indic->type = LV_analogclock_INDICATOR_TYPE_NEEDLE_IMG;
+    indic->type_data.needle_img.src = src;
+    indic->type_data.needle_img.pivot.x = pivot_x;
+    indic->type_data.needle_img.pivot.y = pivot_y;
+    lv_obj_invalidate(obj);
+
+    return indic;
+}
+
+lv_analogclock_indicator_t * lv_analogclock_add_arc(lv_obj_t * obj, uint16_t width, lv_color_t color,
+                                                    int16_t r_mod)
+{
+    LV_ASSERT_OBJ(obj, MY_CLASS);
+    lv_analogclock_t * analogclock = (lv_analogclock_t *)obj;
+    lv_analogclock_indicator_t * indic = _lv_ll_ins_head(&analogclock->indicator_ll);
+    LV_ASSERT_MALLOC(indic);
+    lv_memset_00(indic, sizeof(lv_analogclock_indicator_t));
+    indic->scale = analogclock->scale;
+    indic->opa = LV_OPA_COVER;
+
+    indic->type = LV_analogclock_INDICATOR_TYPE_ARC;
+    indic->type_data.arc.width = width;
+    indic->type_data.arc.color = color;
+    indic->type_data.arc.r_mod = r_mod;
+
+    lv_obj_invalidate(obj);
+    return indic;
+}
+
+lv_analogclock_indicator_t * lv_analogclock_add_scale_lines(lv_obj_t * obj,  lv_color_t color_start,
+                                                            lv_color_t color_end, bool local, int16_t width_mod)
+{
+    LV_ASSERT_OBJ(obj, MY_CLASS);
+    lv_analogclock_t * analogclock = (lv_analogclock_t *)obj;
+    lv_analogclock_indicator_t * indic = _lv_ll_ins_head(&analogclock->indicator_ll);
+    LV_ASSERT_MALLOC(indic);
+    lv_memset_00(indic, sizeof(lv_analogclock_indicator_t));
+    indic->scale = analogclock->scale;
+    indic->opa = LV_OPA_COVER;
+
+    indic->type = LV_analogclock_INDICATOR_TYPE_SCALE_LINES;
+    indic->type_data.scale_lines.color_start = color_start;
+    indic->type_data.scale_lines.color_end = color_end;
+    indic->type_data.scale_lines.local_grad = local;
+    indic->type_data.scale_lines.width_mod = width_mod;
+
+    lv_obj_invalidate(obj);
+    return indic;
+}
+
+/*=====================
+ * Hide digits / center point
+ *====================*/
+void lv_analogclock_hide_digits(lv_obj_t * obj, bool hide_digits)
+{
+    lv_analogclock_t * analogclock = (lv_analogclock_t *)obj;
+    lv_analogclock_scale_t * scale = analogclock->scale;
+    scale->hide_label = hide_digits;
+}
+
+void lv_analogclock_hide_point(lv_obj_t * obj, bool hide_point)
+{
+    lv_analogclock_t * analogclock = (lv_analogclock_t *)obj;
+    analogclock->hide_point = hide_point;
+}
+
+/*=====================
+ * Set indicator value
+ *====================*/
+
+void lv_analogclock_set_indicator_value(lv_obj_t * obj, lv_analogclock_indicator_t * indic, int32_t value)
+{
+    int32_t old_start = indic->start_value;
+    int32_t old_end = indic->end_value;
+    indic->start_value = value;
+    indic->end_value = value;
+
+    if(indic->type == LV_analogclock_INDICATOR_TYPE_ARC) {
+        inv_arc(obj, indic, old_start, value);
+        inv_arc(obj, indic, old_end, value);
+    }
+    else if(indic->type == LV_analogclock_INDICATOR_TYPE_NEEDLE_IMG ||
+            indic->type == LV_analogclock_INDICATOR_TYPE_NEEDLE_LINE) {
+        inv_line(obj, indic, old_start);
+        inv_line(obj, indic, old_end);
+        inv_line(obj, indic, value);
+    }
+    else {
+        lv_obj_invalidate(obj);
+    }
+}
+
+void lv_analogclock_set_indicator_start_value(lv_obj_t * obj, lv_analogclock_indicator_t * indic, int32_t value)
+{
+    int32_t old_value = indic->start_value;
+    indic->start_value = value;
+
+    if(indic->type == LV_analogclock_INDICATOR_TYPE_ARC) {
+        inv_arc(obj, indic, old_value, value);
+    }
+    else if(indic->type == LV_analogclock_INDICATOR_TYPE_NEEDLE_IMG ||
+            indic->type == LV_analogclock_INDICATOR_TYPE_NEEDLE_LINE) {
+        inv_line(obj, indic, old_value);
+        inv_line(obj, indic, value);
+    }
+    else {
+        lv_obj_invalidate(obj);
+    }
+}
+
+void lv_analogclock_set_indicator_end_value(lv_obj_t * obj, lv_analogclock_indicator_t * indic, int32_t value)
+{
+    int32_t old_value = indic->end_value;
+    indic->end_value = value;
+
+    if(indic->type == LV_analogclock_INDICATOR_TYPE_ARC) {
+        inv_arc(obj, indic, old_value, value);
+    }
+    else if(indic->type == LV_analogclock_INDICATOR_TYPE_NEEDLE_IMG ||
+            indic->type == LV_analogclock_INDICATOR_TYPE_NEEDLE_LINE) {
+        inv_line(obj, indic, old_value);
+        inv_line(obj, indic, value);
+    }
+    else {
+        lv_obj_invalidate(obj);
+    }
+}
+
+void lv_analogclock_set_time(lv_obj_t * obj, int32_t hour, int32_t min, int32_t sec)
+{
+    lv_analogclock_t * analogclock = (lv_analogclock_t *)obj;
+    lv_analogclock_set_indicator_value(obj, analogclock->hour_indic, 5 * hour + min / 12);
+    lv_analogclock_set_indicator_value(obj, analogclock->min_indic, min);
+    lv_analogclock_set_indicator_value(obj, analogclock->sec_indic, sec);
+}
+
+/**********************
+ *   STATIC FUNCTIONS
+ **********************/
+
+static void lv_analogclock_constructor(const lv_obj_class_t * class_p, lv_obj_t * obj)
+{
+    LV_UNUSED(class_p);
+    LV_TRACE_OBJ_CREATE("begin");
+
+    lv_analogclock_t * analogclock = (lv_analogclock_t *)obj;
+
+    _lv_ll_init(&analogclock->scale_ll, sizeof(lv_analogclock_scale_t));
+    _lv_ll_init(&analogclock->indicator_ll, sizeof(lv_analogclock_indicator_t));
+    analogclock->scale = lv_analogclock_add_scale(obj);
+    analogclock->hide_point = false;
+
+    LV_TRACE_OBJ_CREATE("finished");
+}
+
+static void lv_analogclock_destructor(const lv_obj_class_t * class_p, lv_obj_t * obj)
+{
+    LV_UNUSED(class_p);
+    LV_ASSERT_OBJ(obj, MY_CLASS);
+    lv_analogclock_t * analogclock = (lv_analogclock_t *)obj;
+    _lv_ll_clear(&analogclock->indicator_ll);
+    _lv_ll_clear(&analogclock->scale_ll);
+
+}
+
+static void lv_analogclock_event(const lv_obj_class_t * class_p, lv_event_t * e)
+{
+    LV_UNUSED(class_p);
+
+    lv_res_t res = lv_obj_event_base(MY_CLASS, e);
+    if(res != LV_RES_OK) return;
+
+    lv_event_code_t code = lv_event_get_code(e);
+    lv_obj_t * obj = lv_event_get_target(e);
+    lv_analogclock_t * analogclock = (lv_analogclock_t *)obj;
+    if(code == LV_EVENT_DRAW_MAIN) {
+        lv_draw_ctx_t * draw_ctx = lv_event_get_draw_ctx(e);
+        lv_area_t scale_area;
+        lv_obj_get_content_coords(obj, &scale_area);
+
+        draw_arcs(obj, draw_ctx, &scale_area);
+        draw_ticks_and_labels(obj, draw_ctx, &scale_area);
+        draw_needles(obj, draw_ctx, &scale_area);
+
+        if(!analogclock->hide_point) {
+            lv_coord_t r_edge = lv_area_get_width(&scale_area) / 2;
+            lv_point_t scale_center;
+            scale_center.x = scale_area.x1 + r_edge;
+            scale_center.y = scale_area.y1 + r_edge;
+
+            lv_draw_rect_dsc_t mid_dsc;
+            lv_draw_rect_dsc_init(&mid_dsc);
+            lv_obj_init_draw_rect_dsc(obj, LV_PART_INDICATOR, &mid_dsc);
+            lv_coord_t w = lv_obj_get_style_width(obj, LV_PART_INDICATOR) / 2;
+            lv_coord_t h = lv_obj_get_style_height(obj, LV_PART_INDICATOR) / 2;
+            lv_area_t nm_cord;
+            nm_cord.x1 = scale_center.x - w;
+            nm_cord.y1 = scale_center.y - h;
+            nm_cord.x2 = scale_center.x + w;
+            nm_cord.y2 = scale_center.y + h;
+            lv_draw_rect(draw_ctx, &mid_dsc, &nm_cord);
+        }
+    }
+}
+
+static void draw_arcs(lv_obj_t * obj, lv_draw_ctx_t * draw_ctx, const lv_area_t * scale_area)
+{
+    lv_analogclock_t * analogclock = (lv_analogclock_t *)obj;
+
+    lv_draw_arc_dsc_t arc_dsc;
+    lv_draw_arc_dsc_init(&arc_dsc);
+    arc_dsc.rounded = lv_obj_get_style_arc_rounded(obj, LV_PART_ITEMS);
+
+    lv_coord_t r_out = lv_area_get_width(scale_area) / 2 ;
+    lv_point_t scale_center;
+    scale_center.x = scale_area->x1 + r_out;
+    scale_center.y = scale_area->y1 + r_out;
+
+    lv_opa_t opa_main = lv_obj_get_style_opa(obj, LV_PART_MAIN);
+    lv_analogclock_indicator_t * indic;
+
+    lv_obj_draw_part_dsc_t part_draw_dsc;
+    lv_obj_draw_dsc_init(&part_draw_dsc, draw_ctx);
+    part_draw_dsc.arc_dsc = &arc_dsc;
+    part_draw_dsc.part = LV_PART_INDICATOR;
+    part_draw_dsc.class_p = MY_CLASS;
+    part_draw_dsc.type = LV_analogclock_DRAW_PART_ARC;
+
+    _LV_LL_READ_BACK(&analogclock->indicator_ll, indic) {
+        if(indic->type != LV_analogclock_INDICATOR_TYPE_ARC) continue;
+
+        arc_dsc.color = indic->type_data.arc.color;
+        arc_dsc.width = indic->type_data.arc.width;
+        arc_dsc.opa = indic->opa > LV_OPA_MAX ? opa_main : (opa_main * indic->opa) >> 8;
+
+        lv_analogclock_scale_t * scale = indic->scale;
+
+        int32_t start_angle = lv_map(indic->start_value, scale->min, scale->max, scale->rotation,
+                                     scale->rotation + scale->angle_range);
+        int32_t end_angle = lv_map(indic->end_value, scale->min, scale->max, scale->rotation,
+                                   scale->rotation + scale->angle_range);
+
+        part_draw_dsc.radius = r_out + indic->type_data.arc.r_mod;
+        part_draw_dsc.sub_part_ptr = indic;
+        part_draw_dsc.p1 = &scale_center;
+
+        lv_event_send(obj, LV_EVENT_DRAW_PART_BEGIN, &part_draw_dsc);
+        lv_draw_arc(draw_ctx, &arc_dsc, &scale_center, part_draw_dsc.radius, start_angle, end_angle);
+        lv_event_send(obj, LV_EVENT_DRAW_PART_END, &part_draw_dsc);
+    }
+}
+
+static void draw_ticks_and_labels(lv_obj_t * obj, lv_draw_ctx_t * draw_ctx, const lv_area_t * scale_area)
+{
+    lv_analogclock_t * analogclock    = (lv_analogclock_t *)obj;
+
+    lv_point_t p_center;
+    lv_coord_t r_edge = LV_MIN(lv_area_get_width(scale_area) / 2, lv_area_get_height(scale_area) / 2);
+    p_center.x = scale_area->x1 + r_edge;
+    p_center.y = scale_area->y1 + r_edge;
+
+    uint8_t i;
+
+    lv_draw_line_dsc_t line_dsc;
+    lv_draw_line_dsc_init(&line_dsc);
+    lv_obj_init_draw_line_dsc(obj, LV_PART_TICKS, &line_dsc);
+    line_dsc.raw_end = 1;
+
+    lv_draw_label_dsc_t label_dsc;
+    lv_draw_label_dsc_init(&label_dsc);
+    lv_obj_init_draw_label_dsc(obj, LV_PART_TICKS, &label_dsc);
+
+    lv_analogclock_scale_t * scale;
+
+    lv_draw_mask_radius_param_t inner_minor_mask;
+    lv_draw_mask_radius_param_t inner_major_mask;
+    lv_draw_mask_radius_param_t outer_mask;
+
+    lv_obj_draw_part_dsc_t part_draw_dsc;
+    lv_obj_draw_dsc_init(&part_draw_dsc, draw_ctx);
+    part_draw_dsc.class_p = MY_CLASS;
+    part_draw_dsc.part = LV_PART_TICKS;
+    part_draw_dsc.type = LV_analogclock_DRAW_PART_TICK;
+    part_draw_dsc.line_dsc = &line_dsc;
+
+    _LV_LL_READ_BACK(&analogclock->scale_ll, scale) {
+        part_draw_dsc.sub_part_ptr = scale;
+
+        lv_coord_t r_out = r_edge + scale->r_mod;
+        lv_coord_t r_in_minor = r_out - scale->tick_length;
+        lv_coord_t r_in_major = r_out - scale->tick_major_length;
+
+        lv_area_t area_inner_minor;
+        area_inner_minor.x1 = p_center.x - r_in_minor;
+        area_inner_minor.y1 = p_center.y - r_in_minor;
+        area_inner_minor.x2 = p_center.x + r_in_minor;
+        area_inner_minor.y2 = p_center.y + r_in_minor;
+        lv_draw_mask_radius_init(&inner_minor_mask, &area_inner_minor, LV_RADIUS_CIRCLE, true);
+
+        lv_area_t area_inner_major;
+        area_inner_major.x1 = p_center.x - r_in_major;
+        area_inner_major.y1 = p_center.y - r_in_major;
+        area_inner_major.x2 = p_center.x + r_in_major - 1;
+        area_inner_major.y2 = p_center.y + r_in_major - 1;
+        lv_draw_mask_radius_init(&inner_major_mask, &area_inner_major, LV_RADIUS_CIRCLE, true);
+
+        lv_area_t area_outer;
+        area_outer.x1 = p_center.x - r_out;
+        area_outer.y1 = p_center.y - r_out;
+        area_outer.x2 = p_center.x + r_out - 1;
+        area_outer.y2 = p_center.y + r_out - 1;
+        lv_draw_mask_radius_init(&outer_mask, &area_outer, LV_RADIUS_CIRCLE, false);
+        int16_t outer_mask_id = lv_draw_mask_add(&outer_mask, NULL);
+
+        int16_t inner_act_mask_id = LV_MASK_ID_INV; /*Will be added later*/
+
+        uint32_t minor_cnt = scale->tick_major_nth ? scale->tick_major_nth - 1 : 0xFFFF;
+        for(i = 0; i < scale->tick_cnt; i++) {
+            minor_cnt++;
+            bool major = false;
+            if(minor_cnt == scale->tick_major_nth) {
+                minor_cnt = 0;
+                major = true;
+            }
+
+            int32_t value_of_line = lv_map(i, 0, scale->tick_cnt - 1, scale->min, scale->max);
+            part_draw_dsc.value = value_of_line;
+
+            lv_color_t line_color = major ? scale->tick_major_color : scale->tick_color;
+            lv_color_t line_color_ori = line_color;
+
+            lv_coord_t line_width_ori = major ? scale->tick_major_width : scale->tick_width;
+            lv_coord_t line_width = line_width_ori;
+
+            lv_analogclock_indicator_t * indic;
+            _LV_LL_READ_BACK(&analogclock->indicator_ll, indic) {
+                if(indic->type != LV_analogclock_INDICATOR_TYPE_SCALE_LINES) continue;
+                if(value_of_line >= indic->start_value && value_of_line <= indic->end_value) {
+                    line_width += indic->type_data.scale_lines.width_mod;
+
+                    if(indic->type_data.scale_lines.color_start.full == indic->type_data.scale_lines.color_end.full) {
+                        line_color = indic->type_data.scale_lines.color_start;
+                    }
+                    else {
+                        lv_opa_t ratio;
+                        if(indic->type_data.scale_lines.local_grad) {
+                            ratio = lv_map(value_of_line, indic->start_value, indic->end_value, LV_OPA_TRANSP, LV_OPA_COVER);
+                        }
+                        else {
+                            ratio = lv_map(value_of_line, scale->min, scale->max, LV_OPA_TRANSP, LV_OPA_COVER);
+                        }
+                        line_color = lv_color_mix(indic->type_data.scale_lines.color_end, indic->type_data.scale_lines.color_start, ratio);
+                    }
+                }
+            }
+
+            /*`* 256` for extra precision*/
+            int32_t angle_upscale = ((i * scale->angle_range) << 8) / (scale->tick_cnt - 1);
+
+            int32_t angle_low = (angle_upscale >> 8);
+            int32_t angle_high = angle_low + 1;
+            int32_t angle_rem = angle_upscale & 0xFF;
+
+            /*Interpolate sine and cos*/
+            int32_t sin_low = lv_trigo_sin(angle_low + scale->rotation);
+            int32_t sin_high = lv_trigo_sin(angle_high + scale->rotation);
+            int32_t sin_mid = (sin_low * (256 - angle_rem) + sin_high * angle_rem) >> 8;
+
+            int32_t cos_low = lv_trigo_cos(angle_low + scale->rotation);
+            int32_t cos_high = lv_trigo_cos(angle_high + scale->rotation);
+            int32_t cos_mid = (cos_low * (256 - angle_rem) + cos_high * angle_rem) >> 8;
+
+            line_dsc.color = line_color;
+            line_dsc.width = line_width;
+            /*Use the interpolated angle to get the outer x and y coordinates.
+             *Draw a little bit longer lines to be sure the mask will clip them correctly*/
+            lv_point_t p_outer;
+            p_outer.x = (int32_t)(((int32_t)cos_mid * (r_out + line_width) + 127) >> (LV_TRIGO_SHIFT)) + p_center.x;
+            p_outer.y = (int32_t)(((int32_t)sin_mid * (r_out + line_width) + 127) >> (LV_TRIGO_SHIFT)) + p_center.y;
+
+            part_draw_dsc.p1 = &p_outer;
+            part_draw_dsc.p1 = &p_center;
+            part_draw_dsc.id = i;
+            part_draw_dsc.label_dsc = &label_dsc;
+
+            /*Draw the text*/
+            if(major && !scale->hide_label) {
+                lv_draw_mask_remove_id(outer_mask_id);
+                uint32_t r_text = r_in_major - scale->label_gap;
+                lv_point_t p;
+                p.x = (int32_t)((int32_t)((int32_t)cos_mid * r_text + 127) >> LV_TRIGO_SHIFT) + p_center.x;
+                p.y = (int32_t)((int32_t)((int32_t)sin_mid * r_text + 127) >> LV_TRIGO_SHIFT) + p_center.y;
+
+                lv_draw_label_dsc_t label_dsc_tmp;
+                lv_memcpy(&label_dsc_tmp, &label_dsc, sizeof(label_dsc_tmp));
+
+                part_draw_dsc.label_dsc = &label_dsc_tmp;
+                char buf[16];
+
+                lv_snprintf(buf, sizeof(buf), "%" LV_PRId32, value_of_line / 5);
+                part_draw_dsc.text = buf;
+
+                lv_event_send(obj, LV_EVENT_DRAW_PART_BEGIN, &part_draw_dsc);
+
+                lv_point_t label_size;
+                lv_txt_get_size(&label_size, part_draw_dsc.text, label_dsc.font, label_dsc.letter_space, label_dsc.line_space,
+                                LV_COORD_MAX, LV_TEXT_FLAG_NONE);
+
+                lv_area_t label_cord;
+                label_cord.x1 = p.x - label_size.x / 2;
+                label_cord.y1 = p.y - label_size.y / 2;
+                label_cord.x2 = label_cord.x1 + label_size.x;
+                label_cord.y2 = label_cord.y1 + label_size.y;
+                if(value_of_line != 0) {
+                    lv_draw_label(draw_ctx, part_draw_dsc.label_dsc, &label_cord, part_draw_dsc.text, NULL);
+                }
+                outer_mask_id = lv_draw_mask_add(&outer_mask, NULL);
+            }
+            else {
+                part_draw_dsc.label_dsc = NULL;
+                part_draw_dsc.text = NULL;
+                lv_event_send(obj, LV_EVENT_DRAW_PART_BEGIN, &part_draw_dsc);
+            }
+
+            inner_act_mask_id = lv_draw_mask_add(major ? &inner_major_mask : &inner_minor_mask, NULL);
+            lv_draw_line(draw_ctx, &line_dsc, &p_outer, &p_center);
+            lv_draw_mask_remove_id(inner_act_mask_id);
+            lv_event_send(obj, LV_EVENT_DRAW_MAIN_END, &part_draw_dsc);
+
+            line_dsc.color = line_color_ori;
+            line_dsc.width = line_width_ori;
+
+        }
+        lv_draw_mask_free_param(&inner_minor_mask);
+        lv_draw_mask_free_param(&inner_major_mask);
+        lv_draw_mask_free_param(&outer_mask);
+        lv_draw_mask_remove_id(outer_mask_id);
+    }
+}
+
+
+static void draw_needles(lv_obj_t * obj, lv_draw_ctx_t * draw_ctx, const lv_area_t * scale_area)
+{
+    lv_analogclock_t * analogclock = (lv_analogclock_t *)obj;
+
+    lv_coord_t r_edge = lv_area_get_width(scale_area) / 2;
+    lv_point_t scale_center;
+    scale_center.x = scale_area->x1 + r_edge;
+    scale_center.y = scale_area->y1 + r_edge;
+
+    lv_draw_line_dsc_t line_dsc;
+    lv_draw_line_dsc_init(&line_dsc);
+    lv_obj_init_draw_line_dsc(obj, LV_PART_ITEMS, &line_dsc);
+
+    lv_draw_img_dsc_t img_dsc;
+    lv_draw_img_dsc_init(&img_dsc);
+    lv_obj_init_draw_img_dsc(obj, LV_PART_ITEMS, &img_dsc);
+    lv_opa_t opa_main = lv_obj_get_style_opa(obj, LV_PART_MAIN);
+
+    lv_obj_draw_part_dsc_t part_draw_dsc;
+    lv_obj_draw_dsc_init(&part_draw_dsc, draw_ctx);
+    part_draw_dsc.class_p = MY_CLASS;
+    part_draw_dsc.p1 = &scale_center;
+
+    lv_analogclock_indicator_t * indic;
+    _LV_LL_READ_BACK(&analogclock->indicator_ll, indic) {
+        lv_analogclock_scale_t * scale = indic->scale;
+        part_draw_dsc.sub_part_ptr = indic;
+
+        if(indic->type == LV_analogclock_INDICATOR_TYPE_NEEDLE_LINE) {
+            int32_t angle = lv_map(indic->end_value, scale->min, scale->max, scale->rotation, scale->rotation + scale->angle_range);
+            lv_coord_t r_out = r_edge + scale->r_mod + indic->type_data.needle_line.r_mod;
+            lv_point_t p_end;
+            p_end.y = (lv_trigo_sin(angle) * (r_out)) / LV_TRIGO_SIN_MAX + scale_center.y;
+            p_end.x = (lv_trigo_cos(angle) * (r_out)) / LV_TRIGO_SIN_MAX + scale_center.x;
+            line_dsc.color = indic->type_data.needle_line.color;
+            line_dsc.width = indic->type_data.needle_line.width;
+            line_dsc.opa = indic->opa > LV_OPA_MAX ? opa_main : (opa_main * indic->opa) >> 8;
+
+            part_draw_dsc.id = LV_analogclock_DRAW_PART_NEEDLE_LINE;
+            part_draw_dsc.line_dsc = &line_dsc;
+            part_draw_dsc.p2 = &p_end;
+
+            lv_event_send(obj, LV_EVENT_DRAW_PART_BEGIN, &part_draw_dsc);
+            lv_draw_line(draw_ctx, &line_dsc, &scale_center, &p_end);
+            lv_event_send(obj, LV_EVENT_DRAW_PART_END, &part_draw_dsc);
+        }
+        else if(indic->type == LV_analogclock_INDICATOR_TYPE_NEEDLE_IMG) {
+            if(indic->type_data.needle_img.src == NULL) continue;
+
+            int32_t angle = lv_map(indic->end_value, scale->min, scale->max, scale->rotation, scale->rotation + scale->angle_range);
+            lv_img_header_t info;
+            lv_img_decoder_get_info(indic->type_data.needle_img.src, &info);
+            lv_area_t a;
+            a.x1 = scale_center.x - indic->type_data.needle_img.pivot.x;
+            a.y1 = scale_center.y - indic->type_data.needle_img.pivot.y;
+            a.x2 = a.x1 + info.w - 1;
+            a.y2 = a.y1 + info.h - 1;
+
+            img_dsc.opa = indic->opa > LV_OPA_MAX ? opa_main : (opa_main * indic->opa) >> 8;
+            img_dsc.pivot.x = indic->type_data.needle_img.pivot.x;
+            img_dsc.pivot.y = indic->type_data.needle_img.pivot.y;
+            angle = angle * 10;
+            if(angle > 3600) angle -= 3600;
+            img_dsc.angle = angle;
+
+            part_draw_dsc.img_dsc = &img_dsc;
+
+            lv_event_send(obj, LV_EVENT_DRAW_PART_BEGIN, &part_draw_dsc);
+            lv_draw_img(draw_ctx, &img_dsc, &a, indic->type_data.needle_img.src);
+            lv_event_send(obj, LV_EVENT_DRAW_PART_END, &part_draw_dsc);
+        }
+    }
+}
+
+static void inv_arc(lv_obj_t * obj, lv_analogclock_indicator_t * indic, int32_t old_value, int32_t new_value)
+{
+    bool rounded = lv_obj_get_style_arc_rounded(obj, LV_PART_ITEMS);
+
+    lv_area_t scale_area;
+    lv_obj_get_content_coords(obj, &scale_area);
+
+    lv_coord_t r_out = lv_area_get_width(&scale_area) / 2;
+    lv_point_t scale_center;
+    scale_center.x = scale_area.x1 + r_out;
+    scale_center.y = scale_area.y1 + r_out;
+
+    r_out += indic->type_data.arc.r_mod;
+
+    lv_analogclock_scale_t * scale = indic->scale;
+
+    int32_t start_angle = lv_map(old_value, scale->min, scale->max, scale->rotation, scale->angle_range + scale->rotation);
+    int32_t end_angle = lv_map(new_value, scale->min, scale->max, scale->rotation, scale->angle_range + scale->rotation);
+
+    lv_area_t a;
+    lv_draw_arc_get_area(scale_center.x, scale_center.y, r_out, LV_MIN(start_angle, end_angle), LV_MAX(start_angle,
+                                                                                                       end_angle), indic->type_data.arc.width, rounded, &a);
+    lv_obj_invalidate_area(obj, &a);
+}
+
+
+static void inv_line(lv_obj_t * obj, lv_analogclock_indicator_t * indic, int32_t value)
+{
+    lv_area_t scale_area;
+    lv_obj_get_content_coords(obj, &scale_area);
+
+    lv_coord_t r_out = lv_area_get_width(&scale_area) / 2;
+    lv_point_t scale_center;
+    scale_center.x = scale_area.x1 + r_out;
+    scale_center.y = scale_area.y1 + r_out;
+
+    lv_analogclock_scale_t * scale = indic->scale;
+
+    if(indic->type == LV_analogclock_INDICATOR_TYPE_NEEDLE_LINE) {
+        int32_t angle = lv_map(value, scale->min, scale->max, scale->rotation, scale->rotation + scale->angle_range);
+        r_out += scale->r_mod + indic->type_data.needle_line.r_mod;
+        lv_point_t p_end;
+        p_end.y = (lv_trigo_sin(angle) * (r_out)) / LV_TRIGO_SIN_MAX + scale_center.y;
+        p_end.x = (lv_trigo_cos(angle) * (r_out)) / LV_TRIGO_SIN_MAX + scale_center.x;
+
+        lv_area_t a;
+        a.x1 = LV_MIN(scale_center.x, p_end.x) - indic->type_data.needle_line.width - 2;
+        a.y1 = LV_MIN(scale_center.y, p_end.y) - indic->type_data.needle_line.width - 2;
+        a.x2 = LV_MAX(scale_center.x, p_end.x) + indic->type_data.needle_line.width + 2;
+        a.y2 = LV_MAX(scale_center.y, p_end.y) + indic->type_data.needle_line.width + 2;
+
+        lv_obj_invalidate_area(obj, &a);
+    }
+    else if(indic->type == LV_analogclock_INDICATOR_TYPE_NEEDLE_IMG) {
+        int32_t angle = lv_map(value, scale->min, scale->max, scale->rotation, scale->rotation + scale->angle_range);
+        lv_img_header_t info;
+        lv_img_decoder_get_info(indic->type_data.needle_img.src, &info);
+
+        angle = angle * 10;
+        if(angle > 3600) angle -= 3600;
+
+        scale_center.x -= indic->type_data.needle_img.pivot.x;
+        scale_center.y -= indic->type_data.needle_img.pivot.y;
+        lv_area_t a;
+        _lv_img_buf_get_transformed_area(&a, info.w, info.h, angle, LV_IMG_ZOOM_NONE, &indic->type_data.needle_img.pivot);
+        a.x1 += scale_center.x - 2;
+        a.y1 += scale_center.y - 2;
+        a.x2 += scale_center.x + 2;
+        a.y2 += scale_center.y + 2;
+
+        lv_obj_invalidate_area(obj, &a);
+    }
+}
+#endif
diff --git a/src/extra/widgets/analogclock/lv_analogclock.h b/src/extra/widgets/analogclock/lv_analogclock.h
new file mode 100644
index 000000000..ef5f7212e
--- /dev/null
+++ b/src/extra/widgets/analogclock/lv_analogclock.h
@@ -0,0 +1,317 @@
+/**
+ * @file lv_analogclock.h
+ *
+ */
+
+#ifndef LV_ANALOGCLOCK_H
+#define LV_ANALOGCLOCK_H
+
+#ifdef __cplusplus
+extern "C" {
+#endif
+
+/*********************
+ *      INCLUDES
+ *********************/
+#include "../../../lvgl.h"
+
+#if LV_USE_ANALOGCLOCK != 0
+
+/*Testing of dependencies*/
+#if LV_DRAW_COMPLEX == 0
+#error "lv_analogclock: Complex drawing is required. Enable it in lv_conf.h (LV_DRAW_COMPLEX 1)"
+#endif
+
+/*********************
+ *      DEFINES
+ *********************/
+
+/**********************
+ *      TYPEDEFS
+ **********************/
+
+typedef struct {
+    lv_color_t tick_color;
+    uint16_t tick_cnt;
+    uint16_t tick_length;
+    uint16_t tick_width;
+
+    lv_color_t tick_major_color;
+    uint16_t tick_major_nth;
+    uint16_t tick_major_length;
+    uint16_t tick_major_width;
+
+    bool hide_label;
+    int16_t label_gap;
+    int16_t label_color;
+
+    int32_t min;
+    int32_t max;
+    int16_t r_mod;
+    uint16_t angle_range;
+    int16_t rotation;
+} lv_analogclock_scale_t;
+
+enum {
+    LV_analogclock_INDICATOR_TYPE_NEEDLE_IMG,
+    LV_analogclock_INDICATOR_TYPE_NEEDLE_LINE,
+    LV_analogclock_INDICATOR_TYPE_SCALE_LINES,
+    LV_analogclock_INDICATOR_TYPE_ARC,
+};
+typedef uint8_t lv_analogclock_indicator_type_t;
+
+typedef struct {
+    lv_analogclock_scale_t * scale;
+    lv_analogclock_indicator_type_t type;
+    lv_opa_t opa;
+    int32_t start_value;
+    int32_t end_value;
+    union {
+        struct {
+            const void * src;
+            lv_point_t pivot;
+        } needle_img;
+        struct {
+            uint16_t width;
+            int16_t r_mod;
+            lv_color_t color;
+        } needle_line;
+        struct {
+            uint16_t width;
+            const void * src;
+            lv_color_t color;
+            int16_t r_mod;
+        } arc;
+        struct {
+            int16_t width_mod;
+            lv_color_t color_start;
+            lv_color_t color_end;
+            uint8_t local_grad  : 1;
+        } scale_lines;
+    } type_data;
+} lv_analogclock_indicator_t;
+
+/*Data of line analogclock*/
+typedef struct {
+    lv_obj_t obj;
+    lv_ll_t scale_ll;
+    lv_analogclock_scale_t * scale;
+    lv_ll_t indicator_ll;
+    bool hide_point;
+    lv_analogclock_indicator_t * hour_indic;
+    lv_analogclock_indicator_t * min_indic;
+    lv_analogclock_indicator_t * sec_indic;
+} lv_analogclock_t;
+
+extern const lv_obj_class_t lv_analogclock_class;
+
+/**
+ * `type` field in `lv_obj_draw_part_dsc_t` if `class_p = lv_analogclock_class`
+ * Used in `LV_EVENT_DRAW_PART_BEGIN` and `LV_EVENT_DRAW_PART_END`
+ */
+typedef enum {
+    LV_analogclock_DRAW_PART_ARC,             /**< The arc indicator*/
+    LV_analogclock_DRAW_PART_NEEDLE_LINE,     /**< The needle lines*/
+    LV_analogclock_DRAW_PART_NEEDLE_IMG,      /**< The needle images*/
+    LV_analogclock_DRAW_PART_TICK,            /**< The tick lines and labels*/
+} lv_analogclock_draw_part_type_t;
+
+/**********************
+ * GLOBAL PROTOTYPES
+ **********************/
+
+/**
+ * Create a analogclock object
+ * @param parent pointer to an object, it will be the parent of the new bar.
+ * @return pointer to the created analogclock
+ */
+lv_obj_t * lv_analogclock_create(lv_obj_t * parent);
+
+/*=====================
+ * Add scale
+ *====================*/
+
+/**
+ * Add a new scale to the analogclock.
+ * @param obj   pointer to a analogclock object
+ * @return      the new scale
+ * @note        Indicators can be attached to scales.
+ */
+lv_analogclock_scale_t * lv_analogclock_add_scale(lv_obj_t * obj);
+
+/*=====================
+ * Add scale
+ *====================*/
+/**
+ * Set the properties of the ticks of a scale
+ * @param obj       pointer to a analogclock object
+ * @param scale     pointer to scale (added to `analogclock`)
+ * @param cnt       number of tick lines
+ * @param width     width of tick lines
+ * @param len       length of tick lines
+ * @param color     color of tick lines
+ */
+void lv_analogclock_set_ticks(lv_obj_t * obj, uint16_t width, uint16_t len, lv_color_t color);
+
+/**
+ * Make some "normal" ticks major ticks and set their attributes.
+ * Texts with the current value are also added to the major ticks.
+ * @param obj           pointer to a analogclock object
+ * @param scale         pointer to scale (added to `analogclock`)
+ * @param nth           make every Nth normal tick major tick. (start from the first on the left)
+ * @param width         width of the major ticks
+ * @param len           length of the major ticks
+ * @param color         color of the major ticks
+ * @param label_gap     gap between the major ticks and the labels
+ */
+void lv_analogclock_set_major_ticks(lv_obj_t * obj, uint16_t width, uint16_t len, lv_color_t color, int16_t label_gap);
+
+
+/**
+ * Set the value and angular range of a scale.
+ * @param obj           pointer to a analogclock object
+ * @param scale         pointer to scale (added to `analogclock`)
+ * @param min           the minimum value
+ * @param max           the maximal value
+ * @param angle_range   the angular range of the scale
+ * @param rotation      the angular offset from the 3 o'clock position (clock-wise)
+ */
+void lv_analogclock_set_scale_range(lv_obj_t * obj, lv_analogclock_scale_t * scale, int32_t min, int32_t max,
+                                    uint32_t angle_range,
+                                    uint32_t rotation);
+
+/*=====================
+ * Hide digits / centerpoint
+ *====================*/
+
+/**
+ * Hide the digits or not
+ * @param obj           pointer to a analogclock object
+ * @param hide_digits   set whether has digits
+ */
+void lv_analogclock_hide_digits(lv_obj_t * obj, bool hide_digits);
+
+/**
+ * Hide the center point or not
+ * @param obj           pointer to a analogclock object
+ * @param hide_point    set whether has center point
+ */
+void lv_analogclock_hide_point(lv_obj_t * obj, bool hide_point);
+
+/*=====================
+ * Add indicator
+ *====================*/
+
+/**
+ * Add a needle line indicator the scale
+ * @param obj           pointer to a analogclock object
+ * @param scale         pointer to scale (added to `analogclock`)
+ * @param width         width of the line
+ * @param color         color of the line
+ * @param r_mod         the radius modifier (added to the scale's radius) to get the lines length
+ * @return              the new indicator
+ */
+lv_analogclock_indicator_t * lv_analogclock_add_needle_line(lv_obj_t * obj, lv_analogclock_scale_t * scale,
+                                                            uint16_t width,
+                                                            lv_color_t color, int16_t r_mod);
+void lv_analogclock_set_hour_needle_line(lv_obj_t * obj, uint16_t width,
+                                         lv_color_t color, int16_t r_mod);
+void lv_analogclock_set_min_needle_line(lv_obj_t * obj, uint16_t width,
+                                        lv_color_t color, int16_t r_mod);
+void lv_analogclock_set_sec_needle_line(lv_obj_t * obj, uint16_t width,
+                                        lv_color_t color, int16_t r_mod);
+
+/**
+ * Add a needle image indicator the scale
+ * @param obj           pointer to a analogclock object
+ * @param scale         pointer to scale (added to `analogclock`)
+ * @param src           the image source of the indicator. path or pointer to ::lv_img_dsc_t
+ * @param pivot_x       the X pivot point of the needle
+ * @param pivot_y       the Y pivot point of the needle
+ * @return              the new indicator
+ * @note                the needle image should point to the right, like -O----->
+ */
+lv_analogclock_indicator_t * lv_analogclock_add_needle_img(lv_obj_t * obj, lv_analogclock_scale_t * scale,
+                                                           const void * src,
+                                                           lv_coord_t pivot_x, lv_coord_t pivot_y);
+void lv_analogclock_set_hour_needle_img(lv_obj_t * obj, const void * src,
+                                        lv_coord_t pivot_x, lv_coord_t pivot_y);
+void lv_analogclock_set_min_needle_img(lv_obj_t * obj, const void * src,
+                                       lv_coord_t pivot_x, lv_coord_t pivot_y);
+void lv_analogclock_set_sec_needle_img(lv_obj_t * obj, const void * src,
+                                       lv_coord_t pivot_x, lv_coord_t pivot_y);
+
+/**
+ * Add an arc indicator the scale
+ * @param obj           pointer to a analogclock object
+ * @param scale         pointer to scale (added to `analogclock`)
+ * @param width         width of the arc
+ * @param color         color of the arc
+ * @param r_mod         the radius modifier (added to the scale's radius) to get the outer radius of the arc
+ * @return              the new indicator
+ */
+lv_analogclock_indicator_t * lv_analogclock_add_arc(lv_obj_t * obj, uint16_t width, lv_color_t color,
+                                                    int16_t r_mod);
+
+
+/**
+ * Add a scale line indicator the scale. It will modify the ticks.
+ * @param obj           pointer to a analogclock object
+ * @param scale         pointer to scale (added to `analogclock`)
+ * @param color_start   the start color
+ * @param color_end     the end color
+ * @param local         tell how to map start and end color. true: the indicator's start and end_value; false: the scale's min max value
+ * @param width_mod     add this the affected tick's width
+ * @return              the new indicator
+ */
+lv_analogclock_indicator_t * lv_analogclock_add_scale_lines(lv_obj_t * obj, lv_color_t color_start,
+                                                            lv_color_t color_end, bool local, int16_t width_mod);
+
+/*=====================
+ * Set indicator value
+ *====================*/
+
+/**
+ * Set the value of the indicator. It will set start and and value to the same value
+ * @param obj           pointer to a analogclock object
+ * @param indic         pointer to an indicator
+ * @param value         the new value
+ */
+void lv_analogclock_set_indicator_value(lv_obj_t * obj, lv_analogclock_indicator_t * indic, int32_t value);
+
+/**
+ * Set the start value of the indicator.
+ * @param obj           pointer to a analogclock object
+ * @param indic         pointer to an indicator
+ * @param value         the new value
+ */
+void lv_analogclock_set_indicator_start_value(lv_obj_t * obj, lv_analogclock_indicator_t * indic, int32_t value);
+
+/**
+ * Set the start value of the indicator.
+ * @param obj           pointer to a analogclock object
+ * @param indic         pointer to an indicator
+ * @param value         the new value
+ */
+void lv_analogclock_set_indicator_end_value(lv_obj_t * obj, lv_analogclock_indicator_t * indic, int32_t value);
+
+/**
+ * Set the time of clock.
+ * @param obj           pointer to a analogclock object
+ * @param hour          hour value
+ * @param min           minute value
+ * @param sec           second value
+ */
+void lv_analogclock_set_time(lv_obj_t * obj, int32_t hour, int32_t min, int32_t sec);
+
+/**********************
+ *      MACROS
+ **********************/
+
+#endif /*LV_USE_ANALOGCLOCK*/
+
+#ifdef __cplusplus
+} /*extern "C"*/
+#endif
+
+#endif /*LV_ANALOGCLOCK_H*/
diff --git a/src/extra/widgets/animimg/lv_animimg.c b/src/extra/widgets/animimg/lv_animimg.c
index 072d02e05..f409ecb89 100644
--- a/src/extra/widgets/animimg/lv_animimg.c
+++ b/src/extra/widgets/animimg/lv_animimg.c
@@ -64,13 +64,18 @@ lv_obj_t * lv_animimg_create(lv_obj_t * parent)
     return obj;
 }
 
-void lv_animimg_set_src(lv_obj_t * obj, const void * dsc[], uint8_t num)
+void lv_animimg_set_src(lv_obj_t * obj, const void * dsc[], uint8_t num, bool reverse)
 {
     LV_ASSERT_OBJ(obj, MY_CLASS);
     lv_animimg_t * animimg = (lv_animimg_t *)obj;
     animimg->dsc = dsc;
     animimg->pic_count = num;
-    lv_anim_set_values(&animimg->anim, 0, num);
+    if(reverse) {
+        lv_anim_set_values(&animimg->anim, num, 0);
+    }
+    else {
+        lv_anim_set_values(&animimg->anim, 0, num);
+    }
 }
 
 void lv_animimg_start(lv_obj_t * obj)
@@ -80,6 +85,14 @@ void lv_animimg_start(lv_obj_t * obj)
     lv_anim_start(&animimg->anim);
 }
 
+bool lv_animimg_del(lv_obj_t * obj)
+{
+    LV_ASSERT_OBJ(obj, MY_CLASS);
+    lv_animimg_t * animimg = (lv_animimg_t *)obj;
+    return lv_anim_del(animimg, NULL);
+}
+
+
 /*=====================
  * Setter functions
  *====================*/
@@ -89,7 +102,6 @@ void lv_animimg_set_duration(lv_obj_t * obj, uint32_t duration)
     LV_ASSERT_OBJ(obj, MY_CLASS);
     lv_animimg_t * animimg = (lv_animimg_t *)obj;
     lv_anim_set_time(&animimg->anim, duration);
-    lv_anim_set_playback_delay(&animimg->anim, duration);
 }
 
 void lv_animimg_set_repeat_count(lv_obj_t * obj, uint16_t count)
@@ -99,6 +111,34 @@ void lv_animimg_set_repeat_count(lv_obj_t * obj, uint16_t count)
     lv_anim_set_repeat_count(&animimg->anim, count);
 }
 
+void lv_animimg_set_playback_time(lv_obj_t * obj, uint16_t duration)
+{
+    LV_ASSERT_OBJ(obj, MY_CLASS);
+    lv_animimg_t * animimg = (lv_animimg_t *)obj;
+    lv_anim_set_playback_time(&animimg->anim, duration);
+}
+
+void lv_animimg_set_playback_delay(lv_obj_t * obj, uint16_t duration)
+{
+    LV_ASSERT_OBJ(obj, MY_CLASS);
+    lv_animimg_t * animimg = (lv_animimg_t *)obj;
+    lv_anim_set_playback_delay(&animimg->anim, duration);
+}
+
+void lv_animimg_set_start_cb(lv_obj_t * obj, lv_anim_start_cb_t start_cb)
+{
+    LV_ASSERT_OBJ(obj, MY_CLASS);
+    lv_animimg_t * animimg = (lv_animimg_t *)obj;
+    lv_anim_set_start_cb(&animimg->anim, start_cb);
+}
+
+void lv_animimg_set_ready_cb(lv_obj_t * obj, lv_anim_start_cb_t ready_cb)
+{
+    LV_ASSERT_OBJ(obj, MY_CLASS);
+    lv_animimg_t * animimg = (lv_animimg_t *)obj;
+    lv_anim_set_ready_cb(&animimg->anim, ready_cb);
+}
+
 /*=====================
  * Getter functions
  *====================*/
@@ -127,12 +167,9 @@ static void lv_animimg_constructor(const lv_obj_class_t * class_p, lv_obj_t * ob
 
 static void index_change(lv_obj_t * obj, int32_t index)
 {
-    lv_coord_t idx;
     lv_animimg_t * animimg = (lv_animimg_t *)obj;
-
-    idx = index % animimg->pic_count;
-
-    lv_img_set_src(obj, animimg->dsc[idx]);
+    index = animimg->pic_count == index ? index - 1 : index;
+    lv_img_set_src(obj, animimg->dsc[index]);
 }
 
 #endif
diff --git a/src/extra/widgets/animimg/lv_animimg.h b/src/extra/widgets/animimg/lv_animimg.h
index 0ba01f239..7e3915632 100644
--- a/src/extra/widgets/animimg/lv_animimg.h
+++ b/src/extra/widgets/animimg/lv_animimg.h
@@ -68,8 +68,9 @@ lv_obj_t * lv_animimg_create(lv_obj_t * parent);
  * @param img pointer to an animation image object
  * @param dsc pointer to a series images
  * @param num images' number
+ * @param reverse play in reverse.
  */
-void lv_animimg_set_src(lv_obj_t * img, const void * dsc[], uint8_t num);
+void lv_animimg_set_src(lv_obj_t * img, const void * dsc[], uint8_t num, bool reverse);
 
 /**
  * Startup the image animation.
@@ -77,6 +78,12 @@ void lv_animimg_set_src(lv_obj_t * img, const void * dsc[], uint8_t num);
  */
 void lv_animimg_start(lv_obj_t * obj);
 
+/**
+ * Delete the image animation.
+ * @param obj pointer to an animation image object
+ */
+bool lv_animimg_del(lv_obj_t * obj);
+
 /**
  * Set the image animation duration time. unit:ms
  * @param img pointer to an animation image object
@@ -90,6 +97,34 @@ void lv_animimg_set_duration(lv_obj_t * img, uint32_t duration);
  */
 void lv_animimg_set_repeat_count(lv_obj_t * img, uint16_t count);
 
+/**
+ * Make the image animation to play back to when the forward direction is ready.
+ * @param img pointer to an animation image object
+ * @param duration the duration of the playback image animation in milliseconds. 0: disable playback
+ */
+void lv_animimg_set_playback_time(lv_obj_t * img, uint16_t duration);
+
+/**
+ * Make the image animation to play back to when the forward direction is ready.
+ * @param img pointer to an animation image object
+ * @param duration delay in milliseconds before starting the playback image animation.
+ */
+void lv_animimg_set_playback_delay(lv_obj_t * img, uint16_t duration);
+
+/**
+ * Set a function call when the animation image really starts (considering `delay`)
+ * @param img pointer to an animation image object
+ * @param start_cb  a function call when the animation is start
+ */
+void lv_animimg_set_start_cb(lv_obj_t * img, lv_anim_ready_cb_t start_cb);
+
+/**
+ * Set a function call when the animation is ready
+ * @param img pointer to an animation image object
+ * @param ready_cb  a function call when the animation is ready
+ */
+void lv_animimg_set_ready_cb(lv_obj_t * img, lv_anim_ready_cb_t ready_cb);
+
 /*=====================
  * Getter functions
  *====================*/
diff --git a/src/extra/widgets/carousel/lv_carousel.c b/src/extra/widgets/carousel/lv_carousel.c
new file mode 100644
index 000000000..36f155b9b
--- /dev/null
+++ b/src/extra/widgets/carousel/lv_carousel.c
@@ -0,0 +1,208 @@
+/**
+ * @file lv_carousel.c
+ *
+ */
+
+/*********************
+ *      INCLUDES
+ *********************/
+#include "lv_carousel.h"
+#if LV_USE_CAROUSEL
+
+/*********************
+ *      DEFINES
+ *********************/
+
+/**********************
+ *      TYPEDEFS
+ **********************/
+
+/**********************
+ *  STATIC PROTOTYPES
+ **********************/
+static void lv_carousel_constructor(const lv_obj_class_t * class_p, lv_obj_t * obj);
+static void lv_carousel_element_constructor(const lv_obj_class_t * class_p, lv_obj_t * obj);
+static void carousel_event_cb(lv_event_t * e);
+
+/**********************
+ *  STATIC VARIABLES
+ **********************/
+
+const lv_obj_class_t lv_carousel_class = {.constructor_cb = lv_carousel_constructor,
+                                          .base_class = &lv_obj_class,
+                                          .instance_size = sizeof(lv_carousel_t)
+                                         };
+
+const lv_obj_class_t lv_carousel_element_class = {.constructor_cb = lv_carousel_element_constructor,
+                                                  .base_class = &lv_obj_class,
+                                                  .instance_size = sizeof(lv_carousel_element_t)
+                                                 };
+
+static uint32_t create_col_id;
+
+/**********************
+ *      MACROS
+ **********************/
+
+/**********************
+ *   GLOBAL FUNCTIONS
+ **********************/
+
+lv_obj_t * lv_carousel_create(lv_obj_t * parent)
+{
+    LV_LOG_INFO("begin");
+    lv_obj_t * obj = lv_obj_class_create_obj(&lv_carousel_class, parent);
+    lv_carousel_t * carousel = (lv_carousel_t *) obj;
+    lv_obj_class_init_obj(obj);
+    carousel->sentinel = lv_obj_create(obj);
+    lv_obj_remove_style_all(carousel->sentinel);
+    return obj;
+}
+
+/*======================
+ * Add/remove functions
+ *=====================*/
+
+lv_obj_t * lv_carousel_add_element(lv_obj_t * obj, uint8_t id)
+{
+    LV_LOG_INFO("begin");
+    create_col_id = id;
+    lv_carousel_t * carousel = (lv_carousel_t *) obj;
+
+    lv_obj_t * element_obj = lv_obj_class_create_obj(&lv_carousel_element_class, obj);
+    lv_obj_class_init_obj(element_obj);
+    if(id == 0) {
+        carousel->element_act = element_obj;
+        lv_obj_add_state(carousel->element_act, LV_STATE_FOCUSED);
+    }
+
+    lv_obj_set_pos(carousel->sentinel, carousel->start_pos + ((carousel->element_width) * (create_col_id + 1)), 0);
+    lv_obj_set_size(carousel->sentinel, carousel->start_pos, LV_PCT(100));
+    lv_obj_update_layout(carousel->sentinel);
+    lv_obj_clear_flag(element_obj, LV_OBJ_FLAG_SCROLLABLE | LV_OBJ_FLAG_CLICKABLE);
+
+    // printf("sentinel: x : %d, width: %d\n", lv_obj_get_x(carousel->sentinel), lv_obj_get_width(carousel->sentinel));
+
+    return element_obj;
+}
+
+void lv_carousel_set_element_width(lv_obj_t * obj, lv_coord_t w)
+{
+    lv_carousel_t * carousel = (lv_carousel_t *) obj;
+    carousel->element_width = w;
+}
+
+void lv_obj_set_element(lv_obj_t * obj, lv_obj_t * element_obj, lv_anim_enable_t anim_en)
+{
+    lv_obj_update_layout(element_obj);
+    lv_coord_t tx = lv_obj_get_x(element_obj);
+    lv_coord_t ty = lv_obj_get_y(element_obj);
+
+    lv_carousel_element_t * element = (lv_carousel_element_t *)element_obj;
+    lv_carousel_t * carousel = (lv_carousel_t *) obj;
+    lv_obj_t * element_pre = carousel->element_act;
+    carousel->element_act = (lv_obj_t *)element;
+    tx -= carousel->start_pos;
+
+    lv_obj_clear_state(element_pre, LV_STATE_FOCUSED);
+    lv_obj_add_state(carousel->element_act, LV_STATE_FOCUSED);
+    //lv_obj_set_scroll_dir(obj, element->dir);
+    lv_obj_scroll_to(obj, tx, ty, anim_en);
+}
+
+void lv_obj_set_element_id(lv_obj_t * obj, uint32_t id, lv_anim_enable_t anim_en)
+{
+    lv_obj_update_layout(obj);
+
+    lv_carousel_t * carousel = (lv_carousel_t *) obj;
+    lv_coord_t tx = (carousel->element_width * id) + carousel->start_pos;
+    // printf("===============tx: %d\n", tx);
+
+    uint32_t i;
+    for(i = 0; i < lv_obj_get_child_cnt(obj); i++) {
+        lv_obj_t * element_obj = lv_obj_get_child(obj, i);
+        lv_coord_t x = lv_obj_get_x(element_obj);
+        // printf("===============i: %d, x: %d\n", i, x);
+        if(x == tx) {
+            lv_obj_set_element(obj, element_obj, anim_en);
+            return;
+        }
+    }
+
+    LV_LOG_WARN("No element found with at (%d) index", (int)id);
+}
+
+lv_obj_t * lv_carousel_get_element_act(lv_obj_t * obj)
+{
+    lv_carousel_t * carousel = (lv_carousel_t *) obj;
+    return carousel->element_act;
+}
+
+/**********************
+ *   STATIC FUNCTIONS
+ **********************/
+
+static void lv_carousel_constructor(const lv_obj_class_t * class_p, lv_obj_t * obj)
+{
+    LV_UNUSED(class_p);
+
+    lv_obj_set_size(obj, 300, 100);
+    lv_carousel_set_element_width(obj, 100);
+    lv_obj_add_event_cb(obj, carousel_event_cb, LV_EVENT_ALL, NULL);
+    lv_obj_add_flag(obj, LV_OBJ_FLAG_SCROLL_ONE);
+    lv_obj_set_scroll_snap_x(obj, LV_SCROLL_SNAP_CENTER);
+}
+
+static void lv_carousel_element_constructor(const lv_obj_class_t * class_p, lv_obj_t * obj)
+{
+
+    LV_UNUSED(class_p);
+    lv_obj_t * parent = lv_obj_get_parent(obj);
+    lv_carousel_t * carousel = (lv_carousel_t *) parent;
+    lv_coord_t carousel_width;
+    lv_obj_set_size(obj, carousel->element_width, LV_PCT(100));
+    lv_obj_update_layout(obj);  /*Be sure the size is correct*/
+
+    carousel_width = lv_obj_get_content_width(parent);
+    lv_coord_t startpos = (carousel_width - carousel->element_width) / 2;
+    carousel->start_pos = startpos;
+    lv_obj_set_pos(obj, startpos + ((carousel->element_width) * create_col_id), 0);
+}
+
+static void carousel_event_cb(lv_event_t * e)
+{
+    lv_event_code_t code = lv_event_get_code(e);
+    lv_obj_t * obj = lv_event_get_target(e);
+    lv_carousel_t * carousel = (lv_carousel_t *) obj;
+
+    if(code == LV_EVENT_SCROLL_END) {
+        lv_point_t scroll_end;
+        lv_obj_get_scroll_end(obj, &scroll_end);
+        lv_coord_t left = scroll_end.x;
+
+        lv_dir_t dir = LV_DIR_HOR;
+        uint32_t child_count = lv_obj_get_child_cnt(obj);
+        uint32_t i;
+        lv_obj_t * element_pre = carousel->element_act;
+        for(i = 0; i < child_count; i++) {
+            lv_obj_t * element_obj = lv_obj_get_child(obj, i);
+            lv_coord_t x = lv_obj_get_x(element_obj) - carousel->start_pos;
+            if(x == left) {
+                lv_carousel_element_t * element = (lv_carousel_element_t *)element_obj;
+                carousel->element_act = (lv_obj_t *)element;
+                lv_event_send(obj, LV_EVENT_VALUE_CHANGED, NULL);
+                if(left == (lv_coord_t)((child_count - 2) * carousel->element_width)) {
+                    dir = LV_DIR_LEFT;
+                }
+                else if(left == 0) {
+                    dir = LV_DIR_RIGHT;
+                }
+                break;
+            }
+        }
+        lv_obj_clear_state(element_pre, LV_STATE_FOCUSED);
+        lv_obj_add_state(carousel->element_act, LV_STATE_FOCUSED);
+        lv_obj_set_scroll_dir(obj, dir);
+    }
+}
+#endif /*LV_USE_CAROUSEL*/
diff --git a/src/extra/widgets/carousel/lv_carousel.h b/src/extra/widgets/carousel/lv_carousel.h
new file mode 100644
index 000000000..267d1427e
--- /dev/null
+++ b/src/extra/widgets/carousel/lv_carousel.h
@@ -0,0 +1,75 @@
+/**
+ * @file lv_carousel.h
+ *
+ */
+
+#ifndef LV_CAROUSEL_H
+#define LV_CAROUSEL_H
+
+#ifdef __cplusplus
+extern "C" {
+#endif
+
+/*********************
+ *      INCLUDES
+ *********************/
+#include "../../../core/lv_obj.h"
+
+#if LV_USE_CAROUSEL
+
+/*********************
+ *      DEFINES
+ *********************/
+
+/**********************
+ *      TYPEDEFS
+ **********************/
+typedef struct {
+    lv_obj_t obj;
+    lv_obj_t * element_act;
+    lv_obj_t * sentinel;
+    lv_coord_t element_width;
+    lv_coord_t start_pos;
+} lv_carousel_t;
+
+typedef struct {
+    lv_obj_t obj;
+    lv_dir_t dir;
+} lv_carousel_element_t;
+
+extern const lv_obj_class_t lv_carousel_class;
+extern const lv_obj_class_t lv_carousel_element_class;
+
+/**********************
+ * GLOBAL PROTOTYPES
+ **********************/
+
+/**
+ * Create a carousel object
+ * @param parent pointer to an object, it will be the parent of the new carousel
+ * @return pointer to the created carousel
+ */
+lv_obj_t * lv_carousel_create(lv_obj_t * parent);
+
+lv_obj_t * lv_carousel_add_element(lv_obj_t * obj, uint8_t id);
+void lv_carousel_set_element_width(lv_obj_t * obj, lv_coord_t w);
+void lv_obj_set_element(lv_obj_t * obj, lv_obj_t * element_obj, lv_anim_enable_t anim_en);
+void lv_obj_set_element_id(lv_obj_t * obj, uint32_t id, lv_anim_enable_t anim_en);
+
+lv_obj_t * lv_carousel_get_element_act(lv_obj_t * obj);
+
+/*=====================
+ * Other functions
+ *====================*/
+
+/**********************
+ *      MACROS
+ **********************/
+
+#endif /*LV_USE_carousel*/
+
+#ifdef __cplusplus
+} /*extern "C"*/
+#endif
+
+#endif /*LV_carousel_H*/
diff --git a/src/extra/widgets/dclock/lv_dclock.c b/src/extra/widgets/dclock/lv_dclock.c
new file mode 100644
index 000000000..00ca0f6dd
--- /dev/null
+++ b/src/extra/widgets/dclock/lv_dclock.c
@@ -0,0 +1,437 @@
+﻿/**
+ * @file lv_dclock.c
+ *
+ */
+
+/*********************
+ *      INCLUDES
+ *********************/
+#include "lv_dclock.h"
+#if LV_USE_DCLOCK != 0
+#include "../../../core/lv_obj.h"
+#include "../../../misc/lv_assert.h"
+#include "../../../core/lv_group.h"
+#include "../../../draw/lv_draw.h"
+#include "../../../misc/lv_color.h"
+#include "../../../misc/lv_math.h"
+#include "../../../misc/lv_bidi.h"
+#include "../../../misc/lv_txt_ap.h"
+#include "../../../misc/lv_printf.h"
+#include "../../../widgets/lv_label.h"
+
+
+/*********************
+ *      DEFINES
+ *********************/
+#define MY_CLASS &lv_dclock_class
+
+#define LV_DCLOCK_DEF_SCROLL_SPEED   (lv_disp_get_dpi(lv_obj_get_disp(obj)) / 3)
+#define LV_DCLOCK_SCROLL_DELAY       300
+#define LV_DCLOCK_DOT_END_INV 0xFFFFFFFF
+#define LV_DCLOCK_HINT_HEIGHT_LIMIT 1024 /*Enable "hint" to buffer info about labels larger than this. (Speed up drawing)*/
+
+/**********************
+ *      TYPEDEFS
+ **********************/
+
+/**********************
+ *  STATIC PROTOTYPES
+ **********************/
+static void lv_dclock_constructor(const lv_obj_class_t * class_p, lv_obj_t * obj);
+static void lv_dclock_destructor(const lv_obj_class_t * class_p, lv_obj_t * obj);
+static void lv_dclock_event(const lv_obj_class_t * class_p, lv_event_t * e);
+static void draw_main(lv_event_t * e);
+static void lv_dclock_refr_text(lv_obj_t * obj);
+
+/**********************
+ *  STATIC VARIABLES
+ **********************/
+const lv_obj_class_t lv_dclock_class = {
+    .constructor_cb = lv_dclock_constructor,
+    .destructor_cb = lv_dclock_destructor,
+    .event_cb = lv_dclock_event,
+    .width_def = LV_SIZE_CONTENT,
+    .height_def = LV_SIZE_CONTENT,
+    .instance_size = sizeof(lv_dclock_t),
+    .base_class = &lv_obj_class
+};
+
+/**********************
+ *   GLOBAL FUNCTIONS
+ **********************/
+
+lv_obj_t * lv_dclock_create(lv_obj_t * parent, char * input_time)
+{
+    LV_LOG_INFO("begin");
+    lv_obj_t * obj = lv_obj_class_create_obj(MY_CLASS, parent);
+    lv_obj_class_init_obj(obj);
+    if(lv_obj_is_valid(obj)) {
+        lv_dclock_set_text_fmt(obj, "%s", input_time);
+    }
+    return obj;
+}
+
+void lv_dclock_set_text(lv_obj_t * obj, const char * text)
+{
+    LV_ASSERT_OBJ(obj, MY_CLASS);
+    lv_dclock_t * dclock = (lv_dclock_t *)obj;
+
+    lv_obj_invalidate(obj);
+
+    /*If text is NULL then just refresh with the current text*/
+    if(text == NULL) text = dclock->text;
+
+    if(dclock->text == text && dclock->static_txt == 0) {
+        /*If set its own text then reallocate it (maybe its size changed)*/
+#if LV_USE_ARABIC_PERSIAN_CHARS
+        /*Get the size of the text and process it*/
+        size_t len = _lv_txt_ap_calc_bytes_cnt(text);
+
+        dclock->text = lv_mem_realloc(dclock->text, len);
+        LV_ASSERT_MALLOC(dclock->text);
+        if(dclock->text == NULL) return;
+
+        _lv_txt_ap_proc(dclock->text, dclock->text);
+#else
+        dclock->text = lv_mem_realloc(dclock->text, strlen(dclock->text) + 1);
+#endif
+
+        LV_ASSERT_MALLOC(dclock->text);
+        if(dclock->text == NULL) return;
+    }
+    else {
+        /*Free the old text*/
+        if(dclock->text != NULL && dclock->static_txt == 0) {
+            lv_mem_free(dclock->text);
+            dclock->text = NULL;
+        }
+
+#if LV_USE_ARABIC_PERSIAN_CHARS
+        /*Get the size of the text and process it*/
+        size_t len = _lv_txt_ap_calc_bytes_cnt(text);
+
+        dclock->text = lv_mem_alloc(len);
+        LV_ASSERT_MALLOC(dclock->text);
+        if(dclock->text == NULL) return;
+
+        _lv_txt_ap_proc(text, dclock->text);
+#else
+        /*Get the size of the text*/
+        size_t len = strlen(text) + 1;
+
+        /*Allocate space for the new text*/
+        dclock->text = lv_mem_alloc(len);
+        LV_ASSERT_MALLOC(dclock->text);
+        if(dclock->text == NULL) return;
+        strcpy(dclock->text, text);
+#endif
+
+        /*Now the text is dynamically allocated*/
+        dclock->static_txt = 0;
+    }
+
+    lv_dclock_refr_text(obj);
+}
+
+void lv_dclock_set_text_fmt(lv_obj_t * obj, const char * fmt, ...)
+{
+
+    LV_ASSERT_OBJ(obj, MY_CLASS);
+    LV_ASSERT_NULL(fmt);
+    lv_obj_invalidate(obj);
+    lv_dclock_t * dclock = (lv_dclock_t *)obj;
+
+    /*If text is NULL then refresh*/
+    if(fmt == NULL) {
+        lv_dclock_refr_text(obj);
+        return;
+    }
+
+    if(dclock->text != NULL && dclock->static_txt == 0) {
+        lv_mem_free(dclock->text);
+        dclock->text = NULL;
+    }
+
+    va_list args;
+    va_start(args, fmt);
+    dclock->text = _lv_txt_set_text_vfmt(fmt, args);
+    va_end(args);
+    dclock->static_txt = 0; /*Now the text is dynamically allocated*/
+
+    lv_dclock_refr_text(obj);
+}
+
+/*=====================
+ * Getter functions
+ *====================*/
+
+uint32_t lv_dclock_get_text_selection_start(const lv_obj_t * obj)
+{
+    LV_ASSERT_OBJ(obj, MY_CLASS);
+
+#if LV_DCLOCK_TEXT_SELECTION
+    lv_dclock_t * dclock = (lv_dclock_t *)obj;
+    return dclock->sel_start;
+
+#else
+    LV_UNUSED(obj); /*Unused*/
+    return LV_DCLOCK_TEXT_SELECTION_OFF;
+#endif
+}
+
+uint32_t lv_dclock_get_text_selection_end(const lv_obj_t * obj)
+{
+    LV_ASSERT_OBJ(obj, MY_CLASS);
+
+#if LV_DCLOCK_TEXT_SELECTION
+    lv_dclock_t * dclock = (lv_dclock_t *)obj;
+    return dclock->sel_end;
+#else
+    LV_UNUSED(obj); /*Unused*/
+    return LV_DCLOCK_TEXT_SELECTION_OFF;
+#endif
+}
+
+
+/*=====================
+ * Other functions
+ *====================*/
+
+/**
+ * @brief           Calculate the digital clock by the 24-hours mode
+ * @param obj       pointer to the values for hour/minute/seconds and the bool value to select AM/PM
+ * @return          selection end index. The function of lv_dclock_24_timer_cb will call it.
+ */
+void clock_count_24(int * hour, int * minute, int * seconds)
+{
+    (*seconds)++;
+
+    if(*seconds == 60) {
+        *seconds = 0;
+        (*minute)++;
+    }
+
+    if(*minute == 60) {
+        *minute = 0;
+        (*hour)++;
+    }
+
+    if(*hour == 24) {
+        *hour = 0;
+    }
+}
+
+/**
+ * @brief           Calculate the digital clock by the 12-hours mode
+ * @param obj       pointer to the values for hour/minute/seconds and the bool value to select AM/PM
+ * @return          selection end index. The function of lv_dclock_12_timer_cb will call it.
+ */
+void clock_count_12(int * hour, int * minute, int * seconds, char * meridiem)
+{
+
+    (*seconds)++;
+    if(*seconds == 60) {
+        *seconds = 0;
+        (*minute)++;
+    }
+    if(*minute == 60) {
+        *minute = 0;
+        if(*hour < 12) {
+            (*hour)++;
+        }
+        else {
+            (*hour)++;
+            (*hour) = (*hour) % 12;
+        }
+    }
+    if(*hour == 12 && *seconds == 0 && *minute == 0) {
+        if((strcmp(meridiem, "PM") == 0)) {
+            strcpy(meridiem, "AM");
+        }
+        else {
+            strcpy(meridiem, "PM");
+        }
+    }
+}
+
+
+/**********************
+ *   STATIC FUNCTIONS
+ **********************/
+
+static void lv_dclock_constructor(const lv_obj_class_t * class_p, lv_obj_t * obj)
+{
+    LV_UNUSED(class_p);
+    LV_TRACE_OBJ_CREATE("begin");
+
+    lv_dclock_t * dclock = (lv_dclock_t *)obj;
+
+    dclock->text       = NULL;
+    dclock->static_txt = 0;
+    dclock->recolor    = 0;
+    dclock->offset.x = 0;
+    dclock->offset.y = 0;
+
+#if LV_DCLOCK_TEXT_SELECTION
+    dclock->sel_start = LV_DRAW_LABEL_NO_TXT_SEL;
+    dclock->sel_end   = LV_DRAW_LABEL_NO_TXT_SEL;
+#endif
+
+    lv_obj_clear_flag(obj, LV_OBJ_FLAG_CLICKABLE);
+
+    LV_TRACE_OBJ_CREATE("finished");
+}
+
+static void lv_dclock_destructor(const lv_obj_class_t * class_p, lv_obj_t * obj)
+{
+    LV_UNUSED(class_p);
+    lv_dclock_t * dclock = (lv_dclock_t *)obj;
+
+    if(!dclock->static_txt) lv_mem_free(dclock->text);
+    dclock->text = NULL;
+}
+
+static void lv_dclock_event(const lv_obj_class_t * class_p, lv_event_t * e)
+{
+    LV_UNUSED(class_p);
+
+    lv_res_t res;
+
+    /*Call the ancestor's event handler*/
+    res = lv_obj_event_base(MY_CLASS, e);
+    if(res != LV_RES_OK) return;
+
+    lv_event_code_t code = lv_event_get_code(e);
+    lv_obj_t * obj = lv_event_get_target(e);
+
+    if(code == LV_EVENT_STYLE_CHANGED) {
+        lv_dclock_refr_text(obj);
+    }
+    else if(code == LV_EVENT_REFR_EXT_DRAW_SIZE) {
+        /* Italic or other non-typical letters can be drawn of out of the object.
+         * It happens if box_w + ofs_x > adw_w in the glyph.
+         * To avoid this add some extra draw area.
+         * font_h / 4 is an empirical value. */
+        const lv_font_t * font = lv_obj_get_style_text_font(obj, LV_PART_MAIN);
+        lv_coord_t font_h = lv_font_get_line_height(font);
+        lv_event_set_ext_draw_size(e, font_h / 4);
+    }
+    else if(code == LV_EVENT_SIZE_CHANGED) {
+        lv_dclock_refr_text(obj);
+    }
+    else if(code == LV_EVENT_GET_SELF_SIZE) {
+        lv_point_t size;
+        lv_dclock_t * dclock = (lv_dclock_t *)obj;
+        const lv_font_t * font = lv_obj_get_style_text_font(obj, LV_PART_MAIN);
+        lv_coord_t letter_space = lv_obj_get_style_text_letter_space(obj, LV_PART_MAIN);
+        lv_coord_t line_space = lv_obj_get_style_text_line_space(obj, LV_PART_MAIN);
+        lv_text_flag_t flag = LV_TEXT_FLAG_NONE;
+        if(dclock->recolor != 0) flag |= LV_TEXT_FLAG_RECOLOR;
+        if(dclock->expand != 0) flag |= LV_TEXT_FLAG_EXPAND;
+
+        lv_coord_t w = lv_obj_get_content_width(obj);
+        if(lv_obj_get_style_width(obj, LV_PART_MAIN) == LV_SIZE_CONTENT && !obj->w_layout) w = LV_COORD_MAX;
+        else w = lv_obj_get_content_width(obj);
+
+        lv_txt_get_size(&size, dclock->text, font, letter_space, line_space, w, flag);
+
+        lv_point_t * self_size = lv_event_get_param(e);
+        self_size->x = LV_MAX(self_size->x, size.x);
+        self_size->y = LV_MAX(self_size->y, size.y);
+    }
+    else if(code == LV_EVENT_DRAW_MAIN) {
+        draw_main(e);
+    }
+}
+
+static void draw_main(lv_event_t * e)
+{
+    lv_obj_t * obj = lv_event_get_target(e);
+    lv_dclock_t * dclock = (lv_dclock_t *)obj;
+    lv_draw_ctx_t * draw_ctx = lv_event_get_draw_ctx(e);
+
+    lv_area_t txt_coords;
+    lv_obj_get_content_coords(obj, &txt_coords);
+
+    lv_text_flag_t flag = LV_TEXT_FLAG_NONE;
+    if(dclock->recolor != 0) flag |= LV_TEXT_FLAG_RECOLOR;
+    if(dclock->expand != 0) flag |= LV_TEXT_FLAG_EXPAND;
+    if(lv_obj_get_style_width(obj, LV_PART_MAIN) == LV_SIZE_CONTENT && !obj->w_layout) flag |= LV_TEXT_FLAG_FIT;
+
+    lv_draw_label_dsc_t dclock_draw_dsc;
+    lv_draw_label_dsc_init(&dclock_draw_dsc);
+
+    dclock_draw_dsc.ofs_x = dclock->offset.x;
+    dclock_draw_dsc.ofs_y = dclock->offset.y;
+
+    dclock_draw_dsc.flag = flag;
+    lv_obj_init_draw_label_dsc(obj, LV_PART_MAIN, &dclock_draw_dsc);
+    lv_bidi_calculate_align(&dclock_draw_dsc.align, &dclock_draw_dsc.bidi_dir, dclock->text);
+
+    dclock_draw_dsc.sel_start = lv_dclock_get_text_selection_start(obj);
+    dclock_draw_dsc.sel_end = lv_dclock_get_text_selection_end(obj);
+    if(dclock_draw_dsc.sel_start != LV_DRAW_LABEL_NO_TXT_SEL && dclock_draw_dsc.sel_end != LV_DRAW_LABEL_NO_TXT_SEL) {
+        dclock_draw_dsc.sel_color = lv_obj_get_style_text_color_filtered(obj, LV_PART_SELECTED);
+        dclock_draw_dsc.sel_bg_color = lv_obj_get_style_bg_color(obj, LV_PART_SELECTED);
+    }
+
+    /* In SCROLL and SCROLL_CIRCULAR mode the CENTER and RIGHT are pointless, so remove them.
+     * (In addition, they will create misalignment in this situation)*/
+
+#if LV_DCLOCK_LONG_TXT_HINT
+    lv_draw_label_hint_t * hint = &dclock->hint;
+    if(dclock->long_mode == LV_DCLOCK_LONG_SCROLL_CIRCULAR || lv_area_get_height(&txt_coords) < LV_DCLOCK_HINT_HEIGHT_LIMIT)
+        hint = NULL;
+
+#else
+    /*Just for compatibility*/
+    lv_draw_label_hint_t * hint = NULL;
+#endif
+
+    lv_area_t txt_clip;
+    bool is_common = _lv_area_intersect(&txt_clip, &txt_coords, draw_ctx->clip_area);
+    if(!is_common) return;
+
+    lv_draw_label(draw_ctx, &dclock_draw_dsc, &txt_coords, dclock->text, hint); /// copy the above case
+    const lv_area_t * clip_area_ori = draw_ctx->clip_area;
+    draw_ctx->clip_area = &txt_clip;
+
+    draw_ctx->clip_area = clip_area_ori;
+}
+
+/**
+ * Refresh the dclock with its text stored in its extended data
+ * @param dclock pointer to a label object
+ */
+static void lv_dclock_refr_text(lv_obj_t * obj)
+{
+
+    lv_dclock_t * dclock = (lv_dclock_t *)obj;
+    if(dclock->text == NULL) return;
+#if LV_DCLOCK_LONG_TXT_HINT
+    dclock->hint.line_start = -1; /*The hint is invalid if the text changes*/
+#endif
+
+    lv_area_t txt_coords;
+    lv_obj_get_content_coords(obj, &txt_coords);
+    lv_coord_t max_w         = lv_area_get_width(&txt_coords);
+    const lv_font_t * font   = lv_obj_get_style_text_font(obj, LV_PART_MAIN);
+    lv_coord_t line_space = lv_obj_get_style_text_line_space(obj, LV_PART_MAIN);
+    lv_coord_t letter_space = lv_obj_get_style_text_letter_space(obj, LV_PART_MAIN);
+
+    /*Calc. the height and longest line*/
+    lv_point_t size;
+    lv_text_flag_t flag = LV_TEXT_FLAG_NONE;
+    if(dclock->recolor != 0) flag |= LV_TEXT_FLAG_RECOLOR;
+    if(dclock->expand != 0) flag |= LV_TEXT_FLAG_EXPAND;
+    if(lv_obj_get_style_width(obj, LV_PART_MAIN) == LV_SIZE_CONTENT && !obj->w_layout) flag |= LV_TEXT_FLAG_FIT;
+
+    lv_txt_get_size(&size, dclock->text, font, letter_space, line_space, max_w, flag);
+
+    lv_obj_refresh_self_size(obj);
+
+    lv_obj_invalidate(obj);
+
+}
+
+#endif
diff --git a/src/extra/widgets/dclock/lv_dclock.h b/src/extra/widgets/dclock/lv_dclock.h
new file mode 100644
index 000000000..fcf1885a8
--- /dev/null
+++ b/src/extra/widgets/dclock/lv_dclock.h
@@ -0,0 +1,130 @@
+/**
+ * @file lv_dclock.h
+ *
+ */
+
+#ifndef LV_DCLOCK_H
+#define LV_DCLOCK_H
+
+#ifdef __cplusplus
+extern "C" {
+#endif
+
+/*********************
+ *      INCLUDES
+ *********************/
+#include "../../../lv_conf_internal.h"
+
+#if LV_USE_DCLOCK != 0
+
+#include <stdarg.h>
+#include <stdlib.h>
+#include "../../../core/lv_obj.h"
+#include "../../../font/lv_font.h"
+#include "../../../font/lv_symbol_def.h"
+#include "../../../misc/lv_txt.h"
+#include "../../../draw/lv_draw.h"
+
+/*********************
+ *      DEFINES
+ *********************/
+#define LV_DCLOCK_WAIT_CHAR_COUNT        3
+#define LV_DCLOCK_DOT_NUM 3
+#define LV_DCLOCK_POS_LAST 0xFFFF
+#define LV_DCLOCK_TEXT_SELECTION_OFF LV_DRAW_LABEL_NO_TXT_SEL
+
+
+LV_EXPORT_CONST_INT(LV_DCLOCK_DOT_NUM);
+LV_EXPORT_CONST_INT(LV_DCLOCK_POS_LAST);
+LV_EXPORT_CONST_INT(LV_DCLOCK_TEXT_SELECTION_OFF);
+
+/**********************
+ *      TYPEDEFS
+ **********************/
+
+
+typedef struct {
+    lv_obj_t obj;
+    char * text;
+
+#if LV_DCLOCK_TEXT_SELECTION
+    uint32_t sel_start;
+    uint32_t sel_end;
+#endif
+
+    lv_point_t offset; /*Text draw position offset*/
+    uint8_t static_txt : 1;             /*Flag to indicate the text is static*/
+    uint8_t recolor : 1;                /*Enable in-line letter re-coloring*/
+    uint8_t expand : 1;                 /*Ignore real width (used by the library with LV_LABEL_LONG_SCROLL)*/
+} lv_dclock_t;
+
+
+extern const lv_obj_class_t lv_dclock_class;
+
+/**********************
+ * GLOBAL PROTOTYPES
+ **********************/
+
+/**
+ * Create a dclock object
+ * @param parent    pointer to an object, it will be the parent of the new label.
+         input_time  Type the time as the following:
+ * @return          pointer to the created button
+ * Example:  lv_obj_t * dclock = lv_dclock_create(lv_obj_t * parent,"11:59:55 AM", LV_DCLOCK_SHOW_SECONDS_TRUE,LV_DCLOCK_SHOW_MERIDIEM_TRUE);
+ */
+
+lv_obj_t * lv_dclock_create(lv_obj_t * parent, char * input_time);
+
+/*=====================
+ * Setter functions
+ *====================*/
+
+void lv_dclock_set_text_fmt(lv_obj_t * obj, const char * fmt, ...) LV_FORMAT_ATTRIBUTE(2, 3);
+
+void lv_dclock_set_text(lv_obj_t * obj, const char * text);
+
+/**
+ * @brief           Calculate the digital clock by the 12-hours mode
+ * @param obj       pointer to the values for hour/minute/seconds and the bool value to select AM/PM
+ * @return          selection end index. The function of lv_dclock_12_timer_cb will call it.
+ */
+void clock_count_12(int * hour, int * minute, int * seconds, char * meridiem);
+
+
+/**
+ * @brief           Calculate the digital clock by the 24-hours mode
+ * @param obj       pointer to the values for hour/minute/seconds and the bool value to select AM/PM
+ * @return          selection end index. The function of lv_dclock_24_timer_cb will call it.
+ */
+void clock_count_24(int * hour, int * minute, int * seconds);
+
+/*=====================
+ * Getter functions
+ *====================*/
+
+/**
+ * @brief Get the selection start index.
+ * @param obj       pointer to a dclock object.
+ * @return          selection start index. `LV_LABEL_TEXT_SELECTION_OFF` if nothing is selected.
+ */
+uint32_t lv_dclock_get_text_selection_start(const lv_obj_t * obj);
+
+/**
+ * @brief Get the selection end index.
+ * @param obj       pointer to a dclock object.
+ * @return          selection end index. `LV_LABEL_TXT_SEL_OFF` if nothing is selected.
+ */
+uint32_t lv_dclock_get_text_selection_end(const lv_obj_t * obj);
+
+
+/**********************
+ *      MACROS
+ **********************/
+
+#endif /*LV_USE_DCLOCK*/
+
+#ifdef __cplusplus
+} /*extern "C"*/
+#endif
+
+#endif /*LV_DCLOCK_H*/
diff --git a/src/extra/widgets/keyboard/chinese_library.h b/src/extra/widgets/keyboard/chinese_library.h
new file mode 100644
index 000000000..077286634
--- /dev/null
+++ b/src/extra/widgets/keyboard/chinese_library.h
@@ -0,0 +1,1237 @@
+/**********************
+ *  STATIC VARIABLES
+ **********************/
+
+#if LV_USE_ZH_KEYBOARD
+
+struct PINYIN_initials {
+    char * PINYIN;
+    char * PINYIN_mp;
+};
+
+#if LV_ZH_KEYBOARD_MINI
+/*Chinese Characters UTF-8*/
+static char PINYIN_mp_a[] = {"啊阿"};
+static char PINYIN_mp_ai[] = {"爱矮挨哎"};
+static char PINYIN_mp_an[] = {"按安暗"};
+static char PINYIN_mp_ang[] = {"昂"};
+static char PINYIN_mp_ao[] = {"傲奥熬"};
+static char PINYIN_mp_ba[] = {"把八吧爸"};
+static char PINYIN_mp_bai[] = {"百白"};
+static char PINYIN_mp_ban[] = {"半办班"};
+static char PINYIN_mp_bang[] = {"帮棒绑"};
+static char PINYIN_mp_bao[] = {"包抱报饱"};
+static char PINYIN_mp_bei[] = {"被北倍杯背悲"};
+static char PINYIN_mp_ben[] = {"本奔苯笨"};
+static char PINYIN_mp_beng[] = {"蹦绷甭崩"};
+static char PINYIN_mp_bi[] = {"比笔闭鼻碧必避逼"};
+static char PINYIN_mp_bian[] = {"边变便"};
+static char PINYIN_mp_biao[] = {"表标"};
+static char PINYIN_mp_bie[] = {"别憋"};
+static char PINYIN_mp_bin[] = {"宾彬"};
+static char PINYIN_mp_bing[] = {"并病兵冰"};
+static char PINYIN_mp_bo[] = {"波播泊博伯"};
+static char PINYIN_mp_bu[] = {"不步"};
+static char PINYIN_mp_ca[] = {"擦"};
+static char PINYIN_mp_cai[] = {"猜才菜蔡"};
+static char PINYIN_mp_can[] = {"蚕残掺参惨惭"};
+static char PINYIN_mp_cang[] = {"藏仓"};
+static char PINYIN_mp_cao[] = {"草操曹"};
+static char PINYIN_mp_ce[] = {"册厕"};
+static char PINYIN_mp_cen[] = {"参"};
+static char PINYIN_mp_ceng[] = {"曾层"};
+static char PINYIN_mp_cha[] = {"查插叉茶差"};
+static char PINYIN_mp_chai[] = {"菜柴拆"};
+static char PINYIN_mp_chan[] = {"产缠掺"};
+static char PINYIN_mp_chang[] = {"长唱常场厂"};
+static char PINYIN_mp_chao[] = {"抄超吵"};
+static char PINYIN_mp_che[] = {"车"};
+static char PINYIN_mp_chen[] = {"趁称陈"};
+static char PINYIN_mp_cheng[] = {"成乘称城程"};
+static char PINYIN_mp_chi[] = {"吃尺斥"};
+static char PINYIN_mp_chong[] = {"冲重虫充"};
+static char PINYIN_mp_chou[] = {"抽愁臭仇丑"};
+static char PINYIN_mp_chu[] = {"出处初"};
+static char PINYIN_mp_chuai[] = {"揣"};
+static char PINYIN_mp_chuan[] = {"穿船传串川"};
+static char PINYIN_mp_chuang[] = {"窗床闯创"};
+static char PINYIN_mp_chui[] = {"吹垂炊"};
+static char PINYIN_mp_chun[] = {"春唇"};
+static char PINYIN_mp_chuo[] = {"戳绰"};
+static char PINYIN_mp_ci[] = {"次此词"};
+static char PINYIN_mp_cong[] = {"从葱匆聪"};
+static char PINYIN_mp_cou[] = {"凑"};
+static char PINYIN_mp_cu[] = {"粗醋"};
+static char PINYIN_mp_cuan[] = {"窜"};
+static char PINYIN_mp_cui[] = {"催脆摧"};
+static char PINYIN_mp_cun[] = {"村寸存"};
+static char PINYIN_mp_cuo[] = {"错搓挫"};
+static char PINYIN_mp_da[] = {"大答达打搭"};
+static char PINYIN_mp_dai[] = {"带代呆"};
+static char PINYIN_mp_dan[] = {"但单蛋担胆淡丹"};
+static char PINYIN_mp_dang[] = {"当党挡档荡"};
+static char PINYIN_mp_dao[] = {"到道倒刀岛盗稻捣"};
+static char PINYIN_mp_de[] = {"的地得德底"};
+static char PINYIN_mp_dei[] = {"得"};
+static char PINYIN_mp_deng[] = {"等灯邓登"};
+static char PINYIN_mp_di[] = {"地第底低敌抵滴帝"};
+static char PINYIN_mp_dia[] = {"嗲"};
+static char PINYIN_mp_dian[] = {"点电店"};
+static char PINYIN_mp_diao[] = {"掉钓叼吊"};
+static char PINYIN_mp_die[] = {"爹跌"};
+static char PINYIN_mp_ding[] = {"顶定盯订丁钉"};
+static char PINYIN_mp_diu[] = {"丢"};
+static char PINYIN_mp_dong[] = {"动东懂洞冻冬董"};
+static char PINYIN_mp_dou[] = {"都斗豆逗陡抖"};
+static char PINYIN_mp_du[] = {"读度毒渡堵独肚"};
+static char PINYIN_mp_duan[] = {"段短断端"};
+static char PINYIN_mp_dui[] = {"对队堆兑"};
+static char PINYIN_mp_dun[] = {"吨顿蹲钝盾"};
+static char PINYIN_mp_duo[] = {"多朵夺"};
+static char PINYIN_mp_e[] = {"饿额鹅"};
+static char PINYIN_mp_ei[] = {"诶"};
+static char PINYIN_mp_en[] = {"恩嗯"};
+static char PINYIN_mp_er[] = {"而二耳儿饵尔"};
+static char PINYIN_mp_fa[] = {"发法罚伐乏筏阀"};
+static char PINYIN_mp_fan[] = {"反饭翻番犯凡帆"};
+static char PINYIN_mp_fang[] = {"放房防纺芳方访仿坊"};
+static char PINYIN_mp_fei[] = {"非飞肥费肺废"};
+static char PINYIN_mp_fen[] = {"分份芬粉坟奋"};
+static char PINYIN_mp_feng[] = {"风封蜂丰"};
+static char PINYIN_mp_fo[] = {"佛"};
+static char PINYIN_mp_fou[] = {"否"};
+static char PINYIN_mp_fu[] = {"扶浮富福负伏付复服附俯芙夫父"};
+static char PINYIN_mp_ga[] = {"夹嘎"};
+static char PINYIN_mp_gai[] = {"该改盖"};
+static char PINYIN_mp_gan[] = {"赶干感"};
+static char PINYIN_mp_gang[] = {"刚钢"};
+static char PINYIN_mp_gao[] = {"高搞告羔"};
+static char PINYIN_mp_ge[] = {"个各歌哥搁格阁隔革"};
+static char PINYIN_mp_gei[] = {"给"};
+static char PINYIN_mp_gen[] = {"跟根"};
+static char PINYIN_mp_geng[] = {"更"};
+static char PINYIN_mp_gong[] = {"工公功共弓巩"};
+static char PINYIN_mp_gou[] = {"够沟狗钩勾"};
+static char PINYIN_mp_gu[] = {"古股鼓谷故"};
+static char PINYIN_mp_gua[] = {"挂刮瓜"};
+static char PINYIN_mp_guai[] = {"怪拐"};
+static char PINYIN_mp_guan[] = {"关管官观馆惯罐"};
+static char PINYIN_mp_guang[] = {"光广逛"};
+static char PINYIN_mp_gui[] = {"归贵鬼跪轨规"};
+static char PINYIN_mp_gun[] = {"滚棍"};
+static char PINYIN_mp_guo[] = {"过国果裹锅郭"};
+static char PINYIN_mp_ha[] = {"哈蛤虾呵"};
+static char PINYIN_mp_hai[] = {"还海害咳氦孩"};
+static char PINYIN_mp_han[] = {"喊含汗寒汉旱"};
+static char PINYIN_mp_hang[] = {"行巷航"};
+static char PINYIN_mp_hao[] = {"好号浩"};
+static char PINYIN_mp_he[] = {"和喝合河禾核何"};
+static char PINYIN_mp_hei[] = {"黑嘿嗨"};
+static char PINYIN_mp_hen[] = {"很狠恨"};
+static char PINYIN_mp_heng[] = {"横恒哼"};
+static char PINYIN_mp_hong[] = {"红轰哄虹洪"};
+static char PINYIN_mp_hou[] = {"后厚吼喉"};
+static char PINYIN_mp_hu[] = {"湖户呼虎"};
+static char PINYIN_mp_hua[] = {"话花化画华划"};
+static char PINYIN_mp_huai[] = {"坏怀"};
+static char PINYIN_mp_huan[] = {"换还唤环患缓欢"};
+static char PINYIN_mp_huang[] = {"黄慌晃"};
+static char PINYIN_mp_hui[] = {"回会灰绘挥汇辉"};
+static char PINYIN_mp_hun[] = {"混昏浑婚"};
+static char PINYIN_mp_huo[] = {"或活火伙货"};
+static char PINYIN_mp_ji[] = {"几及急既即机鸡积记级极计"};
+static char PINYIN_mp_jia[] = {"家加假价架甲佳夹嘉"};
+static char PINYIN_mp_jian[] = {"见件减尖间贱肩兼建检健艰荐剑"};
+static char PINYIN_mp_jiang[] = {"将讲江奖降浆姜蒋"};
+static char PINYIN_mp_jiao[] = {"叫脚交角教觉焦"};
+static char PINYIN_mp_jie[] = {"接节街借皆截解界结届姐"};
+static char PINYIN_mp_jin[] = {"进近今仅紧金斤"};
+static char PINYIN_mp_jing[] = {"竟静井惊经镜京净敬精景"};
+static char PINYIN_mp_jiong[] = {"窘炅"};
+static char PINYIN_mp_jiu[] = {"就九酒旧久揪救纠舅"};
+static char PINYIN_mp_ju[] = {"句举巨局具"};
+static char PINYIN_mp_juan[] = {"卷倦捐眷"};
+static char PINYIN_mp_jue[] = {"决绝觉角爵"};
+static char PINYIN_mp_jun[] = {"军君均菌俊匀"};
+static char PINYIN_mp_ka[] = {"卡喀咯咖胩咔佧"};
+static char PINYIN_mp_kai[] = {"开凯慨楷"};
+static char PINYIN_mp_kan[] = {"看砍刊坎"};
+static char PINYIN_mp_kang[] = {"抗炕扛"};
+static char PINYIN_mp_kao[] = {"靠考烤"};
+static char PINYIN_mp_ke[] = {"咳可克棵科颗刻课客壳渴"};
+static char PINYIN_mp_ken[] = {"肯啃恳垦"};
+static char PINYIN_mp_keng[] = {"坑吭"};
+static char PINYIN_mp_kong[] = {"空孔控恐"};
+static char PINYIN_mp_kou[] = {"口扣抠"};
+static char PINYIN_mp_ku[] = {"哭库苦枯"};
+static char PINYIN_mp_kua[] = {"跨垮挎"};
+static char PINYIN_mp_kuai[] = {"快块筷会"};
+static char PINYIN_mp_kuan[] = {"宽款"};
+static char PINYIN_mp_kuang[] = {"矿筐狂框况旷匡"};
+static char PINYIN_mp_kui[] = {"亏愧奎窥"};
+static char PINYIN_mp_kun[] = {"捆困昆"};
+static char PINYIN_mp_kuo[] = {"阔扩"};
+static char PINYIN_mp_la[] = {"拉啦辣蜡腊"};
+static char PINYIN_mp_lai[] = {"来赖莱"};
+static char PINYIN_mp_lan[] = {"蓝兰烂拦"};
+static char PINYIN_mp_lang[] = {"浪郎朗"};
+static char PINYIN_mp_lao[] = {"老捞牢"};
+static char PINYIN_mp_le[] = {"了乐勒嘞"};
+static char PINYIN_mp_lei[] = {"类累泪雷垒勒擂蕾肋镭儡磊缧诔耒酹羸嫘檑嘞漯"};
+static char PINYIN_mp_leng[] = {"冷棱楞愣"};
+static char PINYIN_mp_li[] = {"里离力立李例哩理利梨厘礼历丽吏"};
+static char PINYIN_mp_lia[] = {"俩"};
+static char PINYIN_mp_lian[] = {"连联练莲恋脸炼链敛"};
+static char PINYIN_mp_liang[] = {"两亮辆凉粮梁量良晾谅"};
+static char PINYIN_mp_liao[] = {"了料撩辽僚"};
+static char PINYIN_mp_lie[] = {"列裂猎劣烈咧"};
+static char PINYIN_mp_lin[] = {"林临淋邻吝"};
+static char PINYIN_mp_ling[] = {"另令领零铃玲灵岭"};
+static char PINYIN_mp_liu[] = {"六流留刘柳"};
+static char PINYIN_mp_lo[] = {"咯"};
+static char PINYIN_mp_long[] = {"龙拢笼聋隆垄窿陇茏"};
+static char PINYIN_mp_lou[] = {"楼搂漏陋露娄篓喽嵝"};
+static char PINYIN_mp_lu[] = {"路露录鹿陆炉卢鲁卤芦颅"};
+static char PINYIN_mp_luan[] = {"乱卵峦孪"};
+static char PINYIN_mp_lun[] = {"论轮抡伦沦囵"};
+static char PINYIN_mp_luo[] = {"落罗裸骡烙螺萝洛骆逻"};
+static char PINYIN_mp_lv[] = {"绿率铝驴旅屡滤吕律"};
+static char PINYIN_mp_lue[] = {"略掠"};
+static char PINYIN_mp_ma[] = {"吗妈马嘛麻骂抹码玛蚂"};
+static char PINYIN_mp_mai[] = {"买卖迈埋麦脉"};
+static char PINYIN_mp_man[] = {"满慢瞒漫蛮蔓曼埋"};
+static char PINYIN_mp_mang[] = {"忙芒盲莽茫氓"};
+static char PINYIN_mp_mao[] = {"毛冒帽猫矛貌茂贸"};
+static char PINYIN_mp_me[] = {"么麽"};
+static char PINYIN_mp_mei[] = {"没每煤美酶妹枚霉玫"};
+static char PINYIN_mp_men[] = {"门们"};
+static char PINYIN_mp_meng[] = {"猛梦盟檬萌瞢"};
+static char PINYIN_mp_mi[] = {"米密迷眯蜜谜觅秘弥幂"};
+static char PINYIN_mp_mian[] = {"面棉免绵眠缅勉冕"};
+static char PINYIN_mp_miao[] = {"秒苗庙妙描瞄"};
+static char PINYIN_mp_mie[] = {"灭蔑咩"};
+static char PINYIN_mp_min[] = {"民抿敏闽皿悯闵"};
+static char PINYIN_mp_ming[] = {"名明命鸣"};
+static char PINYIN_mp_miu[] = {"谬缪"};
+static char PINYIN_mp_mo[] = {"摸磨抹末膜墨没莫默"};
+static char PINYIN_mp_mou[] = {"某谋牟眸"};
+static char PINYIN_mp_mu[] = {"木母亩幕目墓募慕睦沐"};
+static char PINYIN_mp_na[] = {"那拿哪纳钠娜内"};
+static char PINYIN_mp_nai[] = {"乃耐奶"};
+static char PINYIN_mp_nan[] = {"难南男"};
+static char PINYIN_mp_nang[] = {"囊"};
+static char PINYIN_mp_nao[] = {"闹脑恼孬"};
+static char PINYIN_mp_ne[] = {"呢哪那呐讷疒"};
+static char PINYIN_mp_nei[] = {"内哪馁那"};
+static char PINYIN_mp_nen[] = {"嫩恁"};
+static char PINYIN_mp_neng[] = {"能"};
+static char PINYIN_mp_ng[] = {"嗯"};
+static char PINYIN_mp_ni[] = {"你泥拟腻逆呢"};
+static char PINYIN_mp_nian[] = {"年念捻撵拈碾"};
+static char PINYIN_mp_niang[] = {"娘酿"};
+static char PINYIN_mp_niao[] = {"鸟尿溺"};
+static char PINYIN_mp_nie[] = {"捏镍聂"};
+static char PINYIN_mp_nin[] = {"您恁"};
+static char PINYIN_mp_ning[] = {"拧凝宁柠狞"};
+static char PINYIN_mp_niu[] = {"牛扭纽忸妞拗"};
+static char PINYIN_mp_nong[] = {"弄浓农脓侬"};
+static char PINYIN_mp_nou[] = {"耨"};
+static char PINYIN_mp_nu[] = {"怒努奴"};
+static char PINYIN_mp_nv[] = {"女"};
+static char PINYIN_mp_nue[] = {"虐疟"};
+static char PINYIN_mp_nuan[] = {"暖"};
+static char PINYIN_mp_nuo[] = {"挪诺"};
+static char PINYIN_mp_o[] = {"哦喔噢"};
+static char PINYIN_mp_ou[] = {"偶欧藕鸥区"};
+static char PINYIN_mp_pa[] = {"怕爬趴扒帕琶杷"};
+static char PINYIN_mp_pai[] = {"派排拍牌迫"};
+static char PINYIN_mp_pan[] = {"盘盼判攀畔潘"};
+static char PINYIN_mp_pang[] = {"旁胖仿"};
+static char PINYIN_mp_pao[] = {"跑抛炮泡刨"};
+static char PINYIN_mp_pei[] = {"陪配赔胚佩培沛裴"};
+static char PINYIN_mp_pen[] = {"喷盆"};
+static char PINYIN_mp_peng[] = {"碰捧棚朋彭鹏"};
+static char PINYIN_mp_pi[] = {"批皮披匹劈辟"};
+static char PINYIN_mp_pian[] = {"片篇骗偏便扁翩"};
+static char PINYIN_mp_piao[] = {"票飘漂瓢"};
+static char PINYIN_mp_pie[] = {"瞥撇"};
+static char PINYIN_mp_pin[] = {"品贫聘拼频"};
+static char PINYIN_mp_ping[] = {"平凭瓶评屏乒萍苹"};
+static char PINYIN_mp_po[] = {"破坡颇婆泼迫泊魄"};
+static char PINYIN_mp_pou[] = {"剖"};
+static char PINYIN_mp_pu[] = {"扑铺仆蒲"};
+static char PINYIN_mp_qi[] = {"起其七气期齐妻骑汽"};
+static char PINYIN_mp_qia[] = {"恰卡掐洽"};
+static char PINYIN_mp_qian[] = {"前钱千牵浅签欠骞"};
+static char PINYIN_mp_qiang[] = {"强枪墙抢腔"};
+static char PINYIN_mp_qiao[] = {"桥敲巧悄俏窍雀乔侨"};
+static char PINYIN_mp_qie[] = {"切且怯窃"};
+static char PINYIN_mp_qin[] = {"亲琴寝"};
+static char PINYIN_mp_qing[] = {"请轻清青倩"};
+static char PINYIN_mp_qiong[] = {"穷琼"};
+static char PINYIN_mp_qiu[] = {"求球秋丘仇邱"};
+static char PINYIN_mp_qu[] = {"去取区娶"};
+static char PINYIN_mp_quan[] = {"全权劝圈拳"};
+static char PINYIN_mp_que[] = {"却缺确"};
+static char PINYIN_mp_qui[] = {"鼽"};
+static char PINYIN_mp_qun[] = {"群裙"};
+static char PINYIN_mp_ran[] = {"染燃然冉髯苒蚺"};
+static char PINYIN_mp_rang[] = {"让嚷"};
+static char PINYIN_mp_rao[] = {"饶绕扰"};
+static char PINYIN_mp_re[] = {"热"};
+static char PINYIN_mp_ren[] = {"人任忍认刃仁"};
+static char PINYIN_mp_reng[] = {"仍扔"};
+static char PINYIN_mp_ri[] = {"日"};
+static char PINYIN_mp_rong[] = {"容绒融溶"};
+static char PINYIN_mp_rou[] = {"肉揉柔"};
+static char PINYIN_mp_ru[] = {"如入汝茹乳"};
+static char PINYIN_mp_ruan[] = {"软阮"};
+static char PINYIN_mp_rui[] = {"瑞蕊锐"};
+static char PINYIN_mp_run[] = {"闰润"};
+static char PINYIN_mp_ruo[] = {"若弱"};
+static char PINYIN_mp_sa[] = {"撒洒"};
+static char PINYIN_mp_sai[] = {"塞腮"};
+static char PINYIN_mp_san[] = {"三散伞"};
+static char PINYIN_mp_sang[] = {"桑"};
+static char PINYIN_mp_sao[] = {"扫嫂"};
+static char PINYIN_mp_se[] = {"色涩"};
+static char PINYIN_mp_sen[] = {"森"};
+static char PINYIN_mp_seng[] = {"僧"};
+static char PINYIN_mp_sha[] = {"杀沙啥纱"};
+static char PINYIN_mp_shai[] = {"晒筛色"};
+static char PINYIN_mp_shan[] = {"山闪衫善"};
+static char PINYIN_mp_shang[] = {"上伤"};
+static char PINYIN_mp_shao[] = {"少烧哨勺"};
+static char PINYIN_mp_she[] = {"蛇设舌"};
+static char PINYIN_mp_shei[] = {"谁"};
+static char PINYIN_mp_shen[] = {"身伸深神"};
+static char PINYIN_mp_sheng[] = {"声省生升"};
+static char PINYIN_mp_shi[] = {"是使十时事室市石"};
+static char PINYIN_mp_shou[] = {"手受收首守"};
+static char PINYIN_mp_shu[] = {"书树数"};
+static char PINYIN_mp_shua[] = {"耍"};
+static char PINYIN_mp_shuai[] = {"摔甩帅"};
+static char PINYIN_mp_shuan[] = {"栓"};
+static char PINYIN_mp_shuang[] = {"双霜"};
+static char PINYIN_mp_shui[] = {"水谁"};
+static char PINYIN_mp_shun[] = {"顺瞬"};
+static char PINYIN_mp_shuo[] = {"说"};
+static char PINYIN_mp_si[] = {"四司"};
+static char PINYIN_mp_song[] = {"送松"};
+static char PINYIN_mp_sou[] = {"艘搜"};
+static char PINYIN_mp_su[] = {"素速"};
+static char PINYIN_mp_suan[] = {"酸算"};
+static char PINYIN_mp_sui[] = {"岁随碎虽"};
+static char PINYIN_mp_sun[] = {"孙损"};
+static char PINYIN_mp_suo[] = {"所缩"};
+static char PINYIN_mp_ta[] = {"他她它"};
+static char PINYIN_mp_tai[] = {"太抬台"};
+static char PINYIN_mp_tan[] = {"谈叹探"};
+static char PINYIN_mp_tang[] = {"躺趟堂"};
+static char PINYIN_mp_tao[] = {"套掏逃"};
+static char PINYIN_mp_te[] = {"特忒"};
+static char PINYIN_mp_teng[] = {"疼腾藤"};
+static char PINYIN_mp_ti[] = {"提替体题"};
+static char PINYIN_mp_tian[] = {"天田添"};
+static char PINYIN_mp_tiao[] = {"条跳挑"};
+static char PINYIN_mp_tie[] = {"铁贴"};
+static char PINYIN_mp_ting[] = {"听霆"};
+static char PINYIN_mp_tong[] = {"同通痛"};
+static char PINYIN_mp_tou[] = {"头偷透"};
+static char PINYIN_mp_tu[] = {"土图兔涂"};
+static char PINYIN_mp_tuan[] = {"团湍"};
+static char PINYIN_mp_tui[] = {"腿推"};
+static char PINYIN_mp_tun[] = {"吞屯"};
+static char PINYIN_mp_tuo[] = {"拖脱托"};
+static char PINYIN_mp_wa[] = {"挖瓦蛙"};
+static char PINYIN_mp_wai[] = {"外歪"};
+static char PINYIN_mp_wan[] = {"完万晚"};
+static char PINYIN_mp_wang[] = {"望忘王往"};
+static char PINYIN_mp_wei[] = {"为位未围"};
+static char PINYIN_mp_wen[] = {"问文闻稳温"};
+static char PINYIN_mp_weng[] = {"翁嗡"};
+static char PINYIN_mp_wo[] = {"我握窝"};
+static char PINYIN_mp_wu[] = {"无五屋物舞"};
+static char PINYIN_mp_xi[] = {"西洗细吸戏"};
+static char PINYIN_mp_xia[] = {"下吓"};
+static char PINYIN_mp_xian[] = {"先线县现"};
+static char PINYIN_mp_xiang[] = {"想向象"};
+static char PINYIN_mp_xiao[] = {"小笑消"};
+static char PINYIN_mp_xie[] = {"写些血"};
+static char PINYIN_mp_xin[] = {"新心欣信"};
+static char PINYIN_mp_xing[] = {"行型形星兴"};
+static char PINYIN_mp_xiong[] = {"雄凶兄"};
+static char PINYIN_mp_xiu[] = {"修"};
+static char PINYIN_mp_xu[] = {"许须需虚"};
+static char PINYIN_mp_xuan[] = {"选悬"};
+static char PINYIN_mp_xue[] = {"学雪"};
+static char PINYIN_mp_xun[] = {"寻讯熏"};
+static char PINYIN_mp_ya[] = {"呀压"};
+static char PINYIN_mp_yan[] = {"眼烟沿盐言演"};
+static char PINYIN_mp_yang[] = {"样养羊洋仰扬"};
+static char PINYIN_mp_yao[] = {"要摇药咬"};
+static char PINYIN_mp_ye[] = {"也夜业"};
+static char PINYIN_mp_yi[] = {"一以已亿衣移依易医乙仪"};
+static char PINYIN_mp_yin[] = {"因引印银音饮"};
+static char PINYIN_mp_ying[] = {"应硬影营迎映"};
+static char PINYIN_mp_yo[] = {"哟育唷"};
+static char PINYIN_mp_yong[] = {"用涌永拥"};
+static char PINYIN_mp_you[] = {"有又由右油游幼优友"};
+static char PINYIN_mp_yu[] = {"与于欲鱼雨余遇语"};
+static char PINYIN_mp_yuan[] = {"远员元"};
+static char PINYIN_mp_yue[] = {"月越约"};
+static char PINYIN_mp_yun[] = {"云运晕"};
+static char PINYIN_mp_za[] = {"杂砸"};
+static char PINYIN_mp_zai[] = {"在再"};
+static char PINYIN_mp_zan[] = {"咱"};
+static char PINYIN_mp_zang[] = {"脏"};
+static char PINYIN_mp_zao[] = {"早造"};
+static char PINYIN_mp_ze[] = {"则责"};
+static char PINYIN_mp_zei[] = {"贼"};
+static char PINYIN_mp_zen[] = {"怎谮"};
+static char PINYIN_mp_zeng[] = {"增赠曾"};
+static char PINYIN_mp_zha[] = {"扎炸渣闸"};
+static char PINYIN_mp_zhai[] = {"摘窄"};
+static char PINYIN_mp_zhan[] = {"站占战"};
+static char PINYIN_mp_zhang[] = {"张章"};
+static char PINYIN_mp_zhao[] = {"找着罩"};
+static char PINYIN_mp_zhe[] = {"着这者折"};
+static char PINYIN_mp_zhen[] = {"真阵镇针"};
+static char PINYIN_mp_zheng[] = {"正整睁"};
+static char PINYIN_mp_zhi[] = {"只之直知制指"};
+static char PINYIN_mp_zhong[] = {"中重种众终"};
+static char PINYIN_mp_zhou[] = {"周粥州轴舟昼"};
+static char PINYIN_mp_zhu[] = {"住主猪"};
+static char PINYIN_mp_zhua[] = {"抓爪"};
+static char PINYIN_mp_zhuai[] = {"拽转"};
+static char PINYIN_mp_zhuan[] = {"转专砖"};
+static char PINYIN_mp_zhuang[] = {"装撞庄壮"};
+static char PINYIN_mp_zhui[] = {"追"};
+static char PINYIN_mp_zhun[] = {"准"};
+static char PINYIN_mp_zhuo[] = {"捉桌"};
+static char PINYIN_mp_zi[] = {"字自子"};
+static char PINYIN_mp_zong[] = {"总"};
+static char PINYIN_mp_zou[] = {"走"};
+static char PINYIN_mp_zu[] = {"组足"};
+static char PINYIN_mp_zuan[] = {"钻赚"};
+static char PINYIN_mp_zui[] = {"最嘴醉罪"};
+static char PINYIN_mp_zun[] = {"尊遵"};
+static char PINYIN_mp_zuo[] = {"做作坐"};
+#else
+/*Chinese Characters UTF-8*/
+static char PINYIN_mp_a[] = {"啊阿呵吖嗄腌锕"};
+static char PINYIN_mp_ai[] = {"爱矮挨哎碍癌艾唉哀蔼隘埃皑呆嗌嫒瑷暧捱砹嗳锿霭乃剀呃噫奇阂崖"};
+static char PINYIN_mp_an[] = {"按安暗岸俺案鞍氨胺厂广庵揞犴铵桉谙鹌埯黯干盒"};
+static char PINYIN_mp_ang[] = {"昂肮盎仰"};
+static char PINYIN_mp_ao[] = {"袄凹傲奥熬懊敖翱澳嚣拗媪廒骜嗷坳遨聱螯獒鏊鳌鏖岙噢"};
+static char PINYIN_mp_ba[] = {"把八吧爸拔罢跋巴芭扒坝霸叭靶笆疤耙捌粑茇岜鲅钯魃菝灞杷伯"};
+static char PINYIN_mp_bai[] = {"百白摆败柏拜佰伯稗捭呗薭掰"};
+static char PINYIN_mp_ban[] = {"半办班般拌搬版斑板伴扳扮瓣颁绊癍坂钣舨阪瘢"};
+static char PINYIN_mp_bang[] = {"帮棒绑磅镑邦榜蚌傍梆膀谤浜蒡彭"};
+static char PINYIN_mp_bao[] = {"包抱报饱保暴薄宝爆剥豹刨雹褒堡苞胞鲍炮瀑龅孢煲褓鸨趵曝簿葆勹"};
+static char PINYIN_mp_bei[] = {"被北倍杯背悲备碑卑贝辈钡焙狈惫臂褙悖蓓鹎鐾呗邶鞴孛陂碚"};
+static char PINYIN_mp_ben[] = {"本奔苯笨夯锛贲畚坌体"};
+static char PINYIN_mp_beng[] = {"蹦绷甭崩迸蚌泵甏嘣堋"};
+static char PINYIN_mp_bi[] = {"比笔闭鼻碧必避逼毕臂彼鄙壁蓖币弊辟蔽毙庇敝陛毖痹秘泌秕薜荸芘萆匕裨畀俾嬖狴筚箅篦舭荜襞庳铋跸吡愎贲滗濞璧哔髀弼妣婢纰佛拂"};
+static char PINYIN_mp_bian[] = {"边变便遍编辩扁贬鞭卞辨辫忭砭匾汴碥蝙褊鳊笾苄窆弁缏煸"};
+static char PINYIN_mp_biao[] = {"表标彪膘杓婊飑飙鳔瘭飚镳裱骠镖髟灬"};
+static char PINYIN_mp_bie[] = {"别憋鳖瘪蹩"};
+static char PINYIN_mp_bin[] = {"宾濒摈彬斌滨膑殡缤髌傧槟玢鬓镔豳份频"};
+static char PINYIN_mp_bing[] = {"并病兵冰丙饼屏秉柄炳摒槟禀邴冫枋绠"};
+static char PINYIN_mp_bo[] = {"拨波播泊博伯驳玻剥薄勃菠钵搏脖帛般柏舶渤铂箔膊魄卜礴跛檗亳鹁踣啵蕃簸钹饽擘佛擗簿趵"};
+static char PINYIN_mp_bu[] = {"不步补布部捕卜簿哺堡埠怖埔瓿逋晡钸钚醭卟"};
+static char PINYIN_mp_ca[] = {"擦拆礤嚓"};
+static char PINYIN_mp_cai[] = {"猜才材财裁采彩睬踩菜蔡"};
+static char PINYIN_mp_can[] = {"蚕残掺参惨惭餐灿骖璨孱黪粲"};
+static char PINYIN_mp_cang[] = {"藏仓沧舱苍伧臧"};
+static char PINYIN_mp_cao[] = {"草操曹槽糙嘈艚螬漕屮"};
+static char PINYIN_mp_ce[] = {"册侧策测厕恻"};
+static char PINYIN_mp_cen[] = {"参岑涔"};
+static char PINYIN_mp_ceng[] = {"曾层蹭噌"};
+static char PINYIN_mp_cha[] = {"查插叉茶差岔搽察茬碴刹诧楂槎镲衩汊馇檫姹杈锸嚓猹苴喳"};
+static char PINYIN_mp_chai[] = {"菜柴拆差豺钗瘥虿侪"};
+static char PINYIN_mp_chan[] = {"产缠掺搀阐颤铲谗蝉单馋觇婵蒇谄冁廛孱蟾羼镡忏潺禅骣躔澶崭掸"};
+static char PINYIN_mp_chang[] = {"长唱常场厂尝肠畅昌敞倡偿猖裳鲳氅菖惝嫦徜鬯阊怅伥昶苌娼倘淌"};
+static char PINYIN_mp_chao[] = {"朝抄超吵潮巢炒嘲剿绰钞怊焯耖晁"};
+static char PINYIN_mp_che[] = {"车撤扯掣彻尺澈坼砗屮"};
+static char PINYIN_mp_chen[] = {"趁称辰臣尘晨沉陈衬橙忱郴榇抻谌碜谶宸龀嗔伧琛沈肜"};
+static char PINYIN_mp_cheng[] = {"成乘盛撑称城程承呈秤诚惩逞骋澄橙塍柽埕铖噌铛酲晟裎枨蛏丞瞠净抢"};
+static char PINYIN_mp_chi[] = {"吃尺迟池翅痴赤齿耻持斥侈弛驰炽匙踟坻茌墀饬媸豉褫敕哧瘛蚩啻鸱眵螭篪魑叱彳笞嗤傺眙"};
+static char PINYIN_mp_chong[] = {"冲重虫充宠崇涌种艟忡舂铳憧茺"};
+static char PINYIN_mp_chou[] = {"抽愁臭仇丑稠绸酬筹踌畴瞅惆俦帱瘳雠"};
+static char PINYIN_mp_chu[] = {"出处初锄除触橱楚础储畜滁矗搐躇厨雏楮杵刍怵绌亍憷蹰黜蜍樗助褚"};
+static char PINYIN_mp_chuai[] = {"揣膪啜嘬搋踹"};
+static char PINYIN_mp_chuan[] = {"穿船传串川喘椽氚遄钏舡舛巛"};
+static char PINYIN_mp_chuang[] = {"窗床闯创疮幢怆"};
+static char PINYIN_mp_chui[] = {"吹垂炊锤捶椎槌棰陲"};
+static char PINYIN_mp_chun[] = {"春唇纯蠢醇淳椿蝽莼鹑朐肫"};
+static char PINYIN_mp_chuo[] = {"戳绰踔啜龊辍淖辶"};
+static char PINYIN_mp_ci[] = {"次此词瓷慈雌磁辞刺茨伺疵赐差兹呲鹚祠糍粢"};
+static char PINYIN_mp_cong[] = {"从丛葱匆聪囱琮枞淙璁骢苁"};
+static char PINYIN_mp_cou[] = {"凑楱辏腠"};
+static char PINYIN_mp_cu[] = {"粗醋簇促卒徂猝蔟蹙酢殂蹴趋趣"};
+static char PINYIN_mp_cuan[] = {"窜蹿篡攒汆爨镩撺"};
+static char PINYIN_mp_cui[] = {"催脆摧翠崔淬衰瘁粹璀啐悴萃毳榱"};
+static char PINYIN_mp_cun[] = {"村寸存蹲忖皴"};
+static char PINYIN_mp_cuo[] = {"错撮搓挫措磋嵯厝鹾脞痤蹉瘥锉矬"};
+static char PINYIN_mp_da[] = {"大答达打搭瘩塔笪耷哒褡疸怛靼妲沓嗒鞑"};
+static char PINYIN_mp_dai[] = {"带代呆戴待袋逮歹贷怠傣大殆呔玳迨岱甙黛骀绐埭隶"};
+static char PINYIN_mp_dan[] = {"但单蛋担弹掸胆淡丹耽旦氮诞郸惮石疸澹瘅萏殚眈聃箪赕儋啖膻"};
+static char PINYIN_mp_dang[] = {"当党挡档荡谠铛宕菪凼裆砀"};
+static char PINYIN_mp_dao[] = {"到道倒刀岛盗稻捣悼导蹈祷帱纛忉焘氘叨刂"};
+static char PINYIN_mp_de[] = {"的地得德底锝"};
+static char PINYIN_mp_dei[] = {"得"};
+static char PINYIN_mp_deng[] = {"等灯邓登澄瞪凳蹬磴镫噔嶝戥簦"};
+static char PINYIN_mp_di[] = {"地第底低敌抵滴帝递嫡弟缔堤的涤提笛迪狄翟蒂觌邸谛诋嘀柢骶羝氐棣睇娣荻碲镝坻籴砥"};
+static char PINYIN_mp_dia[] = {"嗲"};
+static char PINYIN_mp_dian[] = {"点电店殿淀掂颠垫碘惦奠典佃靛滇甸踮钿坫阽癫簟玷巅癜"};
+static char PINYIN_mp_diao[] = {"掉钓叼吊雕调刁碉凋鸟铞铫鲷貂"};
+static char PINYIN_mp_die[] = {"爹跌叠碟蝶迭谍牒堞瓞揲蹀耋鲽垤喋佚"};
+static char PINYIN_mp_ding[] = {"顶定盯订叮丁钉鼎锭町玎铤腚碇疔仃耵酊啶"};
+static char PINYIN_mp_diu[] = {"丢铥"};
+static char PINYIN_mp_dong[] = {"动东懂洞冻冬董栋侗恫峒鸫垌胨胴硐氡岽咚"};
+static char PINYIN_mp_dou[] = {"都斗豆逗陡抖痘兜读蚪窦篼蔸渎钭"};
+static char PINYIN_mp_du[] = {"读度毒渡堵独肚镀赌睹杜督都犊妒顿蠹笃嘟渎椟牍黩髑芏竺"};
+static char PINYIN_mp_duan[] = {"段短断端锻缎椴煅簖"};
+static char PINYIN_mp_dui[] = {"对队堆兑敦镦碓怼憝"};
+static char PINYIN_mp_dun[] = {"吨顿蹲墩敦钝盾囤遁不趸沌盹镦礅炖砘"};
+static char PINYIN_mp_duo[] = {"多朵夺舵剁垛跺惰堕掇哆驮度躲踱沲咄铎裰哚缍隋"};
+static char PINYIN_mp_e[] = {"饿哦额鹅蛾扼俄讹阿遏峨娥恶厄鄂锇谔垩锷阏萼苊轭婀莪鳄颚腭愕呃噩鹗屙"};
+static char PINYIN_mp_ei[] = {"诶"};
+static char PINYIN_mp_en[] = {"恩嗯摁蒽"};
+static char PINYIN_mp_er[] = {"而二耳儿饵尔贰洱珥鲕鸸佴迩铒"};
+static char PINYIN_mp_fa[] = {"发法罚伐乏筏阀珐垡砝"};
+static char PINYIN_mp_fan[] = {"反饭翻番犯凡帆返泛繁烦贩范樊藩矾钒燔蘩畈蕃蹯梵幡拚袢"};
+static char PINYIN_mp_fang[] = {"放房防纺芳方访仿坊妨肪钫彷邡枋舫鲂匚"};
+static char PINYIN_mp_fei[] = {"非飞肥费肺废匪吠沸菲诽啡篚蜚腓扉妃斐狒芾悱镄霏翡榧淝鲱绯痱怫砩"};
+static char PINYIN_mp_fen[] = {"分份芬粉坟奋愤纷忿粪酚焚吩氛汾棼瀵鲼玢偾鼢"};
+static char PINYIN_mp_feng[] = {"风封逢缝蜂丰枫疯冯奉讽凤峰锋烽砜俸酆葑沣唪"};
+static char PINYIN_mp_fo[] = {"佛"};
+static char PINYIN_mp_fou[] = {"否缶不"};
+static char PINYIN_mp_fu[] = {"副幅扶浮富福负伏付复服附俯芙斧赴缚拂夫父符孵敷赋辅府腐腹妇抚覆辐肤氟佛俘傅讣弗涪袱市甫釜脯腑阜咐黼砩苻趺跗蚨芾鲋幞宓茯滏蜉拊呒菔蝠鳆蝮绂绋赙罘稃匐麸凫桴莩孚馥驸怫祓呋郛艴黻阝"};
+static char PINYIN_mp_ga[] = {"噶胳夹嘎咖轧钆伽旮尬尕尜呷"};
+static char PINYIN_mp_gai[] = {"该改盖概钙芥溉戤垓丐陔赅"};
+static char PINYIN_mp_gan[] = {"赶干感敢竿甘肝柑杆赣秆旰酐矸疳泔苷擀绀橄澉淦尴坩乾"};
+static char PINYIN_mp_gang[] = {"刚钢纲港缸岗杠冈肛扛筻罡戆"};
+static char PINYIN_mp_gao[] = {"高搞告稿膏篙羔糕镐皋郜诰杲缟睾槔锆槁藁"};
+static char PINYIN_mp_ge[] = {"个各歌割哥搁格阁隔革咯胳葛蛤戈鸽疙盖屹合铬硌骼颌袼塥虼圪镉仡舸鬲嗝膈搿纥哿"};
+static char PINYIN_mp_gei[] = {"给"};
+static char PINYIN_mp_gen[] = {"跟根哏茛亘艮"};
+static char PINYIN_mp_geng[] = {"更耕颈梗耿庚羹埂赓鲠哽绠"};
+static char PINYIN_mp_gong[] = {"工公功共弓攻宫供恭拱贡躬巩汞龚红肱觥珙蚣"};
+static char PINYIN_mp_gou[] = {"够沟狗钩勾购构苟垢句岣彀枸鞲觏缑笱诟遘媾篝佝"};
+static char PINYIN_mp_gu[] = {"古股鼓谷故孤箍姑顾固雇估咕骨辜沽蛊贾菇梏鸪汩轱崮菰鹄鹘钴臌酤呱鲴诂牯瞽毂锢牿痼觚蛄罟嘏"};
+static char PINYIN_mp_gua[] = {"挂刮瓜寡剐褂卦呱胍鸹栝诖括"};
+static char PINYIN_mp_guai[] = {"怪拐乖掴"};
+static char PINYIN_mp_guan[] = {"关管官观馆惯罐灌冠贯棺纶盥矜莞掼涫鳏鹳倌"};
+static char PINYIN_mp_guang[] = {"光广逛桄犷咣胱潢"};
+static char PINYIN_mp_gui[] = {"归贵鬼跪轨规硅桂柜龟诡闺瑰圭刽傀癸炔庋宄桧刿鳜鲑皈匦妫晷簋炅匮"};
+static char PINYIN_mp_gun[] = {"滚棍辊鲧衮磙绲丨"};
+static char PINYIN_mp_guo[] = {"过国果裹锅郭涡埚椁聒馘猓崞掴帼呙虢蜾蝈"};
+static char PINYIN_mp_ha[] = {"哈蛤虾铪呵"};
+static char PINYIN_mp_hai[] = {"还海害咳氦孩骇骸亥嗨醢胲"};
+static char PINYIN_mp_han[] = {"喊含汗寒汉旱酣韩焊涵函憨翰罕撼捍憾悍邯邗菡撖瀚阚顸蚶焓颔晗鼾厂旰"};
+static char PINYIN_mp_hang[] = {"行巷航夯杭吭颃沆绗珩桁炕肮"};
+static char PINYIN_mp_hao[] = {"好号浩嚎壕郝毫豪耗貉镐昊颢灏嚆蚝嗥皓蒿濠薅"};
+static char PINYIN_mp_he[] = {"和喝合河禾核何呵荷贺赫褐盒鹤菏貉阂涸吓嗬劾盍翮阖颌壑诃纥曷鹄"};
+static char PINYIN_mp_hei[] = {"黑嘿嗨"};
+static char PINYIN_mp_hen[] = {"很狠恨痕哏"};
+static char PINYIN_mp_heng[] = {"横恒哼衡亨行桁珩蘅"};
+static char PINYIN_mp_hong[] = {"红轰哄虹洪宏烘鸿弘讧訇蕻闳薨黉荭泓"};
+static char PINYIN_mp_hou[] = {"后厚吼喉侯候猴鲎篌堠後逅糇骺瘊"};
+static char PINYIN_mp_hu[] = {"湖户呼虎壶互胡护糊弧忽狐蝴葫沪乎戏核和瑚唬鹕冱怙鹱笏戽扈鹘浒祜醐琥囫烀轷瓠煳斛鹄猢惚岵滹觳唿槲"};
+static char PINYIN_mp_hua[] = {"话花化画华划滑哗猾豁铧桦骅砉"};
+static char PINYIN_mp_huai[] = {"坏怀淮槐徊划踝"};
+static char PINYIN_mp_huan[] = {"换还唤环患缓欢幻宦涣焕豢桓痪漶獾擐逭鲩郇鬟寰奂锾圜洹萑缳浣垸"};
+static char PINYIN_mp_huang[] = {"黄慌晃荒簧凰皇谎惶蝗磺恍煌幌隍肓潢篁徨鳇遑癀湟蟥璜"};
+static char PINYIN_mp_hui[] = {"回会灰绘挥汇辉毁悔惠晦徽恢秽慧贿蛔讳徊卉烩诲彗浍珲蕙喙恚哕晖隳麾诙蟪茴洄咴虺荟缋堕桧"};
+static char PINYIN_mp_hun[] = {"混昏荤浑婚魂阍珲馄溷诨"};
+static char PINYIN_mp_huo[] = {"或活火伙货和获祸豁霍惑嚯镬耠劐藿攉锪蠖钬夥"};
+static char PINYIN_mp_ji[] = {"几及急既即机鸡积记级极计挤己季寄纪系基激吉脊际击圾畸箕肌饥迹讥姬绩棘辑籍缉集疾汲嫉蓟技冀伎祭剂悸济寂期其奇忌齐妓继给革稽墼洎鲚屐齑戟鲫嵇矶稷戢虮诘笈暨笄剞叽蒺跻嵴掎跽霁唧畿荠瘠玑羁丌偈芨佶赍楫髻咭蕺觊麂"};
+static char PINYIN_mp_jia[] = {"家加假价架甲佳夹嘉驾嫁枷荚颊钾稼茄贾铗葭迦戛浃镓痂恝岬跏嘏伽胛笳珈瘕郏袈蛱拮挟袷颉"};
+static char PINYIN_mp_jian[] = {"见件减尖间键贱肩兼建检箭煎简剪歼监坚奸健艰荐剑渐溅涧鉴浅践捡柬笺俭碱硷拣舰槛缄茧饯翦鞯戋谏牮枧腱趼缣搛戬毽菅鲣笕谫楗囝蹇裥踺睑謇鹣蒹僭锏湔堑犍"};
+static char PINYIN_mp_jiang[] = {"将讲江奖降浆僵姜酱蒋疆匠强桨虹豇礓缰犟耩绛茳糨洚"};
+static char PINYIN_mp_jiao[] = {"叫脚交角教较缴觉焦胶娇绞校搅骄狡浇矫郊嚼蕉轿窖椒礁饺铰酵侥剿徼艽僬蛟敫峤跤姣皎茭鹪噍醮佼湫鲛挢"};
+static char PINYIN_mp_jie[] = {"接节街借皆截解界结届姐揭戒介阶劫芥竭洁疥藉价楷秸桔杰捷诫睫家偈桀喈拮骱羯蚧嗟颉鲒婕碣讦孑疖诘卩"};
+static char PINYIN_mp_jin[] = {"进近今仅紧金斤尽劲禁浸锦晋筋津谨巾襟烬靳廑瑾馑槿衿堇荩矜噤缙卺妗赆觐"};
+static char PINYIN_mp_jing[] = {"竟静井惊经镜京净敬精景警竞境径荆晶鲸粳颈兢茎睛劲痉靖肼獍阱腈弪刭憬婧胫菁儆旌迳靓泾箐"};
+static char PINYIN_mp_jiong[] = {"窘炯扃迥炅冂"};
+static char PINYIN_mp_jiu[] = {"就九酒旧久揪救纠舅究韭厩臼玖灸咎疚赳鹫蹴僦柩桕鬏鸠阄啾"};
+static char PINYIN_mp_ju[] = {"句举巨局具距锯剧居聚拘菊矩沮拒惧鞠狙驹且据柜桔俱车咀疽踞炬倨醵裾屦犋苴窭飓锔椐苣琚掬榘龃趄莒雎遽橘踽榉鞫钜讵枸渠瞿"};
+static char PINYIN_mp_juan[] = {"卷圈倦鹃捐娟眷绢鄄锩蠲镌狷桊涓隽"};
+static char PINYIN_mp_jue[] = {"决绝觉角爵掘诀撅倔抉攫嚼脚桷噱橛嗟觖劂爝矍镢獗珏崛蕨噘谲蹶孓厥"};
+static char PINYIN_mp_jun[] = {"军君均菌俊峻龟竣骏钧浚郡筠麇皲捃匀"};
+static char PINYIN_mp_ka[] = {"卡喀咯咖胩咔佧"};
+static char PINYIN_mp_kai[] = {"开揩凯慨楷垲剀锎铠锴忾恺蒈劾"};
+static char PINYIN_mp_kan[] = {"看砍堪刊嵌坎槛勘龛戡侃瞰莰阚凵"};
+static char PINYIN_mp_kang[] = {"抗炕扛糠康慷亢钪闶伉"};
+static char PINYIN_mp_kao[] = {"靠考烤拷栲犒尻铐"};
+static char PINYIN_mp_ke[] = {"咳可克棵科颗刻课客壳渴苛柯磕坷呵恪岢蝌缂蚵轲窠钶氪颏瞌锞稞珂髁疴嗑溘骒铪"};
+static char PINYIN_mp_ken[] = {"肯啃恳垦裉龈"};
+static char PINYIN_mp_keng[] = {"坑吭铿"};
+static char PINYIN_mp_kong[] = {"空孔控恐倥崆箜"};
+static char PINYIN_mp_kou[] = {"口扣抠寇蔻芤眍筘叩佝"};
+static char PINYIN_mp_ku[] = {"哭库苦枯裤窟酷刳骷喾堀绔"};
+static char PINYIN_mp_kua[] = {"跨垮挎夸胯侉"};
+static char PINYIN_mp_kuai[] = {"快块筷会侩哙蒯浍郐狯脍"};
+static char PINYIN_mp_kuan[] = {"宽款髋"};
+static char PINYIN_mp_kuang[] = {"矿筐狂框况旷匡眶诳邝纩夼诓圹贶哐磺湟"};
+static char PINYIN_mp_kui[] = {"亏愧奎窥溃葵魁馈盔傀岿匮愦揆睽跬聩篑喹逵暌蒉悝喟馗蝰隗夔"};
+static char PINYIN_mp_kun[] = {"捆困昆坤鲲锟髡琨醌阃悃"};
+static char PINYIN_mp_kuo[] = {"阔扩括廓适蛞栝"};
+static char PINYIN_mp_la[] = {"拉啦辣蜡腊喇垃蓝落瘌邋砬剌旯"};
+static char PINYIN_mp_lai[] = {"来赖莱濑赉崃涞铼籁徕睐癞"};
+static char PINYIN_mp_lan[] = {"蓝兰烂拦篮懒栏揽缆滥阑谰婪澜览榄岚褴镧斓罱漤啉"};
+static char PINYIN_mp_lang[] = {"浪狼廊郎朗榔琅稂螂莨啷锒阆蒗"};
+static char PINYIN_mp_lao[] = {"老捞牢劳烙涝落姥酪络佬潦耢铹醪铑唠栳崂痨"};
+static char PINYIN_mp_le[] = {"了乐勒肋鳓仂叻泐嘞"};
+static char PINYIN_mp_lei[] = {"类累泪雷垒勒擂蕾肋镭儡磊缧诔耒酹羸嫘檑嘞漯"};
+static char PINYIN_mp_leng[] = {"冷棱楞愣塄"};
+static char PINYIN_mp_li[] = {"里离力立李例哩理利梨厘礼历丽吏砾漓莉傈荔俐痢狸粒沥隶栗璃鲤厉励犁黎篱郦鹂笠坜苈鳢缡跞蜊锂悝澧粝蓠枥蠡鬲呖砺嫠篥疠疬猁藜溧鲡戾栎唳醴轹詈骊罹逦俪喱雳黧莅俚蛎娌"};
+static char PINYIN_mp_lia[] = {"俩"};
+static char PINYIN_mp_lian[] = {"连联练莲恋脸炼链敛怜廉帘镰涟蠊琏殓蔹鲢奁潋臁裢濂裣楝"};
+static char PINYIN_mp_liang[] = {"两亮辆凉粮梁量良晾谅俩粱墚靓踉椋魉莨"};
+static char PINYIN_mp_liao[] = {"了料撩聊撂疗廖燎辽僚寥镣潦钌蓼尥寮缭獠鹩嘹"};
+static char PINYIN_mp_lie[] = {"列裂猎劣烈咧埒捩鬣趔躐冽洌"};
+static char PINYIN_mp_lin[] = {"林临淋邻磷鳞赁吝拎琳霖凛遴嶙蔺粼麟躏辚廪懔瞵檩膦啉"};
+static char PINYIN_mp_ling[] = {"另令领零铃玲灵岭龄凌陵菱伶羚棱翎蛉苓绫瓴酃呤泠棂柃鲮聆囹"};
+static char PINYIN_mp_liu[] = {"六流留刘柳溜硫瘤榴琉馏碌陆绺锍鎏镏浏骝旒鹨熘遛"};
+static char PINYIN_mp_lo[] = {"咯"};
+static char PINYIN_mp_long[] = {"龙拢笼聋隆垄弄咙窿陇垅胧珑茏泷栊癃砻"};
+static char PINYIN_mp_lou[] = {"楼搂漏陋露娄篓偻蝼镂蒌耧髅喽瘘嵝"};
+static char PINYIN_mp_lu[] = {"路露录鹿陆炉卢鲁卤芦颅庐碌掳绿虏赂戮潞禄麓六鲈栌渌蓼逯泸轳氇簏橹辂垆胪噜镥辘漉撸璐鸬鹭舻"};
+static char PINYIN_mp_luan[] = {"乱卵滦峦孪挛栾銮脔娈鸾"};
+static char PINYIN_mp_lun[] = {"论轮抡伦沦仑纶囵"};
+static char PINYIN_mp_luo[] = {"落罗锣裸骡烙箩螺萝洛骆逻络咯荦漯蠃雒倮硌椤捋脶瘰摞泺珞镙猡跞"};
+static char PINYIN_mp_lv[] = {"绿率铝驴旅屡滤吕律氯缕侣虑履偻膂榈闾捋褛稆"};
+static char PINYIN_mp_lue[] = {"略掠锊"};
+static char PINYIN_mp_ma[] = {"吗妈马嘛麻骂抹码玛蚂摩唛蟆犸嬷杩"};
+static char PINYIN_mp_mai[] = {"买卖迈埋麦脉劢霾荬"};
+static char PINYIN_mp_man[] = {"满慢瞒漫蛮蔓曼馒埋谩幔鳗墁螨镘颟鞔缦熳"};
+static char PINYIN_mp_mang[] = {"忙芒盲莽茫氓硭邙蟒漭"};
+static char PINYIN_mp_mao[] = {"毛冒帽猫矛卯貌茂贸铆锚茅耄茆瑁蝥髦懋昴牦瞀峁袤蟊旄泖"};
+static char PINYIN_mp_me[] = {"么麽"};
+static char PINYIN_mp_mei[] = {"没每煤镁美酶妹枚霉玫眉梅寐昧媒糜媚谜沫嵋猸袂湄浼鹛莓魅镅楣"};
+static char PINYIN_mp_men[] = {"门们闷懑扪钔焖汶鞔"};
+static char PINYIN_mp_meng[] = {"猛梦蒙锰孟盟檬萌氓礞蜢勐懵甍蠓虻朦艋艨瞢"};
+static char PINYIN_mp_mi[] = {"米密迷眯蜜谜觅秘弥幂靡糜泌醚蘼縻咪汨麋祢猕弭谧芈脒宓敉嘧糸"};
+static char PINYIN_mp_mian[] = {"面棉免绵眠缅勉冕娩腼湎眄沔黾渑宀"};
+static char PINYIN_mp_miao[] = {"秒苗庙妙描瞄藐渺眇缪缈邈淼喵杪鹋"};
+static char PINYIN_mp_mie[] = {"灭蔑咩篾蠛乜"};
+static char PINYIN_mp_min[] = {"民抿敏闽皿悯珉愍缗闵玟苠泯黾鳘岷"};
+static char PINYIN_mp_ming[] = {"名明命鸣铭螟盟冥瞑暝茗溟酩"};
+static char PINYIN_mp_miu[] = {"谬缪"};
+static char PINYIN_mp_mo[] = {"摸磨抹末膜墨没莫默魔模摩摹漠陌蘑脉沫万无冒寞秣瘼殁镆嫫谟蓦貊貘麽茉馍耱么嘿嬷"};
+static char PINYIN_mp_mou[] = {"某谋牟眸蛑鍪侔缪哞厶"};
+static char PINYIN_mp_mu[] = {"木母亩幕目墓牧牟模穆暮牡拇募慕睦姆姥钼毪坶沐仫苜"};
+static char PINYIN_mp_na[] = {"那拿哪纳钠娜呐南衲捺镎肭内"};
+static char PINYIN_mp_nai[] = {"乃耐奶奈氖哪萘艿柰鼐佴能"};
+static char PINYIN_mp_nan[] = {"难南男赧囡蝻楠喃腩囝"};
+static char PINYIN_mp_nang[] = {"囊馕曩囔攮"};
+static char PINYIN_mp_nao[] = {"闹脑恼挠淖孬铙瑙垴呶蛲猱硇"};
+static char PINYIN_mp_ne[] = {"呢哪那呐讷疒"};
+static char PINYIN_mp_nei[] = {"内哪馁那"};
+static char PINYIN_mp_nen[] = {"嫩恁"};
+static char PINYIN_mp_neng[] = {"能"};
+static char PINYIN_mp_ng[] = {"嗯"};
+static char PINYIN_mp_ni[] = {"你泥拟腻逆呢溺倪尼匿妮霓铌昵坭祢猊伲怩鲵睨旎"};
+static char PINYIN_mp_nian[] = {"年念捻撵拈碾蔫粘廿黏辇鲇鲶埝辗"};
+static char PINYIN_mp_niang[] = {"娘酿"};
+static char PINYIN_mp_niao[] = {"鸟尿袅茑脲嬲溺"};
+static char PINYIN_mp_nie[] = {"捏镍聂孽涅镊啮陧蘖嗫臬蹑颞乜"};
+static char PINYIN_mp_nin[] = {"您恁"};
+static char PINYIN_mp_ning[] = {"拧凝宁柠狞泞佞甯咛聍"};
+static char PINYIN_mp_niu[] = {"牛扭纽钮狃忸妞拗"};
+static char PINYIN_mp_nong[] = {"弄浓农脓哝侬"};
+static char PINYIN_mp_nou[] = {"耨"};
+static char PINYIN_mp_nu[] = {"怒努奴孥胬驽弩呶帑"};
+static char PINYIN_mp_nv[] = {"女钕恧衄"};
+static char PINYIN_mp_nue[] = {"虐疟谑"};
+static char PINYIN_mp_nuan[] = {"暖"};
+static char PINYIN_mp_nuo[] = {"挪诺懦糯娜喏傩锘搦"};
+static char PINYIN_mp_o[] = {"哦喔噢"};
+static char PINYIN_mp_ou[] = {"偶呕欧藕鸥区沤殴怄瓯讴耦"};
+static char PINYIN_mp_pa[] = {"怕爬趴啪耙扒帕琶派筢杷葩钯"};
+static char PINYIN_mp_pai[] = {"派排拍牌迫徘湃哌俳蒎"};
+static char PINYIN_mp_pan[] = {"盘盼判攀畔潘叛磐番般胖襻蟠袢泮拚爿蹒"};
+static char PINYIN_mp_pang[] = {"旁胖耪庞乓膀磅滂彷逄螃仿"};
+static char PINYIN_mp_pao[] = {"跑抛炮泡刨袍咆狍匏庖疱脬"};
+static char PINYIN_mp_pei[] = {"陪配赔呸胚佩培沛裴旆锫帔醅霈辔淠"};
+static char PINYIN_mp_pen[] = {"喷盆湓"};
+static char PINYIN_mp_peng[] = {"碰捧棚砰蓬朋彭鹏烹硼膨抨澎篷怦堋蟛嘭"};
+static char PINYIN_mp_pi[] = {"批皮披匹劈辟坯屁脾僻疲痞霹琵毗啤譬砒否貔丕圮媲癖仳擗郫甓枇睥蜱鼙邳吡陂铍庀罴埤纰陴淠噼蚍裨芘"};
+static char PINYIN_mp_pian[] = {"片篇骗偏便扁翩缏犏骈胼蹁谝"};
+static char PINYIN_mp_piao[] = {"票飘漂瓢朴螵莩嫖瞟殍缥嘌骠剽"};
+static char PINYIN_mp_pie[] = {"瞥撇苤氕"};
+static char PINYIN_mp_pin[] = {"品贫聘拼频嫔榀姘牝颦"};
+static char PINYIN_mp_ping[] = {"平凭瓶评屏乒萍苹坪冯娉鲆枰俜"};
+static char PINYIN_mp_po[] = {"破坡颇婆泼迫泊魄朴繁粕笸皤钋陂鄱攴叵珀钷泺"};
+static char PINYIN_mp_pou[] = {"剖掊裒"};
+static char PINYIN_mp_pu[] = {"扑铺谱脯仆蒲葡朴菩莆瀑曝埔圃浦堡普暴镨噗匍溥濮氆蹼璞镤攴攵"};
+static char PINYIN_mp_qi[] = {"起其七气期齐器妻骑汽棋奇欺漆启戚柒岂砌弃泣祁凄企乞契歧祈栖畦脐崎稽迄缉沏讫旗祺颀骐屺岐蹊萁蕲桤憩芪荠萋芑汔亟鳍俟槭嘁蛴綦亓欹琪麒琦蜞圻杞葺碛淇祗耆绮丌伎綮"};
+static char PINYIN_mp_qia[] = {"恰卡掐洽袷葜髂"};
+static char PINYIN_mp_qian[] = {"前钱千牵浅签欠铅嵌钎迁钳乾谴谦潜歉纤扦遣黔堑仟岍钤褰箝掮搴倩慊悭愆虔芡荨缱佥芊阡肷茜椠犍骞凵"};
+static char PINYIN_mp_qiang[] = {"强枪墙抢腔呛羌蔷将蜣跄戗襁戕炝镪锖锵羟樯嫱"};
+static char PINYIN_mp_qiao[] = {"桥瞧敲巧翘锹壳鞘撬悄俏窍雀乔侨峭橇樵荞跷硗憔谯鞒愀缲诮劁峤"};
+static char PINYIN_mp_qie[] = {"切且怯窃茄砌郄趄惬锲妾箧慊伽挈"};
+static char PINYIN_mp_qin[] = {"亲琴侵勤擒寝秦芹沁禽钦吣覃矜衾芩溱廑嗪螓噙揿檎锓"};
+static char PINYIN_mp_qing[] = {"请轻清青情晴氢倾庆擎顷亲卿氰圊謦檠箐苘蜻黥罄鲭磬綮倩"};
+static char PINYIN_mp_qiong[] = {"穷琼跫穹邛蛩茕銎筇"};
+static char PINYIN_mp_qiu[] = {"求球秋丘泅仇邱囚酋龟楸蚯裘糗蝤巯逑俅虬赇鳅犰湫鼽遒艽"};
+static char PINYIN_mp_qu[] = {"去取区娶渠曲趋趣屈驱蛆躯龋戌蠼蘧祛蕖磲劬诎鸲阒麴癯衢黢璩氍觑蛐朐瞿岖苣"};
+static char PINYIN_mp_quan[] = {"全权劝圈拳犬泉券颧痊醛铨筌绻诠辁畎鬈悛蜷荃犭"};
+static char PINYIN_mp_que[] = {"却缺确雀瘸鹊炔榷阙阕悫芍"};
+static char PINYIN_mp_qui[] = {"鼽"};
+static char PINYIN_mp_qun[] = {"群裙逡麇"};
+static char PINYIN_mp_ran[] = {"染燃然冉髯苒蚺"};
+static char PINYIN_mp_rang[] = {"让嚷瓤攘壤穰禳"};
+static char PINYIN_mp_rao[] = {"饶绕扰荛桡娆"};
+static char PINYIN_mp_re[] = {"热若惹喏"};
+static char PINYIN_mp_ren[] = {"人任忍认刃仁韧妊纫壬饪轫仞荏葚衽稔亻"};
+static char PINYIN_mp_reng[] = {"仍扔艿"};
+static char PINYIN_mp_ri[] = {"日"};
+static char PINYIN_mp_rong[] = {"容绒融溶熔荣戎蓉冗茸榕狨嵘肜蝾"};
+static char PINYIN_mp_rou[] = {"肉揉柔糅蹂鞣"};
+static char PINYIN_mp_ru[] = {"如入汝儒茹乳褥辱蠕孺蓐襦铷嚅缛濡薷颥溽洳"};
+static char PINYIN_mp_ruan[] = {"软阮朊"};
+static char PINYIN_mp_rui[] = {"瑞蕊锐睿芮蚋枘蕤"};
+static char PINYIN_mp_run[] = {"闰润"};
+static char PINYIN_mp_ruo[] = {"若弱偌箬"};
+static char PINYIN_mp_sa[] = {"撒洒萨挲仨卅飒脎"};
+static char PINYIN_mp_sai[] = {"塞腮鳃思赛噻"};
+static char PINYIN_mp_san[] = {"三散伞叁馓糁毵霰参"};
+static char PINYIN_mp_sang[] = {"桑丧嗓颡磉搡"};
+static char PINYIN_mp_sao[] = {"扫嫂搔骚梢埽鳋臊缫瘙缲"};
+static char PINYIN_mp_se[] = {"色涩瑟塞啬铯穑槭"};
+static char PINYIN_mp_sen[] = {"森"};
+static char PINYIN_mp_seng[] = {"僧"};
+static char PINYIN_mp_sha[] = {"杀沙啥纱傻砂刹莎厦煞杉嗄唼鲨霎铩痧裟挲歃噎"};
+static char PINYIN_mp_shai[] = {"晒筛色酾"};
+static char PINYIN_mp_shan[] = {"山闪衫善扇杉删煽单珊掺赡栅苫掸膳陕汕擅缮嬗蟮芟禅跚鄯潸鳝姗剡骟疝膻讪钐舢埏彡"};
+static char PINYIN_mp_shang[] = {"上伤尚商赏晌墒汤裳熵觞绱殇垧"};
+static char PINYIN_mp_shao[] = {"少烧捎哨勺梢稍邵韶绍芍召鞘苕劭潲艄蛸筲杓"};
+static char PINYIN_mp_she[] = {"社射蛇设舌摄舍折涉赊赦慑奢歙厍畲猞麝滠佘拾揲"};
+static char PINYIN_mp_shei[] = {"谁"};
+static char PINYIN_mp_shen[] = {"身伸深婶神甚渗肾审申沈绅呻参砷什娠慎葚糁莘诜谂矧椹渖蜃哂胂吲"};
+static char PINYIN_mp_sheng[] = {"声省剩生升绳胜盛圣甥牲乘晟渑眚笙嵊"};
+static char PINYIN_mp_shi[] = {"是使十时事室市石师试史式识虱矢拾屎驶始似嘘示士世柿匙拭誓逝势什殖峙嗜噬失适仕侍释饰氏狮食恃蚀视实施湿诗尸豕莳埘铈舐鲥鲺贳轼蓍筮炻谥弑酾螫礻耆饣"};
+static char PINYIN_mp_shou[] = {"手受收首守瘦授兽售熟寿艏狩绶扌"};
+static char PINYIN_mp_shu[] = {"书树数熟输梳叔属束术述蜀黍鼠淑赎孰蔬疏戍竖墅庶薯漱恕枢暑殊抒曙署舒姝摅秫纾沭毹腧塾菽殳澍倏疋荼"};
+static char PINYIN_mp_shua[] = {"刷耍唰"};
+static char PINYIN_mp_shuai[] = {"摔甩率帅衰蟀"};
+static char PINYIN_mp_shuan[] = {"栓拴闩涮"};
+static char PINYIN_mp_shuang[] = {"双霜爽泷孀"};
+static char PINYIN_mp_shui[] = {"水谁睡税说氵"};
+static char PINYIN_mp_shun[] = {"顺吮瞬舜"};
+static char PINYIN_mp_shuo[] = {"说数硕烁朔搠妁槊蒴铄"};
+static char PINYIN_mp_si[] = {"四司丝撕似私嘶思寺斯食伺死厕肆饲嗣巳耜驷兕蛳厮汜锶泗笥咝鸶姒厶缌祀澌俟糸纟"};
+static char PINYIN_mp_song[] = {"送松耸宋颂诵怂讼竦菘淞悚嵩凇崧忪"};
+static char PINYIN_mp_sou[] = {"艘搜擞嗽嗾嗖飕叟薮锼馊瞍溲螋"};
+static char PINYIN_mp_su[] = {"素速诉塑宿俗苏肃粟酥缩溯僳愫簌觫稣夙嗉谡蔌涑"};
+static char PINYIN_mp_suan[] = {"酸算蒜狻"};
+static char PINYIN_mp_sui[] = {"岁随碎虽穗遂尿隋髓绥隧祟眭谇濉邃燧荽睢粹"};
+static char PINYIN_mp_sun[] = {"孙损笋榫荪飧狲隼"};
+static char PINYIN_mp_suo[] = {"所缩锁琐索梭蓑莎唆挲睃嗍唢桫嗦娑羧"};
+static char PINYIN_mp_ta[] = {"他她它踏塔塌拓獭挞蹋溻趿鳎沓榻漯遢铊闼嗒"};
+static char PINYIN_mp_tai[] = {"太抬台态胎苔泰酞汰炱肽跆呔鲐钛薹邰骀"};
+static char PINYIN_mp_tan[] = {"谈叹探滩弹碳摊潭贪坛痰毯坦炭瘫谭坍檀袒钽郯镡锬覃澹昙忐"};
+static char PINYIN_mp_tang[] = {"躺趟堂糖汤塘烫倘淌唐搪棠膛螳樘羰醣瑭镗傥饧溏耥帑铴螗"};
+static char PINYIN_mp_tao[] = {"套掏逃桃讨淘涛滔陶绦萄鼗啕洮韬饕叨焘"};
+static char PINYIN_mp_te[] = {"特忑慝铽忒"};
+static char PINYIN_mp_teng[] = {"疼腾藤誊滕"};
+static char PINYIN_mp_ti[] = {"提替体题踢蹄剃剔梯锑啼涕嚏惕屉醍鹈绨缇倜裼逖荑悌棣"};
+static char PINYIN_mp_tian[] = {"天田添填甜舔恬腆佃掭钿阗忝殄畋栝"};
+static char PINYIN_mp_tiao[] = {"条跳挑调迢眺龆笤祧蜩髫佻窕鲦苕粜啁"};
+static char PINYIN_mp_tie[] = {"铁贴帖萜餮"};
+static char PINYIN_mp_ting[] = {"听停挺厅亭艇庭廷烃汀町莛铤葶婷蜓梃霆"};
+static char PINYIN_mp_tong[] = {"同通痛铜桶筒捅统童彤桐瞳恫侗酮潼茼仝砼峒恸佟嗵垌僮"};
+static char PINYIN_mp_tou[] = {"头偷透投钭骰亠愉"};
+static char PINYIN_mp_tu[] = {"土图兔涂吐秃突徒凸途屠酴荼钍菟堍"};
+static char PINYIN_mp_tuan[] = {"团湍疃抟彖"};
+static char PINYIN_mp_tui[] = {"腿推退褪颓蜕煺忒弟"};
+static char PINYIN_mp_tun[] = {"吞屯褪臀囤氽饨豚暾"};
+static char PINYIN_mp_tuo[] = {"拖脱托妥驮拓驼椭唾鸵陀魄橐柝跎乇坨佗庹酡柁鼍沱箨砣铊隋舵"};
+static char PINYIN_mp_wa[] = {"挖瓦蛙哇娃洼凹袜佤娲腽"};
+static char PINYIN_mp_wai[] = {"外歪崴呙"};
+static char PINYIN_mp_wan[] = {"完万晚碗玩弯挽湾丸腕宛婉烷顽豌惋娩皖蔓莞脘蜿绾芄琬纨剜畹菀箢"};
+static char PINYIN_mp_wang[] = {"望忘王往网亡枉旺汪妄芒辋魍惘罔尢"};
+static char PINYIN_mp_wei[] = {"为位未围喂胃微味尾伪威伟卫危违委魏唯维畏惟韦巍蔚谓尉潍纬慰桅萎苇渭遗葳帏艉鲔娓逶闱隈沩玮涠帷崴隗诿洧偎猥猬嵬軎韪炜煨圩薇痿倭隹"};
+static char PINYIN_mp_wen[] = {"问文闻稳温吻蚊纹瘟紊汶阌刎雯璺韫"};
+static char PINYIN_mp_weng[] = {"翁嗡瓮蕹蓊"};
+static char PINYIN_mp_wo[] = {"我握窝卧挝沃蜗涡斡倭幄龌肟莴喔渥硪媪"};
+static char PINYIN_mp_wu[] = {"无五屋物舞雾误捂污悟勿钨武戊务呜伍吴午吾侮乌毋恶诬芜巫晤梧坞妩蜈牾寤兀怃阢邬忤骛於鋈仵杌鹜婺迕痦芴焐庑鹉鼯浯圬亡唔"};
+static char PINYIN_mp_xi[] = {"西洗细吸戏系喜席稀溪熄锡膝息袭惜习嘻夕悉矽熙希檄牺晰昔媳硒铣烯腊析隙栖汐犀蜥奚浠葸饩屣玺嬉禊兮翕穸禧僖淅蓰舾蹊醯欷皙蟋羲茜徙隰唏曦螅歙樨阋粞熹觋菥鼷裼舄郄褶鳃郗"};
+static char PINYIN_mp_xia[] = {"下吓夏峡虾瞎霞狭匣侠辖厦暇唬狎柙呷黠硖罅遐瑕假"};
+static char PINYIN_mp_xian[] = {"先线县现显掀闲献嫌陷险鲜弦衔馅限咸锨仙腺贤纤宪舷涎羡铣见苋藓岘痫莶籼娴蚬猃祆冼燹跣跹酰暹氙鹇筅霰彡"};
+static char PINYIN_mp_xiang[] = {"想向象项响香乡相像箱巷享镶厢降翔祥橡详湘襄飨鲞骧蟓庠芗饷缃葙"};
+static char PINYIN_mp_xiao[] = {"小笑消削销萧效宵晓肖孝硝淆啸霄哮嚣校魈蛸骁枵哓崤筱潇逍枭绡箫俏姣"};
+static char PINYIN_mp_xie[] = {"写些鞋歇斜血谢卸挟屑蟹泻懈泄楔邪协械谐蝎携胁解契叶绁颉缬獬榭廨撷偕瀣渫亵榍邂薤躞燮勰鲑"};
+static char PINYIN_mp_xin[] = {"新心欣信芯薪锌辛寻衅忻歆囟莘镡馨鑫昕忄"};
+static char PINYIN_mp_xing[] = {"性行型形星醒姓腥刑杏兴幸邢猩惺省硎悻荥陉擤荇饧"};
+static char PINYIN_mp_xiong[] = {"胸雄凶兄熊汹匈芎"};
+static char PINYIN_mp_xiu[] = {"修锈绣休羞宿嗅袖秀朽臭溴貅馐髹鸺咻庥岫"};
+static char PINYIN_mp_xu[] = {"许须需虚嘘蓄续序叙畜絮婿戌徐旭绪吁酗恤墟糈勖栩浒蓿顼圩洫胥醑诩溆煦盱砉"};
+static char PINYIN_mp_xuan[] = {"选悬旋玄宣喧轩绚眩癣券暄楦儇渲漩泫铉璇煊碹镟炫揎萱痃谖"};
+static char PINYIN_mp_xue[] = {"学雪血靴穴削薛踅噱鳕泶谑彐"};
+static char PINYIN_mp_xun[] = {"寻讯熏训循殉旬巡迅驯汛逊勋荤询浚巽鲟浔埙恂獯醺洵郇峋蕈薰荀窨曛徇孙荨"};
+static char PINYIN_mp_ya[] = {"呀压牙押芽鸭轧哑亚涯丫雅衙鸦讶蚜垭疋砑琊桠睚娅痖岈氩伢迓揠崖厌吖"};
+static char PINYIN_mp_yan[] = {"眼烟沿盐言演严咽淹炎掩厌宴岩研延堰验艳殷阉砚雁唁彦焰蜒衍谚燕颜阎铅焉奄芫厣阏菸魇琰滟焱赝筵腌兖剡餍恹罨檐湮偃谳胭晏闫俨郾酽鄢妍鼹崦阽嫣埏讠"};
+static char PINYIN_mp_yang[] = {"样养羊洋仰扬秧氧痒杨漾阳殃央鸯佯疡炀恙徉鞅泱蛘烊怏"};
+static char PINYIN_mp_yao[] = {"要摇药咬腰窑舀邀妖谣遥姚瑶耀尧钥侥陶约疟珧夭鳐鹞轺爻吆铫幺崾肴曜徭杳窈么繇"};
+static char PINYIN_mp_ye[] = {"也夜业野叶爷页液掖腋冶噎耶咽曳椰邪谒邺晔烨揶铘靥揲"};
+static char PINYIN_mp_yi[] = {"一以已亿衣移依易医乙仪亦椅益倚姨翼译伊蛇遗食艾胰疑沂宜异彝壹蚁谊揖铱矣翌艺抑绎邑蛾屹尾役臆逸肄疫颐裔意毅忆义夷溢诣议怿痍镒癔怡驿旖熠酏翊欹峄圯殪嗌咦懿噫劓诒饴漪佚咿瘗猗眙羿弈苡荑仡佾贻钇缢迤刈悒黟翳弋奕蜴埸挹嶷薏呓轶镱舣衤"};
+static char PINYIN_mp_yin[] = {"因引印银音饮阴隐荫吟尹寅茵淫殷姻烟堙鄞喑夤胤龈吲圻狺垠霪蚓氤铟窨瘾洇茚廴湮"};
+static char PINYIN_mp_ying[] = {"应硬影营迎映蝇赢鹰英颖莹盈婴樱缨荧萤萦楹蓥瘿茔鹦媵莺璎郢嘤撄瑛滢潆嬴罂瀛膺荥颍景"};
+static char PINYIN_mp_yo[] = {"哟育唷"};
+static char PINYIN_mp_yong[] = {"用涌永拥蛹勇雍咏泳佣踊痈庸臃恿壅慵俑墉鳙邕喁甬饔镛"};
+static char PINYIN_mp_you[] = {"有又由右油游幼优友铀忧尤犹诱悠邮酉佑釉幽疣攸蚰莠鱿卣黝莸猷蚴宥牖囿柚蝣莜鼬铕蝤繇呦侑尢"};
+static char PINYIN_mp_yu[] = {"与于欲鱼雨余遇语愈狱玉渔予誉育愚羽虞娱淤舆屿禹宇迂俞逾域芋郁谷吁盂喻峪御愉粥渝尉榆隅浴寓裕预豫驭蔚妪嵛雩馀阈窬鹆妤揄窳觎臾舁龉蓣煜钰谀纡於竽瑜禺聿欤俣伛圄鹬庾昱萸瘐谕鬻圉瘀熨饫毓燠腴狳菀蜮蝓吾喁菸肀"};
+static char PINYIN_mp_yuan[] = {"远员元院圆原愿园援猿怨冤源缘袁渊苑垣鸳辕圜鼋橼媛爰眢鸢掾芫沅瑗螈箢塬垸"};
+static char PINYIN_mp_yue[] = {"月越约跃阅乐岳悦曰说粤钥瀹钺刖龠栎樾哕"};
+static char PINYIN_mp_yun[] = {"云运晕允匀韵陨孕耘蕴酝郧员氲恽愠郓芸筠韫昀狁殒纭熨媪菀"};
+static char PINYIN_mp_za[] = {"杂砸咋匝扎咱咂拶"};
+static char PINYIN_mp_zai[] = {"在再灾载栽宰哉崽甾仔"};
+static char PINYIN_mp_zan[] = {"咱暂攒赞簪趱糌瓒拶昝錾"};
+static char PINYIN_mp_zang[] = {"脏葬赃藏臧驵奘"};
+static char PINYIN_mp_zao[] = {"早造遭糟灶燥枣凿躁藻皂噪澡蚤唣"};
+static char PINYIN_mp_ze[] = {"则责择泽咋侧箦舴帻迮啧仄昃笮赜"};
+static char PINYIN_mp_zei[] = {"贼"};
+static char PINYIN_mp_zen[] = {"怎谮"};
+static char PINYIN_mp_zeng[] = {"增赠憎曾综缯罾甑锃"};
+static char PINYIN_mp_zha[] = {"扎炸渣闸眨榨乍轧诈喳札铡揸吒咤哳猹砟痄蚱齄查蜡栅咋喋楂柞"};
+static char PINYIN_mp_zhai[] = {"摘窄债斋寨择翟宅侧祭砦瘵"};
+static char PINYIN_mp_zhan[] = {"站占战盏沾粘毡展栈詹颤蘸湛绽斩辗崭瞻谵搌旃"};
+static char PINYIN_mp_zhang[] = {"张章长帐仗丈掌涨账樟杖彰漳胀瘴障仉嫜幛鄣璋嶂獐蟑"};
+static char PINYIN_mp_zhao[] = {"找着照招罩爪兆朝昭沼肇嘲召赵棹啁钊笊诏濯"};
+static char PINYIN_mp_zhe[] = {"着这者折遮蛰哲蔗锗辙浙柘辄赭摺鹧磔褶蜇谪乇"};
+static char PINYIN_mp_zhen[] = {"真阵镇针震枕振斟珍疹诊甄砧臻贞侦缜蓁祯箴轸榛稹赈朕鸩胗浈桢畛圳椹溱"};
+static char PINYIN_mp_zheng[] = {"正整睁争挣征怔证症郑拯丁蒸狰政峥钲铮筝诤徵鲭帧"};
+static char PINYIN_mp_zhi[] = {"只之直知制指纸支芝枝稚吱蜘质肢脂汁炙织职痔植抵殖执值侄址滞止趾治旨窒志挚掷至致置帜识峙氏智秩帙摭黹桎枳轵忮祉蛭膣觯郅栀彘芷祗咫鸷絷踬胝骘轾痣陟踯雉埴贽卮酯豸跖栉郦夂"};
+static char PINYIN_mp_zhong[] = {"中重种钟肿众终盅忠仲衷踵舯螽锺冢忪夂"};
+static char PINYIN_mp_zhou[] = {"周洲皱粥州轴舟昼骤宙诌肘帚咒繇胄纣荮啁碡绉籀妯酎"};
+static char PINYIN_mp_zhu[] = {"住主猪竹株煮筑贮铸嘱拄注祝驻属术珠瞩蛛朱柱诸诛逐助烛蛀潴洙伫瘃翥茱苎橥舳杼箸炷侏铢疰渚褚躅麈邾槠竺澍丶"};
+static char PINYIN_mp_zhua[] = {"抓爪挝"};
+static char PINYIN_mp_zhuai[] = {"拽转"};
+static char PINYIN_mp_zhuan[] = {"转专砖赚传撰篆颛馔啭沌"};
+static char PINYIN_mp_zhuang[] = {"装撞庄壮桩状幢妆僮奘戆"};
+static char PINYIN_mp_zhui[] = {"追坠缀锥赘椎骓惴缒隹揣"};
+static char PINYIN_mp_zhun[] = {"准谆屯肫窀淳"};
+static char PINYIN_mp_zhuo[] = {"捉桌着啄拙灼浊卓琢缴茁酌擢焯濯诼浞涿倬镯禚斫棹淖躅"};
+static char PINYIN_mp_zi[] = {"字自子紫籽资姿吱滓仔兹咨孜渍滋淄笫粢龇秭恣谘趑缁梓鲻锱孳耔觜髭赀茈訾嵫眦姊辎甾"};
+static char PINYIN_mp_zong[] = {"总纵宗棕综踪鬃偬粽枞腙"};
+static char PINYIN_mp_zou[] = {"走揍奏邹鲰鄹陬驺诹楱"};
+static char PINYIN_mp_zu[] = {"组族足阻租祖诅菹镞卒俎"};
+static char PINYIN_mp_zuan[] = {"钻纂赚攥缵躜"};
+static char PINYIN_mp_zui[] = {"最嘴醉罪堆咀觜蕞羧"};
+static char PINYIN_mp_zun[] = {"尊遵鳟撙樽奠"};
+static char PINYIN_mp_zuo[] = {"做作坐左座昨凿琢撮佐笮酢唑祚胙怍阼柞嘬"};
+#endif
+
+struct PINYIN_initials PINYIN_SUMMARY[] = {{"a", PINYIN_mp_a},
+    {"ai", PINYIN_mp_ai},
+    {"an", PINYIN_mp_an},
+    {"ang", PINYIN_mp_ang},
+    {"ao", PINYIN_mp_ao},
+    {"ba", PINYIN_mp_ba},
+    {"bai", PINYIN_mp_bai},
+    {"ban", PINYIN_mp_ban},
+    {"bang", PINYIN_mp_bang},
+    {"bao", PINYIN_mp_bao},
+    {"bei", PINYIN_mp_bei},
+    {"ben", PINYIN_mp_ben},
+    {"beng", PINYIN_mp_beng},
+    {"bi", PINYIN_mp_bi},
+    {"bian", PINYIN_mp_bian},
+    {"biao", PINYIN_mp_biao},
+    {"bie", PINYIN_mp_bie},
+    {"bin", PINYIN_mp_bin},
+    {"bing", PINYIN_mp_bing},
+    {"bo", PINYIN_mp_bo},
+    {"bu", PINYIN_mp_bu},
+    {"ca", PINYIN_mp_ca},
+    {"cai", PINYIN_mp_cai},
+    {"can", PINYIN_mp_can},
+    {"cang", PINYIN_mp_cang},
+    {"cao", PINYIN_mp_cao},
+    {"ce", PINYIN_mp_ce},
+    {"cen", PINYIN_mp_cen},
+    {"ceng", PINYIN_mp_ceng},
+    {"cha", PINYIN_mp_cha},
+    {"chai", PINYIN_mp_chai},
+    {"chan", PINYIN_mp_chan},
+    {"chang", PINYIN_mp_chang},
+    {"chao", PINYIN_mp_chao},
+    {"che", PINYIN_mp_che},
+    {"chen", PINYIN_mp_chen},
+    {"cheng", PINYIN_mp_cheng},
+    {"chi", PINYIN_mp_chi},
+    {"chong", PINYIN_mp_chong},
+    {"chou", PINYIN_mp_chou},
+    {"chu", PINYIN_mp_chu},
+    {"chuai", PINYIN_mp_chuai},
+    {"chuan", PINYIN_mp_chuan},
+    {"chuang", PINYIN_mp_chuang},
+    {"chui", PINYIN_mp_chui},
+    {"chun", PINYIN_mp_chun},
+    {"chuo", PINYIN_mp_chuo},
+    {"ci", PINYIN_mp_ci},
+    {"cong", PINYIN_mp_cong},
+    {"cou", PINYIN_mp_cou},
+    {"cu", PINYIN_mp_cu},
+    {"cuan", PINYIN_mp_cuan},
+    {"cui", PINYIN_mp_cui},
+    {"cun", PINYIN_mp_cun},
+    {"cuo", PINYIN_mp_cuo},
+    {"da", PINYIN_mp_da},
+    {"dai", PINYIN_mp_dai},
+    {"dan", PINYIN_mp_dan},
+    {"dang", PINYIN_mp_dang},
+    {"dao", PINYIN_mp_dao},
+    {"de", PINYIN_mp_de},
+    {"dei", PINYIN_mp_dei},
+    {"deng", PINYIN_mp_deng},
+    {"di", PINYIN_mp_di},
+    {"dia", PINYIN_mp_dia},
+    {"dian", PINYIN_mp_dian},
+    {"diao", PINYIN_mp_diao},
+    {"die", PINYIN_mp_die},
+    {"ding", PINYIN_mp_ding},
+    {"diu", PINYIN_mp_diu},
+    {"dong", PINYIN_mp_dong},
+    {"dou", PINYIN_mp_dou},
+    {"du", PINYIN_mp_du},
+    {"duan", PINYIN_mp_duan},
+    {"dui", PINYIN_mp_dui},
+    {"dun", PINYIN_mp_dun},
+    {"duo", PINYIN_mp_duo},
+    {"e", PINYIN_mp_e},
+    {"ei", PINYIN_mp_ei},
+    {"en", PINYIN_mp_en},
+    {"er", PINYIN_mp_er},
+    {"fa", PINYIN_mp_fa},
+    {"fan", PINYIN_mp_fan},
+    {"fang", PINYIN_mp_fang},
+    {"fei", PINYIN_mp_fei},
+    {"fen", PINYIN_mp_fen},
+    {"feng", PINYIN_mp_feng},
+    {"fo", PINYIN_mp_fo},
+    {"fou", PINYIN_mp_fou},
+    {"fu", PINYIN_mp_fu},
+    {"ga", PINYIN_mp_ga},
+    {"gai", PINYIN_mp_gai},
+    {"gan", PINYIN_mp_gan},
+    {"gang", PINYIN_mp_gang},
+    {"gao", PINYIN_mp_gao},
+    {"ge", PINYIN_mp_ge},
+    {"gei", PINYIN_mp_gei},
+    {"gen", PINYIN_mp_gen},
+    {"geng", PINYIN_mp_geng},
+    {"gong", PINYIN_mp_gong},
+    {"gou", PINYIN_mp_gou},
+    {"gu", PINYIN_mp_gu},
+    {"gua", PINYIN_mp_gua},
+    {"guai", PINYIN_mp_guai},
+    {"guan", PINYIN_mp_guan},
+    {"guang", PINYIN_mp_guang},
+    {"gui", PINYIN_mp_gui},
+    {"gun", PINYIN_mp_gun},
+    {"guo", PINYIN_mp_guo},
+    {"ha", PINYIN_mp_ha},
+    {"hai", PINYIN_mp_hai},
+    {"han", PINYIN_mp_han},
+    {"hang", PINYIN_mp_hang},
+    {"hao", PINYIN_mp_hao},
+    {"he", PINYIN_mp_he},
+    {"hei", PINYIN_mp_hei},
+    {"hen", PINYIN_mp_hen},
+    {"heng", PINYIN_mp_heng},
+    {"hong", PINYIN_mp_hong},
+    {"hou", PINYIN_mp_hou},
+    {"hu", PINYIN_mp_hu},
+    {"hua", PINYIN_mp_hua},
+    {"huai", PINYIN_mp_huai},
+    {"huan", PINYIN_mp_huan},
+    {"huang", PINYIN_mp_huang},
+    {"hui", PINYIN_mp_hui},
+    {"hun", PINYIN_mp_hun},
+    {"huo", PINYIN_mp_huo},
+    {"ji", PINYIN_mp_ji},
+    {"jia", PINYIN_mp_jia},
+    {"jian", PINYIN_mp_jian},
+    {"jiang", PINYIN_mp_jiang},
+    {"jiao", PINYIN_mp_jiao},
+    {"jie", PINYIN_mp_jie},
+    {"jin", PINYIN_mp_jin},
+    {"jing", PINYIN_mp_jing},
+    {"jiong", PINYIN_mp_jiong},
+    {"jiu", PINYIN_mp_jiu},
+    {"ju", PINYIN_mp_ju},
+    {"juan", PINYIN_mp_juan},
+    {"jue", PINYIN_mp_jue},
+    {"jun", PINYIN_mp_jun},
+    {"ka", PINYIN_mp_ka},
+    {"kai", PINYIN_mp_kai},
+    {"kan", PINYIN_mp_kan},
+    {"kang", PINYIN_mp_kang},
+    {"kao", PINYIN_mp_kao},
+    {"ke", PINYIN_mp_ke},
+    {"ken", PINYIN_mp_ken},
+    {"keng", PINYIN_mp_keng},
+    {"kong", PINYIN_mp_kong},
+    {"kou", PINYIN_mp_kou},
+    {"ku", PINYIN_mp_ku},
+    {"kua", PINYIN_mp_kua},
+    {"kuai", PINYIN_mp_kuai},
+    {"kuan", PINYIN_mp_kuan},
+    {"kuang", PINYIN_mp_kuang},
+    {"kui", PINYIN_mp_kui},
+    {"kun", PINYIN_mp_kun},
+    {"kuo", PINYIN_mp_kuo},
+    {"la", PINYIN_mp_la},
+    {"lai", PINYIN_mp_lai},
+    {"lan", PINYIN_mp_lan},
+    {"lang", PINYIN_mp_lang},
+    {"lao", PINYIN_mp_lao},
+    {"le", PINYIN_mp_le},
+    {"lei", PINYIN_mp_lei},
+    {"leng", PINYIN_mp_leng},
+    {"li", PINYIN_mp_li},
+    {"lia", PINYIN_mp_lia},
+    {"lian", PINYIN_mp_lian},
+    {"liang", PINYIN_mp_liang},
+    {"liao", PINYIN_mp_liao},
+    {"lie", PINYIN_mp_lie},
+    {"lin", PINYIN_mp_lin},
+    {"ling", PINYIN_mp_ling},
+    {"liu", PINYIN_mp_liu},
+    {"lo", PINYIN_mp_lo},
+    {"long", PINYIN_mp_long},
+    {"lou", PINYIN_mp_lou},
+    {"lu", PINYIN_mp_lu},
+    {"luan", PINYIN_mp_luan},
+    {"lue", PINYIN_mp_lue},
+    {"lun", PINYIN_mp_lun},
+    {"luo", PINYIN_mp_luo},
+    {"lv", PINYIN_mp_lv},
+    {"ma", PINYIN_mp_ma},
+    {"mai", PINYIN_mp_mai},
+    {"man", PINYIN_mp_man},
+    {"mang", PINYIN_mp_mang},
+    {"mao", PINYIN_mp_mao},
+    {"me", PINYIN_mp_me},
+    {"mei", PINYIN_mp_mei},
+    {"men", PINYIN_mp_men},
+    {"meng", PINYIN_mp_meng},
+    {"mi", PINYIN_mp_mi},
+    {"mian", PINYIN_mp_mian},
+    {"miao", PINYIN_mp_miao},
+    {"mie", PINYIN_mp_mie},
+    {"min", PINYIN_mp_min},
+    {"ming", PINYIN_mp_ming},
+    {"miu", PINYIN_mp_miu},
+    {"mo", PINYIN_mp_mo},
+    {"mou", PINYIN_mp_mou},
+    {"mu", PINYIN_mp_mu},
+    {"ma", PINYIN_mp_na},
+    {"nai", PINYIN_mp_nai},
+    {"nan", PINYIN_mp_nan},
+    {"nang", PINYIN_mp_nang},
+    {"nao", PINYIN_mp_nao},
+    {"ne", PINYIN_mp_ne},
+    {"nei", PINYIN_mp_nei},
+    {"nen", PINYIN_mp_nen},
+    {"neng", PINYIN_mp_neng},
+    {"ng", PINYIN_mp_ng},
+    {"ni", PINYIN_mp_ni},
+    {"nian", PINYIN_mp_nian},
+    {"niang", PINYIN_mp_niang},
+    {"niao", PINYIN_mp_niao},
+    {"nie", PINYIN_mp_nie},
+    {"nin", PINYIN_mp_nin},
+    {"ning", PINYIN_mp_ning},
+    {"niu", PINYIN_mp_niu},
+    {"nong", PINYIN_mp_nong},
+    {"nou", PINYIN_mp_nou},
+    {"nu", PINYIN_mp_nu},
+    {"nuan", PINYIN_mp_nuan},
+    {"nue", PINYIN_mp_nue},
+    {"nuo", PINYIN_mp_nuo},
+    {"nv", PINYIN_mp_nv},
+    {"o", PINYIN_mp_o},
+    {"ou", PINYIN_mp_ou},
+    {"pa", PINYIN_mp_pa},
+    {"pai", PINYIN_mp_pai},
+    {"pan", PINYIN_mp_pan},
+    {"pang", PINYIN_mp_pang},
+    {"pao", PINYIN_mp_pao},
+    {"pei", PINYIN_mp_pei},
+    {"pen", PINYIN_mp_pen},
+    {"peng", PINYIN_mp_peng},
+    {"pi", PINYIN_mp_pi},
+    {"pian", PINYIN_mp_pian},
+    {"piao", PINYIN_mp_piao},
+    {"pie", PINYIN_mp_pie},
+    {"pin", PINYIN_mp_pin},
+    {"ping", PINYIN_mp_ping},
+    {"po", PINYIN_mp_po},
+    {"pou", PINYIN_mp_pou},
+    {"pu", PINYIN_mp_pu},
+    {"qi", PINYIN_mp_qi},
+    {"qia", PINYIN_mp_qia},
+    {"qian", PINYIN_mp_qian},
+    {"qiang", PINYIN_mp_qiang},
+    {"qiao", PINYIN_mp_qiao},
+    {"qie", PINYIN_mp_qie},
+    {"qin", PINYIN_mp_qin},
+    {"qing", PINYIN_mp_qing},
+    {"qiong", PINYIN_mp_qiong},
+    {"qiu", PINYIN_mp_qiu},
+    {"qu", PINYIN_mp_qu},
+    {"quan", PINYIN_mp_quan},
+    {"que", PINYIN_mp_que},
+    {"qui", PINYIN_mp_qui},
+    {"qun", PINYIN_mp_qun},
+    {"ran", PINYIN_mp_ran},
+    {"rang", PINYIN_mp_rang},
+    {"rao", PINYIN_mp_rao},
+    {"re", PINYIN_mp_re},
+    {"ren", PINYIN_mp_ren},
+    {"reng", PINYIN_mp_reng},
+    {"ri", PINYIN_mp_ri},
+    {"rong", PINYIN_mp_rong},
+    {"rou", PINYIN_mp_rou},
+    {"ru", PINYIN_mp_ru},
+    {"ruan", PINYIN_mp_ruan},
+    {"rui", PINYIN_mp_rui},
+    {"run", PINYIN_mp_run},
+    {"ruo", PINYIN_mp_ruo},
+    {"sa", PINYIN_mp_sa},
+    {"sai", PINYIN_mp_sai},
+    {"san", PINYIN_mp_san},
+    {"sang", PINYIN_mp_sang},
+    {"sao", PINYIN_mp_sao},
+    {"se", PINYIN_mp_se},
+    {"sen", PINYIN_mp_sen},
+    {"seng", PINYIN_mp_seng},
+    {"sha", PINYIN_mp_sha},
+    {"shai", PINYIN_mp_shai},
+    {"shan", PINYIN_mp_shan},
+    {"shang ", PINYIN_mp_shang},
+    {"shao", PINYIN_mp_shao},
+    {"she", PINYIN_mp_she},
+    {"shei", PINYIN_mp_shei},
+    {"shen", PINYIN_mp_shen},
+    {"sheng", PINYIN_mp_sheng},
+    {"shi", PINYIN_mp_shi},
+    {"shou", PINYIN_mp_shou},
+    {"shu", PINYIN_mp_shu},
+    {"shua", PINYIN_mp_shua},
+    {"shuai", PINYIN_mp_shuai},
+    {"shuan", PINYIN_mp_shuan},
+    {"shuang", PINYIN_mp_shuang},
+    {"shui", PINYIN_mp_shui},
+    {"shun", PINYIN_mp_shun},
+    {"shuo", PINYIN_mp_shuo},
+    {"si", PINYIN_mp_si},
+    {"song", PINYIN_mp_song},
+    {"sou", PINYIN_mp_sou},
+    {"su", PINYIN_mp_su},
+    {"suan", PINYIN_mp_suan},
+    {"sui", PINYIN_mp_sui},
+    {"sun", PINYIN_mp_sun},
+    {"suo", PINYIN_mp_suo},
+    {"ta", PINYIN_mp_ta},
+    {"tai", PINYIN_mp_tai},
+    {"tan", PINYIN_mp_tan},
+    {"tang", PINYIN_mp_tang},
+    {"tao", PINYIN_mp_tao},
+    {"te", PINYIN_mp_te},
+    {"teng", PINYIN_mp_teng},
+    {"ti", PINYIN_mp_ti},
+    {"tian", PINYIN_mp_tian},
+    {"tiao", PINYIN_mp_tiao},
+    {"tie", PINYIN_mp_tie},
+    {"ting", PINYIN_mp_ting},
+    {"tong", PINYIN_mp_tong},
+    {"tou", PINYIN_mp_tou},
+    {"tu", PINYIN_mp_tu},
+    {"tuan", PINYIN_mp_tuan},
+    {"tui", PINYIN_mp_tui},
+    {"tun", PINYIN_mp_tun},
+    {"tuo", PINYIN_mp_tuo},
+    {"wa", PINYIN_mp_wa},
+    {"wai", PINYIN_mp_wai},
+    {"wan", PINYIN_mp_wan},
+    {"wang", PINYIN_mp_wang},
+    {"wei", PINYIN_mp_wei},
+    {"wen", PINYIN_mp_wen},
+    {"weng", PINYIN_mp_weng},
+    {"wo", PINYIN_mp_wo},
+    {"wu", PINYIN_mp_wu},
+    {"xi", PINYIN_mp_xi},
+    {"xia", PINYIN_mp_xia},
+    {"xian", PINYIN_mp_xian},
+    {"xiang", PINYIN_mp_xiang},
+    {"xiao", PINYIN_mp_xiao},
+    {"xie", PINYIN_mp_xie},
+    {"xin", PINYIN_mp_xin},
+    {"xing", PINYIN_mp_xing},
+    {"xiong", PINYIN_mp_xiong},
+    {"xiu", PINYIN_mp_xiu},
+    {"xu", PINYIN_mp_xu},
+    {"xuan", PINYIN_mp_xuan},
+    {"xue", PINYIN_mp_xue},
+    {"xun", PINYIN_mp_xun},
+    {"ya", PINYIN_mp_ya},
+    {"yan", PINYIN_mp_yan},
+    {"yang", PINYIN_mp_yang},
+    {"yao", PINYIN_mp_yao},
+    {"ye", PINYIN_mp_ye},
+    {"yi", PINYIN_mp_yi},
+    {"yin", PINYIN_mp_yin},
+    {"ying", PINYIN_mp_ying},
+    {"yo", PINYIN_mp_yo},
+    {"yong", PINYIN_mp_yong},
+    {"you", PINYIN_mp_you},
+    {"yu", PINYIN_mp_yu},
+    {"yuan", PINYIN_mp_yuan},
+    {"yue", PINYIN_mp_yue},
+    {"yun", PINYIN_mp_yun},
+    {"za", PINYIN_mp_za},
+    {"zai", PINYIN_mp_zai},
+    {"zan", PINYIN_mp_zan},
+    {"zang", PINYIN_mp_zang},
+    {"zao", PINYIN_mp_zao},
+    {"ze", PINYIN_mp_ze},
+    {"zei", PINYIN_mp_zei},
+    {"zen", PINYIN_mp_zen},
+    {"zeng", PINYIN_mp_zeng},
+    {"zha", PINYIN_mp_zha},
+    {"zhai", PINYIN_mp_zhai},
+    {"zhan", PINYIN_mp_zhan},
+    {"zhang", PINYIN_mp_zhang},
+    {"zhao", PINYIN_mp_zhao},
+    {"zhe", PINYIN_mp_zhe},
+    {"zhen", PINYIN_mp_zhen},
+    {"zheng", PINYIN_mp_zheng},
+    {"zhi", PINYIN_mp_zhi},
+    {"zhong", PINYIN_mp_zhong},
+    {"zhou", PINYIN_mp_zhou},
+    {"zhu", PINYIN_mp_zhu},
+    {"zhua", PINYIN_mp_zhua},
+    {"zhuai", PINYIN_mp_zhuai},
+    {"zhuan", PINYIN_mp_zhuan},
+    {"zhuang", PINYIN_mp_zhuang},
+    {"zhui", PINYIN_mp_zhui},
+    {"zhun", PINYIN_mp_zhun},
+    {"zhuo", PINYIN_mp_zhuo},
+    {"zi", PINYIN_mp_zi},
+    {"zong", PINYIN_mp_zong},
+    {"zou", PINYIN_mp_zou},
+    {"zu", PINYIN_mp_zu},
+    {"zuan", PINYIN_mp_zuan},
+    {"zui", PINYIN_mp_zui},
+    {"zun", PINYIN_mp_zun},
+    {"zuo", PINYIN_mp_zuo}
+};
+#endif
diff --git a/src/extra/widgets/keyboard/lv_zh_keyboard.c b/src/extra/widgets/keyboard/lv_zh_keyboard.c
new file mode 100644
index 000000000..16becab37
--- /dev/null
+++ b/src/extra/widgets/keyboard/lv_zh_keyboard.c
@@ -0,0 +1,751 @@
+
+/**
+ * @file lv_zh_keyboard.c
+ *
+ */
+
+/*********************
+ *      INCLUDES
+ *********************/
+#include "lv_zh_keyboard.h"
+#if LV_USE_ZH_KEYBOARD
+
+#include "../../../lvgl.h"
+#include <stdlib.h>
+#include <stdio.h>
+#include <string.h>
+#include "chinese_library.h"
+
+/*********************
+ *      DEFINES
+ *********************/
+#define MY_CLASS &lv_zh_keyboard_class
+#define LV_KB_BTN(width) LV_BTNMATRIX_CTRL_POPOVER | width
+
+/**********************
+ *      TYPEDEFS
+ **********************/
+
+/**********************
+ *  STATIC PROTOTYPES
+ **********************/
+static void lv_zh_keyboard_constructor(const lv_obj_class_t * class_p, lv_obj_t * obj);
+static void lv_zh_keyboard_destructor(const lv_obj_class_t * class_p, lv_obj_t * obj);
+static void lv_zh_keyboard_update_map(lv_obj_t * obj, uint8_t input_type);
+static void lv_zh_keyboard_update_ctrl_map(lv_obj_t * obj, uint8_t input_type);
+static void init_selection_box(lv_obj_t * parent);
+static void switch_input_type(uint8_t inputType);
+static void set_selection_box(uint8_t cand_show);
+static void show_chinese(char * text_uft8);
+
+/**********************
+ *  STATIC VARIABLES
+ **********************/
+
+const lv_obj_class_t lv_zh_keyboard_class = {
+    .constructor_cb = lv_zh_keyboard_constructor,
+    .destructor_cb = lv_zh_keyboard_destructor,
+    .width_def = LV_PCT(100),
+    .height_def = LV_PCT(50),
+    .instance_size = sizeof(lv_zh_keyboard_t),
+    .editable = 1,
+    .base_class = &lv_btnmatrix_class
+};
+
+static const char * const default_kb_map_lc[] = {" ", "\n", " ", "\n",
+                                                 "1#", "q", "w", "e", "r", "t", "y", "u", "i", "o", "p", LV_SYMBOL_BACKSPACE, "\n",
+                                                 "ABC", "a", "s", "d", "f", "g", "h", "j", "k", "l", LV_SYMBOL_NEW_LINE, "\n",
+                                                 "_", "-", "z", "x", "c", "v", "b", "n", "m", ".", ",", ":", "\n",
+                                                 LV_SYMBOL_KEYBOARD, "En/Zh", LV_SYMBOL_LEFT, " ", LV_SYMBOL_RIGHT, LV_SYMBOL_OK, ""
+                                                };
+
+static const lv_btnmatrix_ctrl_t default_kb_ctrl_lc_map[] = {
+    LV_BTNMATRIX_CTRL_HIDDEN, LV_BTNMATRIX_CTRL_HIDDEN,
+    LV_ZH_KEYBOARD_CTRL_BTN_FLAGS | 5, LV_KB_BTN(4), LV_KB_BTN(4), LV_KB_BTN(4), LV_KB_BTN(4), LV_KB_BTN(4), LV_KB_BTN(4), LV_KB_BTN(4), LV_KB_BTN(4), LV_KB_BTN(4), LV_KB_BTN(4), LV_BTNMATRIX_CTRL_CHECKED | 7,
+    LV_ZH_KEYBOARD_CTRL_BTN_FLAGS | 6, LV_KB_BTN(3), LV_KB_BTN(3), LV_KB_BTN(3), LV_KB_BTN(3), LV_KB_BTN(3), LV_KB_BTN(3), LV_KB_BTN(3), LV_KB_BTN(3), LV_KB_BTN(3), LV_BTNMATRIX_CTRL_CHECKED | 7,
+    LV_BTNMATRIX_CTRL_CHECKED | LV_KB_BTN(1), LV_BTNMATRIX_CTRL_CHECKED | LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_BTNMATRIX_CTRL_CHECKED | LV_KB_BTN(1), LV_BTNMATRIX_CTRL_CHECKED | LV_KB_BTN(1), LV_BTNMATRIX_CTRL_CHECKED | LV_KB_BTN(1),
+    LV_ZH_KEYBOARD_CTRL_BTN_FLAGS | 2, LV_ZH_KEYBOARD_CTRL_BTN_FLAGS | 3, LV_BTNMATRIX_CTRL_CHECKED | 2, 6, LV_BTNMATRIX_CTRL_CHECKED | 2, LV_ZH_KEYBOARD_CTRL_BTN_FLAGS | 2
+};
+
+static const char * const default_kb_map_uc[] = {" ", "\n",
+                                                 " ", "\n",
+                                                 "1#", "Q", "W", "E", "R", "T", "Y", "U", "I", "O", "P", LV_SYMBOL_BACKSPACE, "\n",
+                                                 "abc", "A", "S", "D", "F", "G", "H", "J", "K", "L", LV_SYMBOL_NEW_LINE, "\n",
+                                                 "_", "-", "Z", "X", "C", "V", "B", "N", "M", ".", ",", ":", "\n",
+                                                 LV_SYMBOL_KEYBOARD, "En/Zh", LV_SYMBOL_LEFT, " ", LV_SYMBOL_RIGHT, LV_SYMBOL_OK, ""
+                                                };
+
+static const lv_btnmatrix_ctrl_t default_kb_ctrl_uc_map[] = {
+    LV_BTNMATRIX_CTRL_HIDDEN, LV_BTNMATRIX_CTRL_HIDDEN,
+    LV_ZH_KEYBOARD_CTRL_BTN_FLAGS | 5, LV_KB_BTN(4), LV_KB_BTN(4), LV_KB_BTN(4), LV_KB_BTN(4), LV_KB_BTN(4), LV_KB_BTN(4), LV_KB_BTN(4), LV_KB_BTN(4), LV_KB_BTN(4), LV_KB_BTN(4), LV_BTNMATRIX_CTRL_CHECKED | 7,
+    LV_ZH_KEYBOARD_CTRL_BTN_FLAGS | 6, LV_KB_BTN(3), LV_KB_BTN(3), LV_KB_BTN(3), LV_KB_BTN(3), LV_KB_BTN(3), LV_KB_BTN(3), LV_KB_BTN(3), LV_KB_BTN(3), LV_KB_BTN(3), LV_BTNMATRIX_CTRL_CHECKED | 7,
+    LV_BTNMATRIX_CTRL_CHECKED | LV_KB_BTN(1), LV_BTNMATRIX_CTRL_CHECKED | LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_BTNMATRIX_CTRL_CHECKED | LV_KB_BTN(1), LV_BTNMATRIX_CTRL_CHECKED | LV_KB_BTN(1), LV_BTNMATRIX_CTRL_CHECKED | LV_KB_BTN(1),
+    LV_ZH_KEYBOARD_CTRL_BTN_FLAGS | 2, LV_ZH_KEYBOARD_CTRL_BTN_FLAGS | 3, LV_BTNMATRIX_CTRL_CHECKED | 2, 6, LV_BTNMATRIX_CTRL_CHECKED | 2, LV_ZH_KEYBOARD_CTRL_BTN_FLAGS | 2
+};
+
+static const char * const default_kb_map_spec[] = {" ", "\n",
+                                                   " ", "\n",
+                                                   "1", "2", "3", "4", "5", "6", "7", "8", "9", "0", LV_SYMBOL_BACKSPACE, "\n",
+                                                   "abc", "+", "-", "/", "*", "=", "%", "!", "?", "#", "<", ">", "\n",
+                                                   "\\", "@", "$", "(", ")", "{", "}", "[", "]", ";", "\"", "'", "\n",
+                                                   LV_SYMBOL_KEYBOARD, "En/Zh", LV_SYMBOL_LEFT, " ", LV_SYMBOL_RIGHT, LV_SYMBOL_OK, ""
+                                                  };
+
+static const lv_btnmatrix_ctrl_t default_kb_ctrl_spec_map[] = {
+    LV_BTNMATRIX_CTRL_HIDDEN, LV_BTNMATRIX_CTRL_HIDDEN,
+    LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_BTNMATRIX_CTRL_CHECKED | 2,
+    LV_ZH_KEYBOARD_CTRL_BTN_FLAGS | 2, LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1),
+    LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1), LV_KB_BTN(1),
+    LV_ZH_KEYBOARD_CTRL_BTN_FLAGS | 2, LV_ZH_KEYBOARD_CTRL_BTN_FLAGS | 3, LV_BTNMATRIX_CTRL_CHECKED | 2, 6, LV_BTNMATRIX_CTRL_CHECKED | 2, LV_ZH_KEYBOARD_CTRL_BTN_FLAGS | 2
+};
+
+static const char * const default_kb_map_num[] = {"1", "2", "3", LV_SYMBOL_KEYBOARD, "\n",
+                                                  "4", "5", "6", LV_SYMBOL_OK, "\n",
+                                                  "7", "8", "9", LV_SYMBOL_BACKSPACE, "\n",
+                                                  "+/-", "0", ".", LV_SYMBOL_LEFT, LV_SYMBOL_RIGHT, ""
+                                                 };
+
+static const lv_btnmatrix_ctrl_t default_kb_ctrl_num_map[] = {
+    1, 1, 1, LV_ZH_KEYBOARD_CTRL_BTN_FLAGS | 2,
+    1, 1, 1, LV_ZH_KEYBOARD_CTRL_BTN_FLAGS | 2,
+    1, 1, 1, 2,
+    1, 1, 1, 1, 1
+};
+
+static const char ** en_kb_map[9] = {
+    (const char **) & default_kb_map_lc[4],
+    (const char **) & default_kb_map_uc[4],
+    (const char **) & default_kb_map_spec[4],
+    (const char **)default_kb_map_num,
+    (const char **)default_kb_map_lc,
+    (const char **)default_kb_map_lc,
+    (const char **)default_kb_map_lc,
+    (const char **)default_kb_map_lc,
+    (const char **)NULL,
+};
+
+static const lv_btnmatrix_ctrl_t * en_kb_ctrl[9] = {
+    &default_kb_ctrl_lc_map[2],
+    &default_kb_ctrl_uc_map[2],
+    &default_kb_ctrl_spec_map[2],
+    default_kb_ctrl_num_map,
+    default_kb_ctrl_lc_map,
+    default_kb_ctrl_lc_map,
+    default_kb_ctrl_lc_map,
+    default_kb_ctrl_lc_map,
+    NULL,
+};
+
+static const char ** zh_kb_map[9] = {
+    (const char **)default_kb_map_lc,
+    (const char **)default_kb_map_uc,
+    (const char **)default_kb_map_spec,
+    (const char **)default_kb_map_num,
+    (const char **)default_kb_map_lc,
+    (const char **)default_kb_map_lc,
+    (const char **)default_kb_map_lc,
+    (const char **)default_kb_map_lc,
+    (const char **)NULL,
+};
+
+static const lv_btnmatrix_ctrl_t * zh_kb_ctrl[9] = {
+    default_kb_ctrl_lc_map,
+    default_kb_ctrl_uc_map,
+    default_kb_ctrl_spec_map,
+    default_kb_ctrl_num_map,
+    default_kb_ctrl_lc_map,
+    default_kb_ctrl_lc_map,
+    default_kb_ctrl_lc_map,
+    default_kb_ctrl_lc_map,
+    NULL,
+};
+
+typedef struct zh_input_function {
+    uint8_t input_type;
+    lv_font_t * custom_font;
+    lv_obj_t * obj_key_board;
+    lv_obj_t * obj_selection_box;
+    lv_obj_t * pinyin_label;
+    lv_obj_t * adjustable_box;
+    lv_obj_t * drop_btn_label;
+    uint8_t extend_flag;
+    char * matched_txt;
+} zh_input_struct;
+
+static zh_input_struct * zh_input_obj = NULL;
+
+/**********************
+ *      MACROS
+ **********************/
+
+/**********************
+ *   GLOBAL FUNCTIONS
+ **********************/
+
+/**
+ * Create a Keyboard object
+ * @param parent pointer to an object, it will be the parent of the new keyboard
+ * @param zh_font pointer to an font object.
+ * @return pointer to the created keyboard
+ */
+lv_obj_t * lv_zh_keyboard_create(lv_obj_t * parent, const lv_font_t * zh_font)
+{
+    LV_LOG_INFO("begin");
+    if(zh_input_obj) {
+        return NULL;
+    }
+
+    else {
+        zh_input_obj = lv_mem_alloc(sizeof(zh_input_struct));
+        if(zh_input_obj == NULL)
+            return NULL;
+        lv_memset(zh_input_obj, 0, sizeof(zh_input_struct));
+        zh_input_obj->custom_font = (lv_font_t *)zh_font;
+    }
+    lv_obj_t * obj = lv_obj_class_create_obj(&lv_zh_keyboard_class, parent);
+    lv_obj_class_init_obj(obj);
+    lv_group_remove_obj(obj);
+    return obj;
+}
+
+/*=====================
+ * Setter functions
+ *====================*/
+
+/**
+ * Assign a Text Area to the Keyboard. The pressed characters will be put there.
+ * @param kb pointer to a Keyboard object
+ * @param ta pointer to a Text Area object to write there
+ */
+void lv_zh_keyboard_set_textarea(lv_obj_t * obj, lv_obj_t * ta)
+{
+    if(ta) {
+        LV_ASSERT_OBJ(ta, &lv_textarea_class);
+    }
+
+    LV_ASSERT_OBJ(obj, MY_CLASS);
+    lv_zh_keyboard_t * keyboard = (lv_zh_keyboard_t *)obj;
+
+    /*Hide the cursor of the old Text area if cursor management is enabled*/
+    if(keyboard->ta) {
+        lv_obj_clear_state(obj, LV_STATE_FOCUSED);
+    }
+
+    keyboard->ta = ta;
+
+    /*Show the cursor of the new Text area if cursor management is enabled*/
+    if(keyboard->ta) {
+        lv_obj_add_flag(obj, LV_STATE_FOCUSED);
+    }
+
+    if(keyboard->ta) {
+        if(zh_input_obj->input_type) {
+            lv_obj_clear_flag(zh_input_obj->obj_selection_box, LV_OBJ_FLAG_HIDDEN);
+        }
+        else {
+            lv_obj_add_flag(zh_input_obj->obj_selection_box, LV_OBJ_FLAG_HIDDEN);
+        }
+    }
+}
+
+/**
+ * Set a new a mode (text or number map)
+ * @param kb pointer to a Keyboard object
+ * @param mode the mode from 'lv_keyboard_mode_t'
+ */
+void lv_zh_keyboard_set_mode(lv_obj_t * obj, lv_zh_keyboard_mode_t mode)
+{
+    LV_ASSERT_OBJ(obj, MY_CLASS);
+    lv_zh_keyboard_t * keyboard = (lv_zh_keyboard_t *)obj;
+    if(keyboard->mode == mode)
+        return;
+
+    keyboard->mode = mode;
+    lv_zh_keyboard_update_map(obj, zh_input_obj->input_type);
+    switch_input_type(zh_input_obj->input_type);
+}
+
+/**
+ * Show the button title in a popover when pressed.
+ * @param kb pointer to a Keyboard object
+ * @param en whether "popovers" mode is enabled
+ */
+void lv_zh_keyboard_set_popovers(lv_obj_t * obj, bool en)
+{
+    lv_zh_keyboard_t * keyboard = (lv_zh_keyboard_t *)obj;
+
+    if(keyboard->popovers == en) {
+        return;
+    }
+
+    keyboard->popovers = en;
+    lv_zh_keyboard_update_ctrl_map(obj, zh_input_obj->input_type);
+}
+
+/**
+ * Set a new map for the keyboard
+ * @param kb pointer to a Keyboard object
+ * @param mode keyboard map to alter 'lv_keyboard_mode_t'
+ * @param map pointer to a string array to describe the map.
+ *            See 'lv_btnmatrix_set_map()' for more info.
+ */
+void lv_zh_keyboard_set_map(lv_obj_t * obj, lv_zh_keyboard_mode_t mode, const char * map[],
+                            const lv_btnmatrix_ctrl_t ctrl_map[])
+{
+    en_kb_map[mode] = map;
+    en_kb_ctrl[mode] = ctrl_map;
+    lv_zh_keyboard_update_map(obj, zh_input_obj->input_type);
+}
+
+/*=====================
+ * Getter functions
+ *====================*/
+
+/**
+ * Assign a Text Area to the Keyboard. The pressed characters will be put there.
+ * @param kb pointer to a Keyboard object
+ * @return pointer to the assigned Text Area object
+ */
+lv_obj_t * lv_zh_keyboard_get_textarea(const lv_obj_t * obj)
+{
+    LV_ASSERT_OBJ(obj, MY_CLASS);
+    lv_zh_keyboard_t * keyboard = (lv_zh_keyboard_t *)obj;
+    return keyboard->ta;
+}
+
+/**
+ * Set a new a mode (text or number map)
+ * @param kb pointer to a Keyboard object
+ * @return the current mode from 'lv_keyboard_mode_t'
+ */
+lv_zh_keyboard_mode_t lv_zh_keyboard_get_mode(const lv_obj_t * obj)
+{
+    LV_ASSERT_OBJ(obj, MY_CLASS);
+    lv_zh_keyboard_t * keyboard = (lv_zh_keyboard_t *)obj;
+    return keyboard->mode;
+}
+
+/**
+ * Tell whether "popovers" mode is enabled or not.
+ * @param kb pointer to a Keyboard object
+ * @return true: "popovers" mode is enabled; false: disabled
+ */
+bool lv_zh_btnmatrix_get_popovers(const lv_obj_t * obj)
+{
+    lv_zh_keyboard_t * keyboard = (lv_zh_keyboard_t *)obj;
+    return keyboard->popovers;
+}
+
+/*=====================
+ * Other functions
+ *====================*/
+/**
+ * Default keyboard event to add characters to the Text area and change the map.
+ * If a custom `event_cb` is added to the keyboard this function can be called from it to handle the
+ * button clicks
+ * @param kb pointer to a keyboard
+ * @param event the triggering event
+ */
+void lv_zh_keyboard_def_event_cb(lv_event_t * e)
+{
+    lv_obj_t * obj = lv_event_get_target(e);
+
+    LV_ASSERT_OBJ(obj, MY_CLASS);
+    lv_zh_keyboard_t * keyboard = (lv_zh_keyboard_t *)obj;
+    uint16_t btn_id = lv_btnmatrix_get_selected_btn(obj);
+    if(btn_id == LV_BTNMATRIX_BTN_NONE)
+        return;
+
+    const char * txt = lv_btnmatrix_get_btn_text(obj, lv_btnmatrix_get_selected_btn(obj));
+    if(txt == NULL)
+        return;
+
+    if(strcmp(txt, "abc") == 0) {
+        keyboard->mode = LV_ZH_KEYBOARD_MODE_TEXT_LOWER;
+        lv_zh_keyboard_update_map(obj, zh_input_obj->input_type);
+        return;
+    }
+    else if(strcmp(txt, "ABC") == 0) {
+        keyboard->mode = LV_ZH_KEYBOARD_MODE_TEXT_UPPER;
+        lv_zh_keyboard_update_map(obj, zh_input_obj->input_type);
+        return;
+    }
+    else if(strcmp(txt, "1#") == 0) {
+        keyboard->mode = LV_ZH_KEYBOARD_MODE_SPECIAL;
+        lv_zh_keyboard_update_map(obj, zh_input_obj->input_type);
+        return;
+    }
+    else if(strcmp(txt, LV_SYMBOL_CLOSE) == 0 || strcmp(txt, LV_SYMBOL_KEYBOARD) == 0) {
+        lv_res_t res = lv_event_send(obj, LV_EVENT_CANCEL, NULL);
+        if(res != LV_RES_OK)
+            return;
+
+        if(keyboard->ta) {
+            res = lv_event_send(keyboard->ta, LV_EVENT_CANCEL, NULL);
+            if(res != LV_RES_OK)
+                return;
+        }
+        return;
+    }
+    else if(strcmp(txt, LV_SYMBOL_OK) == 0) {
+        lv_res_t res = lv_event_send(obj, LV_EVENT_READY, NULL);
+        if(res != LV_RES_OK)
+            return;
+
+        if(keyboard->ta) {
+            res = lv_event_send(keyboard->ta, LV_EVENT_READY, NULL);
+            if(res != LV_RES_OK)
+                return;
+        }
+        return;
+    }
+    else if(strcmp(txt, "En/Zh") == 0) {
+        if(zh_input_obj->input_type == 0) {
+            zh_input_obj->input_type = 1;
+        }
+        else {
+            zh_input_obj->input_type = 0;
+        }
+        lv_zh_keyboard_update_map(obj, zh_input_obj->input_type);
+        switch_input_type(zh_input_obj->input_type);
+        return;
+    }
+
+    /*Add the characters to the text area if set*/
+    if(keyboard->ta == NULL)
+        return;
+
+    if(strcmp(txt, "Enter") == 0 || strcmp(txt, LV_SYMBOL_NEW_LINE) == 0) {
+        lv_textarea_add_char(keyboard->ta, '\n');
+        if(lv_textarea_get_one_line(keyboard->ta)) {
+            lv_res_t res = lv_event_send(keyboard->ta, LV_EVENT_READY, NULL);
+            if(res != LV_RES_OK)
+                return;
+        }
+    }
+    else if(strcmp(txt, LV_SYMBOL_LEFT) == 0) {
+        lv_textarea_cursor_left(keyboard->ta);
+    }
+    else if(strcmp(txt, LV_SYMBOL_RIGHT) == 0) {
+        lv_textarea_cursor_right(keyboard->ta);
+    }
+    else if(strcmp(txt, LV_SYMBOL_BACKSPACE) == 0) {
+        const char * pinyin_str;
+        pinyin_str = lv_textarea_get_text(zh_input_obj->pinyin_label);
+        if(pinyin_str[0] != 0) {
+            lv_textarea_del_char(zh_input_obj->pinyin_label);
+            const char * kb_input = lv_textarea_get_text(zh_input_obj->pinyin_label);
+            if(kb_input[0] != 0) {
+                zh_input_obj->matched_txt = get_chinese((char *)kb_input);
+                show_chinese(zh_input_obj->matched_txt);
+            }
+            return;
+        }
+        else {
+            lv_textarea_set_text(zh_input_obj->adjustable_box, "");
+        }
+        lv_textarea_del_char(keyboard->ta);
+    }
+    else if(strcmp(txt, "+/-") == 0) {
+        uint16_t cur = lv_textarea_get_cursor_pos(keyboard->ta);
+        const char * ta_txt = lv_textarea_get_text(keyboard->ta);
+        if(ta_txt[0] == '-') {
+            lv_textarea_set_cursor_pos(keyboard->ta, 1);
+            lv_textarea_del_char(keyboard->ta);
+            lv_textarea_add_char(keyboard->ta, '+');
+            lv_textarea_set_cursor_pos(keyboard->ta, cur);
+        }
+        else if(ta_txt[0] == '+') {
+            lv_textarea_set_cursor_pos(keyboard->ta, 1);
+            lv_textarea_del_char(keyboard->ta);
+            lv_textarea_add_char(keyboard->ta, '-');
+            lv_textarea_set_cursor_pos(keyboard->ta, cur);
+        }
+        else {
+            lv_textarea_set_cursor_pos(keyboard->ta, 0);
+            lv_textarea_add_char(keyboard->ta, '-');
+            lv_textarea_set_cursor_pos(keyboard->ta, cur + 1);
+        }
+    }
+    else {
+        if(keyboard->ta) {
+            if((zh_input_obj->input_type == 0) || (keyboard->mode == LV_ZH_KEYBOARD_MODE_SPECIAL)) {
+                lv_textarea_add_text(keyboard->ta, txt);
+            }
+            else if(zh_input_obj->input_type == 1) {
+                lv_textarea_add_text(zh_input_obj->pinyin_label, txt);
+                const char * kb_input = lv_textarea_get_text(zh_input_obj->pinyin_label);
+                if(kb_input[0] != 0) {
+                    zh_input_obj->matched_txt = get_chinese((char *)kb_input);
+                    show_chinese(zh_input_obj->matched_txt);
+                }
+            }
+        }
+    }
+}
+
+/**
+ * Find Chinese characters based on pinyin.
+ * @param input_string pointer to input pinyin.
+ * @return pointer to chinese characters.
+ */
+
+char * get_chinese(char * input_string)
+{
+    char i, cInputStrLength;
+    int pinyinLen, j;
+
+    cInputStrLength = strlen(input_string);
+    if(*input_string == '\0' || *input_string == 'i' || *input_string == 'u' || *input_string == 'v') {
+        return NULL;
+    }
+
+    for(i = 0; i < cInputStrLength; i++) {
+        *(input_string + i) |= 0x20;
+    }
+    pinyinLen = sizeof(PINYIN_SUMMARY) / sizeof(PINYIN_SUMMARY[0]);
+
+    for(j = 0; j < pinyinLen; j++) {
+        if(strcmp(input_string, PINYIN_SUMMARY[j].PINYIN) == 0) {
+            return PINYIN_SUMMARY[j].PINYIN_mp;
+        }
+    }
+    return NULL;
+}
+
+/**
+ * Tell whether "popovers" mode is enabled or not.
+ * @param kb pointer to a Keyboard object
+ * @return true: "popovers" mode is enabled; false: disabled
+ */
+
+static void switch_input_type(uint8_t inputType)
+{
+    zh_input_obj->matched_txt = NULL;
+    if(inputType == 0) {
+        set_selection_box(0);
+    }
+    else if(inputType == 1) {
+        set_selection_box(1);
+    }
+}
+
+static void font_click_event_cb(lv_event_t * e)
+{
+    lv_event_code_t code = lv_event_get_code(e);
+    lv_obj_t * ta = lv_event_get_target(e);
+
+    static uint16_t index = 0;
+    if(code == LV_EVENT_CLICKED) {
+        index = lv_textarea_get_cursor_pos(ta);
+        if(index % 2 == 0) {
+            index = index / 2;
+            if(zh_input_obj->matched_txt != NULL && index < strlen(zh_input_obj->matched_txt) / 3) {
+                char uft8_char[3] = {0};
+                lv_zh_keyboard_t * keyboard = (lv_zh_keyboard_t *)zh_input_obj->obj_key_board;
+                lv_memcpy(uft8_char, &zh_input_obj->matched_txt[index * 3], 3);
+                if(keyboard->ta != NULL) {
+                    lv_textarea_add_text(keyboard->ta, uft8_char);
+                    lv_textarea_set_text(zh_input_obj->adjustable_box, "");
+                    zh_input_obj->matched_txt = NULL;
+                    lv_textarea_set_text(zh_input_obj->pinyin_label, "");
+                }
+            }
+        }
+    }
+}
+
+static void expand_retract_click_cb(lv_event_t * e)
+{
+    zh_input_obj->extend_flag = (zh_input_obj->extend_flag == 1) ? 2 : 1;
+    set_selection_box(zh_input_obj->extend_flag);
+}
+
+static void set_selection_box(uint8_t cand_show)
+{
+    if(zh_input_obj->obj_selection_box != NULL) {
+        if(cand_show == 0) {
+            lv_obj_add_flag(zh_input_obj->obj_selection_box, LV_OBJ_FLAG_HIDDEN);
+        }
+        else if(cand_show == 1) {
+            lv_textarea_set_one_line(zh_input_obj->adjustable_box, true);
+            lv_obj_set_scroll_dir(zh_input_obj->adjustable_box, LV_DIR_HOR);
+            lv_obj_set_height(zh_input_obj->adjustable_box, 38);
+            lv_obj_set_height(zh_input_obj->obj_selection_box, 38);
+            lv_label_set_text(zh_input_obj->drop_btn_label, LV_SYMBOL_DOWN);
+            lv_obj_clear_flag(zh_input_obj->obj_selection_box, LV_OBJ_FLAG_HIDDEN);
+
+        }
+        else if(cand_show == 2) {
+            lv_textarea_set_one_line(zh_input_obj->adjustable_box, false);
+            lv_obj_set_scroll_dir(zh_input_obj->adjustable_box, LV_DIR_VER);
+            lv_obj_set_height(zh_input_obj->adjustable_box, lv_obj_get_height(zh_input_obj->obj_key_board));
+            lv_obj_set_height(zh_input_obj->obj_selection_box, lv_obj_get_height(zh_input_obj->obj_key_board));
+            lv_label_set_text(zh_input_obj->drop_btn_label, LV_SYMBOL_UP);
+            lv_obj_clear_flag(zh_input_obj->obj_selection_box, LV_OBJ_FLAG_HIDDEN);
+
+        }
+    }
+}
+
+static void show_chinese(char * chineseText)
+{
+    if(chineseText != NULL) {
+        size_t i = 0, j = 0;
+        char resultText[4 * 120 + 12] = {0};
+        for(i = 0; i < strlen(chineseText); i++) {
+            if(chineseText[i] == 0) break;
+            resultText[j++] = chineseText[i];
+            if((i + 1) % 3 == 0) {
+                resultText[j++] = ' ';
+            }
+        }
+        lv_textarea_set_text(zh_input_obj->adjustable_box, resultText);
+        lv_obj_scroll_to(zh_input_obj->adjustable_box, 0, 0, LV_ANIM_OFF);
+    }
+    else {
+        lv_textarea_set_text(zh_input_obj->adjustable_box, "");
+    }
+    return;
+}
+
+static void init_selection_box(lv_obj_t * parent)
+{
+    lv_obj_update_layout(parent);
+    lv_obj_set_style_bg_opa(parent, LV_OPA_COVER, 0);
+    zh_input_obj->obj_selection_box = lv_obj_create(parent);
+    lv_obj_align(zh_input_obj->obj_selection_box, LV_ALIGN_TOP_MID, 0, 0);
+    lv_obj_set_width(zh_input_obj->obj_selection_box, lv_obj_get_width(parent));
+    lv_obj_set_style_bg_color(zh_input_obj->obj_selection_box, lv_color_hex(0xf7f7f7), 0);
+    lv_obj_set_style_pad_all(zh_input_obj->obj_selection_box, 5, 0);
+    lv_obj_set_style_pad_top(zh_input_obj->obj_selection_box, 12, 0);
+    lv_obj_clear_flag(zh_input_obj->obj_selection_box, LV_OBJ_FLAG_SCROLLABLE);
+
+    zh_input_obj->pinyin_label = lv_textarea_create(zh_input_obj->obj_selection_box);
+    lv_obj_remove_style_all(zh_input_obj->pinyin_label);
+    lv_obj_align(zh_input_obj->pinyin_label, LV_ALIGN_TOP_LEFT, 0, -12);
+    lv_obj_clear_flag(zh_input_obj->pinyin_label, LV_OBJ_FLAG_CLICKABLE);
+    lv_obj_set_size(zh_input_obj->pinyin_label, lv_obj_get_width(zh_input_obj->obj_selection_box), 20);
+    lv_obj_set_style_text_font(zh_input_obj->pinyin_label, &lv_font_montserrat_12, 0);
+    lv_obj_set_style_text_color(zh_input_obj->pinyin_label, lv_color_hex(0x262626), 0);
+
+    lv_obj_t * ext_btn = lv_btn_create(zh_input_obj->obj_selection_box);
+    lv_obj_remove_style_all(ext_btn);
+    lv_obj_align(ext_btn, LV_ALIGN_TOP_RIGHT, 0, -5);
+    lv_obj_set_size(ext_btn, 30, 30);
+    lv_obj_add_event_cb(ext_btn, expand_retract_click_cb, LV_EVENT_CLICKED, NULL);
+    lv_obj_clear_flag(ext_btn, LV_OBJ_FLAG_CLICK_FOCUSABLE | LV_OBJ_FLAG_FLOATING);
+    zh_input_obj->drop_btn_label = lv_label_create(ext_btn);
+    lv_obj_set_align(zh_input_obj->drop_btn_label, LV_ALIGN_CENTER);
+    lv_obj_set_style_text_font(zh_input_obj->drop_btn_label, LV_FONT_DEFAULT, 0);
+    lv_label_set_text(zh_input_obj->drop_btn_label, LV_SYMBOL_DOWN);
+    lv_obj_set_style_text_color(zh_input_obj->drop_btn_label, lv_color_hex(0x262626), 0);
+    lv_obj_update_layout(zh_input_obj->obj_selection_box);
+
+    int16_t width;
+    zh_input_obj->adjustable_box = lv_textarea_create(zh_input_obj->obj_selection_box);
+    width = lv_obj_get_width(zh_input_obj->obj_selection_box) - lv_obj_get_width(ext_btn) - 20;
+    lv_obj_remove_style_all(zh_input_obj->adjustable_box);
+    lv_obj_set_width(zh_input_obj->adjustable_box, width);
+    lv_obj_set_style_text_font(zh_input_obj->adjustable_box, zh_input_obj->custom_font, 0);
+    lv_obj_set_style_text_color(zh_input_obj->adjustable_box, lv_color_hex(0x262626), 0);
+    lv_obj_align(zh_input_obj->adjustable_box, LV_ALIGN_TOP_LEFT, 0, 0);
+    lv_obj_clear_flag(zh_input_obj->adjustable_box, LV_OBJ_FLAG_CLICK_FOCUSABLE);
+    lv_obj_add_event_cb(zh_input_obj->adjustable_box, font_click_event_cb, LV_EVENT_CLICKED, 0);
+
+    lv_obj_update_layout(parent);
+    switch_input_type(zh_input_obj->input_type);
+}
+
+/**********************
+ *   STATIC FUNCTIONS
+ **********************/
+
+static void lv_zh_keyboard_constructor(const lv_obj_class_t * class_p, lv_obj_t * obj)
+{
+    LV_UNUSED(class_p);
+    lv_obj_clear_flag(obj, LV_OBJ_FLAG_CLICK_FOCUSABLE);
+
+    lv_zh_keyboard_t * keyboard = (lv_zh_keyboard_t *)obj;
+    keyboard->ta = NULL;
+    keyboard->mode = LV_ZH_KEYBOARD_MODE_TEXT_LOWER;
+    keyboard->popovers = 0;
+
+    lv_disp_t * disp = lv_obj_get_disp(obj);
+    obj->class_p = &lv_zh_keyboard_class;
+    disp->theme->apply_cb(NULL, obj);
+    lv_obj_clear_flag(obj, LV_OBJ_FLAG_SCROLLABLE);
+
+    zh_input_obj->obj_key_board = obj;
+    zh_input_obj->input_type = 1;
+    zh_input_obj->extend_flag = 1;
+    lv_obj_align(obj, LV_ALIGN_BOTTOM_MID, 0, 0);
+    lv_obj_add_event_cb(obj, lv_zh_keyboard_def_event_cb, LV_EVENT_VALUE_CHANGED, NULL);
+    lv_obj_set_style_base_dir(obj, LV_BASE_DIR_LTR, 0);
+    lv_zh_keyboard_update_map(obj, 1);
+    lv_obj_update_layout(obj);
+    init_selection_box(obj);
+}
+
+/**
+ * Update the key and control map for the current mode
+ * @param obj pointer to a keyboard object
+ */
+static void lv_zh_keyboard_update_map(lv_obj_t * obj, uint8_t input_type)
+{
+    lv_zh_keyboard_t * keyboard = (lv_zh_keyboard_t *)obj;
+    if(input_type == 0) {
+        lv_btnmatrix_set_map(obj, en_kb_map[keyboard->mode]);
+        lv_btnmatrix_set_ctrl_map(obj, en_kb_ctrl[keyboard->mode]);
+    }
+    else {
+        lv_btnmatrix_set_map(obj, zh_kb_map[keyboard->mode]);
+        lv_btnmatrix_set_ctrl_map(obj, zh_kb_ctrl[keyboard->mode]);
+    }
+}
+
+/**
+ * Update the control map for the current mode
+ * @param obj pointer to a keyboard object
+ */
+static void lv_zh_keyboard_update_ctrl_map(lv_obj_t * obj, uint8_t input_type)
+{
+    lv_zh_keyboard_t * keyboard = (lv_zh_keyboard_t *)obj;
+
+    if(keyboard->popovers) {
+        /*Apply the current control map (already includes LV_BTNMATRIX_CTRL_POPOVER flags)*/
+        if(input_type == 0) {
+            lv_btnmatrix_set_ctrl_map(obj, en_kb_ctrl[keyboard->mode]);
+        }
+        else {
+            lv_btnmatrix_set_ctrl_map(obj, zh_kb_ctrl[keyboard->mode]);
+        }
+    }
+    else {
+        /*Make a copy of the current control map*/
+        lv_btnmatrix_t * btnm = (lv_btnmatrix_t *)obj;
+        lv_btnmatrix_ctrl_t * ctrl_map = lv_mem_alloc(btnm->btn_cnt * sizeof(lv_btnmatrix_ctrl_t));
+        lv_memcpy(ctrl_map, en_kb_ctrl[keyboard->mode], sizeof(lv_btnmatrix_ctrl_t) * btnm->btn_cnt);
+
+        /*Remove all LV_BTNMATRIX_CTRL_POPOVER flags*/
+        for(uint16_t i = 0; i < btnm->btn_cnt; i++) {
+            ctrl_map[i] &= (~LV_BTNMATRIX_CTRL_POPOVER);
+        }
+
+        /*Apply new control map and clean up*/
+        lv_btnmatrix_set_ctrl_map(obj, ctrl_map);
+        lv_mem_free(ctrl_map);
+    }
+}
+
+static void lv_zh_keyboard_destructor(const lv_obj_class_t * class_p, lv_obj_t * obj)
+{
+    LV_UNUSED(class_p);
+    lv_obj_clear_flag(obj, LV_OBJ_FLAG_CLICK_FOCUSABLE);
+    lv_zh_keyboard_t * keyboard = (lv_zh_keyboard_t *)obj;
+    keyboard->ta = NULL;
+    /* when the screen deleted will set the zh_input_obj value to NULL*/
+    zh_input_obj = NULL;
+}
+
+#endif /*LV_USE_ZH_KEYBOARD*/
diff --git a/src/extra/widgets/keyboard/lv_zh_keyboard.h b/src/extra/widgets/keyboard/lv_zh_keyboard.h
new file mode 100644
index 000000000..1de0b5b82
--- /dev/null
+++ b/src/extra/widgets/keyboard/lv_zh_keyboard.h
@@ -0,0 +1,190 @@
+/**
+ * @file lv_zh_keyboard.h
+ *
+ */
+
+#ifndef LV_ZH_KEYBOARD_H
+#define LV_ZH_KEYBOARD_H
+
+#ifdef __cplusplus
+extern "C" {
+#endif
+
+/*********************
+ *      INCLUDES
+ *********************/
+#include "../../../widgets/lv_btnmatrix.h"
+
+#if LV_USE_ZH_KEYBOARD
+/*Testing of dependencies*/
+#if LV_USE_BTNMATRIX == 0
+#error "lv_kb: lv_btnm is required. Enable it in lv_conf.h (LV_USE_BTNMATRIX  1) "
+#endif
+
+#if LV_USE_TEXTAREA == 0
+#error "lv_kb: lv_ta is required. Enable it in lv_conf.h (LV_USE_TEXTAREA  1) "
+#endif
+
+/*********************
+ *      DEFINES
+ *********************/
+#define LV_ZH_KEYBOARD_CTRL_BTN_FLAGS (LV_BTNMATRIX_CTRL_NO_REPEAT | LV_BTNMATRIX_CTRL_CLICK_TRIG | LV_BTNMATRIX_CTRL_CHECKED)
+
+/**********************
+ *      TYPEDEFS
+ **********************/
+
+/** Current keyboard mode.*/
+enum {
+    LV_ZH_KEYBOARD_MODE_TEXT_LOWER,
+    LV_ZH_KEYBOARD_MODE_TEXT_UPPER,
+    LV_ZH_KEYBOARD_MODE_SPECIAL,
+    LV_ZH_KEYBOARD_MODE_NUMBER,
+    LV_ZH_KEYBOARD_MODE_USER_1,
+    LV_ZH_KEYBOARD_MODE_USER_2,
+    LV_ZH_KEYBOARD_MODE_USER_3,
+    LV_ZH_KEYBOARD_MODE_USER_4,
+};
+typedef uint8_t lv_zh_keyboard_mode_t;
+
+/*Data of keyboard*/
+typedef struct {
+    lv_btnmatrix_t btnm;
+    lv_obj_t * ta;              /*Pointer to the assigned text area*/
+    lv_zh_keyboard_mode_t mode;    /*Key map type*/
+    uint8_t popovers : 1;       /*Show button titles in popovers on press*/
+} lv_zh_keyboard_t;
+
+extern const lv_obj_class_t lv_zh_keyboard_class;
+
+/**********************
+ * GLOBAL PROTOTYPES
+ **********************/
+
+/**
+ * Create a Keyboard object
+ * @param parent pointer to an object, it will be the parent of the new keyboard
+ * @param zh_font pointer to an font object.
+ * @return pointer to the created keyboard
+ */
+lv_obj_t * lv_zh_keyboard_create(lv_obj_t * parent, const lv_font_t * zh_font);
+
+/*=====================
+ * Setter functions
+ *====================*/
+
+/**
+ * Assign a Text Area to the Keyboard. The pressed characters will be put there.
+ * @param kb pointer to a Keyboard object
+ * @param ta pointer to a Text Area object to write there
+ */
+void lv_zh_keyboard_set_textarea(lv_obj_t * kb, lv_obj_t * ta);
+
+/**
+ * Set a new a mode (text or number map)
+ * @param kb pointer to a Keyboard object
+ * @param mode the mode from 'lv_keyboard_mode_t'
+ */
+void lv_zh_keyboard_set_mode(lv_obj_t * kb, lv_zh_keyboard_mode_t mode);
+
+/**
+ * Show the button title in a popover when pressed.
+ * @param kb pointer to a Keyboard object
+ * @param en whether "popovers" mode is enabled
+ */
+void lv_zh_keyboard_set_popovers(lv_obj_t * kb, bool en);
+
+/**
+ * Set a new map for the keyboard
+ * @param kb pointer to a Keyboard object
+ * @param mode keyboard map to alter 'lv_keyboard_mode_t'
+ * @param map pointer to a string array to describe the map.
+ *            See 'lv_btnmatrix_set_map()' for more info.
+ */
+void lv_zh_keyboard_set_map(lv_obj_t * kb, lv_zh_keyboard_mode_t mode, const char * map[],
+                            const lv_btnmatrix_ctrl_t ctrl_map[]);
+
+/*=====================
+ * Getter functions
+ *====================*/
+
+/**
+ * Assign a Text Area to the Keyboard. The pressed characters will be put there.
+ * @param kb pointer to a Keyboard object
+ * @return pointer to the assigned Text Area object
+ */
+lv_obj_t * lv_zh_keyboard_get_textarea(const lv_obj_t * kb);
+
+/**
+ * Set a new a mode (text or number map)
+ * @param kb pointer to a Keyboard object
+ * @return the current mode from 'lv_keyboard_mode_t'
+ */
+lv_zh_keyboard_mode_t lv_zh_keyboard_get_mode(const lv_obj_t * kb);
+
+/**
+ * Tell whether "popovers" mode is enabled or not.
+ * @param kb pointer to a Keyboard object
+ * @return true: "popovers" mode is enabled; false: disabled
+ */
+bool lv_zh_btnmatrix_get_popovers(const lv_obj_t * obj);
+
+/**
+ * Get the current map of a keyboard
+ * @param kb pointer to a keyboard object
+ * @return the current map
+ */
+static inline const char ** lv_zh_keyboard_get_map_array(const lv_obj_t * kb)
+{
+    return lv_btnmatrix_get_map(kb);
+}
+
+/**
+ * Get the index of the lastly "activated" button by the user (pressed, released, focused etc)
+ * Useful in the `event_cb` to get the text of the button, check if hidden etc.
+ * @param obj       pointer to button matrix object
+ * @return          index of the last released button (LV_BTNMATRIX_BTN_NONE: if unset)
+ */
+static inline uint16_t lv_zh_keyboard_get_selected_btn(const lv_obj_t * obj)
+{
+    return lv_btnmatrix_get_selected_btn(obj);
+}
+
+/**
+ * Get the button's text
+ * @param obj       pointer to button matrix object
+ * @param btn_id    the index a button not counting new line characters.
+ * @return          text of btn_index` button
+ */
+static inline const char * lv_zh_keyboard_get_btn_text(const lv_obj_t * obj, uint16_t btn_id)
+{
+    return lv_btnmatrix_get_btn_text(obj, btn_id);
+}
+
+
+/*=====================
+ * Other functions
+ *====================*/
+
+/**
+ * Default keyboard event to add characters to the Text area and change the map.
+ * If a custom `event_cb` is added to the keyboard this function can be called from it to handle the
+ * button clicks
+ * @param kb pointer to a keyboard
+ * @param event the triggering event
+ */
+void lv_zh_keyboard_def_event_cb(lv_event_t * e);
+
+char * get_chinese(char * input_string);
+
+/**********************
+ *      MACROS
+ **********************/
+
+#endif  /*LV_USE_KEYBOARD*/
+
+#ifdef __cplusplus
+} /*extern "C"*/
+#endif
+
+#endif /*LV_KEYBOARD_H*/
diff --git a/src/extra/widgets/lv_widgets.h b/src/extra/widgets/lv_widgets.h
index 114181022..f2ee4d8ee 100644
--- a/src/extra/widgets/lv_widgets.h
+++ b/src/extra/widgets/lv_widgets.h
@@ -17,21 +17,28 @@ extern "C" {
 #include "calendar/lv_calendar.h"
 #include "calendar/lv_calendar_header_arrow.h"
 #include "calendar/lv_calendar_header_dropdown.h"
+#include "carousel/lv_carousel.h"
 #include "chart/lv_chart.h"
 #include "keyboard/lv_keyboard.h"
+#include "keyboard/lv_zh_keyboard.h"
 #include "list/lv_list.h"
 #include "menu/lv_menu.h"
 #include "msgbox/lv_msgbox.h"
 #include "meter/lv_meter.h"
+#include "analogclock/lv_analogclock.h"
+#include "radiobtn/lv_radiobtn.h"
 #include "spinbox/lv_spinbox.h"
 #include "spinner/lv_spinner.h"
 #include "tabview/lv_tabview.h"
+#include "textprogress/lv_textprogress.h"
 #include "tileview/lv_tileview.h"
 #include "win/lv_win.h"
 #include "colorwheel/lv_colorwheel.h"
 #include "led/lv_led.h"
 #include "imgbtn/lv_imgbtn.h"
 #include "span/lv_span.h"
+#include "dclock/lv_dclock.h"
+#include "video/lv_video.h"
 
 /*********************
  *      DEFINES
diff --git a/src/extra/widgets/radiobtn/lv_radiobtn.c b/src/extra/widgets/radiobtn/lv_radiobtn.c
new file mode 100644
index 000000000..0ade7d967
--- /dev/null
+++ b/src/extra/widgets/radiobtn/lv_radiobtn.c
@@ -0,0 +1,385 @@
+/**
+ * @file lv_radiobtn.c
+ *
+ */
+
+/*********************
+ *      INCLUDES
+ *********************/
+#include "lv_radiobtn.h"
+
+#if LV_USE_RADIOBTN != 0
+
+#include "../../../misc/lv_assert.h"
+#include "../../../core/lv_group.h"
+#include "../../../draw/lv_draw.h"
+#include "../../../misc/lv_txt_ap.h"
+
+/*********************
+ *      DEFINES
+ *********************/
+#define MY_CLASS &lv_radiobtn_class
+#define ITEM_CLASS &lv_radiobtn_item_class
+
+/**********************
+ *      TYPEDEFS
+ **********************/
+
+/**********************
+ *  STATIC PROTOTYPES
+ **********************/
+static void lv_radiobtn_constructor(const lv_obj_class_t * class_p, lv_obj_t * obj);
+static void lv_radiobtn_event(const lv_obj_class_t * class_p, lv_event_t * e);
+static void lv_radiobtn_item_constructor(const lv_obj_class_t * class_p, lv_obj_t * obj);
+static void lv_radiobtn_item_destructor(const lv_obj_class_t * class_p, lv_obj_t * obj);
+static void lv_radiobtn_item_event(const lv_obj_class_t * class_p, lv_event_t * e);
+static void lv_radiobtn_item_draw(lv_event_t * e);
+
+
+/**********************
+ *  STATIC VARIABLES
+ **********************/
+const lv_obj_class_t lv_radiobtn_class = {
+    .constructor_cb = lv_radiobtn_constructor,
+    .event_cb = lv_radiobtn_event,
+    .instance_size = sizeof(lv_radiobtn_t),
+    .width_def = (LV_DPI_DEF * 3) / 4,
+    .height_def = (LV_DPI_DEF * 3) / 4,
+    .base_class = &lv_obj_class
+};
+
+const lv_obj_class_t lv_radiobtn_item_class = {
+    .constructor_cb = lv_radiobtn_item_constructor,
+    .destructor_cb = lv_radiobtn_item_destructor,
+    .event_cb = lv_radiobtn_item_event,
+    .width_def = LV_SIZE_CONTENT,
+    .height_def = LV_SIZE_CONTENT,
+    .group_def = LV_OBJ_CLASS_GROUP_DEF_TRUE,
+    .instance_size = sizeof(lv_radiobtn_item_t),
+    .base_class = &lv_obj_class
+};
+
+
+
+/**********************
+ *      MACROS
+ **********************/
+
+/**********************
+ *   GLOBAL FUNCTIONS
+ **********************/
+
+lv_obj_t * lv_radiobtn_create(lv_obj_t * parent)
+{
+    LV_LOG_INFO("begin");
+    lv_obj_t * obj = lv_obj_class_create_obj(MY_CLASS, parent);
+    lv_obj_class_init_obj(obj);
+
+    return obj;
+}
+
+lv_obj_t * lv_radiobtn_create_item(lv_obj_t * parent)
+{
+    LV_LOG_INFO("begin");
+    lv_obj_t * obj = lv_obj_class_create_obj(ITEM_CLASS, parent);
+    lv_obj_class_init_obj(obj);
+
+    return obj;
+}
+
+lv_obj_t * lv_radiobtn_add_item(lv_obj_t * parent, const char * txt)
+{
+    LV_LOG_INFO("begin");
+    lv_obj_t * obj = lv_radiobtn_create_item(parent);
+    lv_obj_class_init_obj(obj);
+    lv_obj_add_flag(obj, LV_OBJ_FLAG_EVENT_BUBBLE);
+    lv_obj_set_size(obj, LV_PCT(100), LV_SIZE_CONTENT);
+    if(txt) {
+        lv_radiobtn_set_item_text(obj, txt);
+    }
+
+    return obj;
+}
+
+/*=====================
+ * Setter functions
+ *====================*/
+
+void lv_radiobtn_set_item_text(lv_obj_t * obj, const char * txt)
+{
+    LV_LOG_INFO("begin");
+    lv_radiobtn_item_t * rb = (lv_radiobtn_item_t *)obj;
+#if LV_USE_ARABIC_PERSIAN_CHARS
+    size_t len = _lv_txt_ap_calc_bytes_cnt(txt);
+#else
+    size_t len = strlen(txt);
+#endif
+
+    if(!rb->static_txt) rb->txt = lv_mem_realloc(rb->txt, len + 1);
+    else  rb->txt = lv_mem_alloc(len + 1);
+#if LV_USE_ARABIC_PERSIAN_CHARS
+    _lv_txt_ap_proc(txt, rb->txt);
+#else
+    if(rb->txt != NULL) strncpy(rb->txt, txt, len + 1);
+#endif
+
+    rb->static_txt = 0;
+
+    lv_obj_refresh_self_size(obj);
+    lv_obj_invalidate(obj);
+}
+
+void lv_radiobtn_set_item_text_static(lv_obj_t * obj, const char * txt)
+{
+    lv_radiobtn_item_t * rb = (lv_radiobtn_item_t *)obj;
+
+    if(!rb->static_txt) lv_mem_free(rb->txt);
+
+    rb->txt = (char *)txt;
+    rb->static_txt = 1;
+
+    lv_obj_refresh_self_size(obj);
+    lv_obj_invalidate(obj);
+}
+
+/*=====================
+ * Getter functions
+ *====================*/
+
+lv_obj_t * lv_radiobtn_get_item(lv_obj_t * radiobtn, uint32_t index)
+{
+    LV_LOG_INFO("begin");
+    lv_obj_t * item = lv_obj_get_child(radiobtn, index);
+    return item;
+}
+
+const char * lv_radiobtn_get_item_text(lv_obj_t * radiobtn, lv_obj_t * obj)
+{
+    LV_UNUSED(radiobtn);
+
+    lv_radiobtn_item_t * rb = (lv_radiobtn_item_t *)obj;
+    return rb->txt;
+}
+
+uint32_t lv_radiobtn_get_item_num(lv_obj_t * radiobtn)
+{
+    uint32_t num = lv_obj_get_child_cnt(radiobtn);
+    return num;
+}
+
+/**********************
+ *   STATIC FUNCTIONS
+ **********************/
+static void lv_radiobtn_constructor(const lv_obj_class_t * class_p, lv_obj_t * obj)
+{
+    LV_UNUSED(class_p);
+    LV_TRACE_OBJ_CREATE("begin");
+
+    lv_radiobtn_t * rb = (lv_radiobtn_t *)obj;
+    rb->active_index = 0;
+    rb->checked_txt = "";
+    lv_obj_set_flex_flow(obj, LV_FLEX_FLOW_COLUMN);
+
+    LV_TRACE_OBJ_CREATE("finished");
+}
+
+static void lv_radiobtn_item_constructor(const lv_obj_class_t * class_p, lv_obj_t * obj)
+{
+    LV_UNUSED(class_p);
+    LV_TRACE_OBJ_CREATE("begin");
+
+    lv_radiobtn_item_t * btn = (lv_radiobtn_item_t *)obj;
+
+    btn->txt = "radio";
+    btn->static_txt = 1;
+    lv_obj_add_flag(obj, LV_OBJ_FLAG_CLICKABLE);
+    lv_obj_add_flag(obj, LV_OBJ_FLAG_CHECKABLE);
+    lv_obj_add_flag(obj, LV_OBJ_FLAG_SCROLL_ON_FOCUS);
+
+    LV_TRACE_OBJ_CREATE("finished");
+}
+
+static void lv_radiobtn_item_destructor(const lv_obj_class_t * class_p, lv_obj_t * obj)
+{
+    LV_UNUSED(class_p);
+    LV_TRACE_OBJ_CREATE("begin");
+
+    lv_radiobtn_item_t * btn = (lv_radiobtn_item_t *)obj;
+    if(!btn->static_txt) {
+        lv_mem_free(btn->txt);
+        btn->txt = NULL;
+    }
+    LV_TRACE_OBJ_CREATE("finished");
+}
+
+static void lv_radiobtn_item_event(const lv_obj_class_t * class_p, lv_event_t * e)
+{
+    LV_UNUSED(class_p);
+
+    lv_res_t res;
+    /*Call the ancestor's event handler*/
+    res = lv_obj_event_base(ITEM_CLASS, e);
+    if(res != LV_RES_OK) return;
+
+    lv_event_code_t code = lv_event_get_code(e);
+    lv_obj_t * obj = lv_event_get_target(e);
+
+    if(code == LV_EVENT_GET_SELF_SIZE) {
+        lv_point_t * p = lv_event_get_param(e);
+        lv_radiobtn_item_t * btn = (lv_radiobtn_item_t *)obj;
+
+        const lv_font_t * font = lv_obj_get_style_text_font(obj, LV_PART_MAIN);
+        lv_coord_t font_h = lv_font_get_line_height(font);
+        lv_coord_t line_space = lv_obj_get_style_text_line_space(obj, LV_PART_MAIN);
+        lv_coord_t letter_space = lv_obj_get_style_text_letter_space(obj, LV_PART_MAIN);
+
+        lv_point_t txt_size;
+        lv_txt_get_size(&txt_size, btn->txt, font, letter_space, line_space, LV_COORD_MAX, LV_TEXT_FLAG_NONE);
+
+        lv_coord_t bg_colp = lv_obj_get_style_pad_column(obj, LV_PART_MAIN);
+        lv_coord_t marker_leftp = lv_obj_get_style_pad_left(obj, LV_PART_INDICATOR);
+        lv_coord_t marker_rightp = lv_obj_get_style_pad_right(obj, LV_PART_INDICATOR);
+        lv_coord_t marker_topp = lv_obj_get_style_pad_top(obj, LV_PART_INDICATOR);
+        lv_coord_t marker_bottomp = lv_obj_get_style_pad_bottom(obj, LV_PART_INDICATOR);
+        lv_point_t marker_size;
+        marker_size.x = font_h + marker_leftp + marker_rightp;
+        marker_size.y = font_h + marker_topp + marker_bottomp;
+
+        p->x = marker_size.x + txt_size.x + bg_colp;
+        p->y = LV_MAX(marker_size.y, txt_size.y);
+    }
+    else if(code == LV_EVENT_REFR_EXT_DRAW_SIZE) {
+        lv_coord_t * s = lv_event_get_param(e);
+        lv_coord_t m = lv_obj_calculate_ext_draw_size(obj, LV_PART_INDICATOR);
+        *s = LV_MAX(*s, m);
+    }
+    else if(code == LV_EVENT_DRAW_MAIN) {
+        lv_radiobtn_item_draw(e);
+    }
+}
+
+static void lv_radiobtn_item_draw(lv_event_t * e)
+{
+    lv_obj_t * obj = lv_event_get_target(e);
+    lv_radiobtn_item_t * btn = (lv_radiobtn_item_t *)obj;
+
+    lv_draw_ctx_t * draw_ctx = lv_event_get_draw_ctx(e);
+    if(draw_ctx == NULL) return;
+    const lv_font_t * font = lv_obj_get_style_text_font(obj, LV_PART_MAIN);
+    lv_coord_t font_h = lv_font_get_line_height(font);
+
+    lv_coord_t bg_border = lv_obj_get_style_border_width(obj, LV_PART_MAIN);
+    lv_coord_t bg_topp = lv_obj_get_style_pad_top(obj, LV_PART_MAIN) + bg_border;
+    lv_coord_t bg_leftp = lv_obj_get_style_pad_left(obj, LV_PART_MAIN) + bg_border;
+    lv_coord_t bg_colp = lv_obj_get_style_pad_column(obj, LV_PART_MAIN);
+
+    lv_coord_t marker_leftp = lv_obj_get_style_pad_left(obj, LV_PART_INDICATOR);
+    lv_coord_t marker_rightp = lv_obj_get_style_pad_right(obj, LV_PART_INDICATOR);
+    lv_coord_t marker_topp = lv_obj_get_style_pad_top(obj, LV_PART_INDICATOR);
+    lv_coord_t marker_bottomp = lv_obj_get_style_pad_bottom(obj, LV_PART_INDICATOR);
+
+    lv_coord_t transf_w = lv_obj_get_style_transform_width(obj, LV_PART_INDICATOR);
+    lv_coord_t transf_h = lv_obj_get_style_transform_height(obj, LV_PART_INDICATOR);
+
+    lv_draw_rect_dsc_t indic_dsc;
+    lv_draw_rect_dsc_init(&indic_dsc);
+    lv_obj_init_draw_rect_dsc(obj, LV_PART_INDICATOR, &indic_dsc);
+    lv_area_t marker_area;
+    marker_area.x1 = obj->coords.x1 + bg_leftp;
+    marker_area.x2 = marker_area.x1 + font_h + marker_leftp + marker_rightp - 1;
+    marker_area.y1 = obj->coords.y1 + bg_topp;
+    marker_area.y2 = marker_area.y1 + font_h + marker_topp + marker_bottomp - 1;
+
+    lv_area_t marker_area_transf;
+    lv_area_copy(&marker_area_transf, &marker_area);
+    marker_area_transf.x1 -= transf_w;
+    marker_area_transf.x2 += transf_w;
+    marker_area_transf.y1 -= transf_h;
+    marker_area_transf.y2 += transf_h;
+
+    lv_obj_draw_part_dsc_t part_draw_dsc;
+    lv_obj_draw_dsc_init(&part_draw_dsc, draw_ctx);
+    part_draw_dsc.rect_dsc = &indic_dsc;
+    part_draw_dsc.class_p = ITEM_CLASS;
+    part_draw_dsc.type = LV_RADIOBTN_DRAW_PART_BOX;
+    part_draw_dsc.draw_area = &marker_area_transf;
+    part_draw_dsc.part = LV_PART_INDICATOR;
+
+    lv_event_send(obj, LV_EVENT_DRAW_PART_BEGIN, &part_draw_dsc);
+    lv_draw_rect(draw_ctx, &indic_dsc, &marker_area_transf);
+    lv_event_send(obj, LV_EVENT_DRAW_PART_END, &part_draw_dsc);
+
+    /**draw inner box*/
+    lv_draw_rect_dsc_t indic_inner_dsc;
+    lv_draw_rect_dsc_init(&indic_inner_dsc);
+    lv_obj_init_draw_rect_dsc(obj, LV_PART_CUSTOM_FIRST, &indic_inner_dsc);
+
+    lv_area_t marker_inner_area_transf;
+    lv_area_copy(&marker_inner_area_transf, &marker_area_transf);
+    int16_t dist = ((int16_t)(marker_inner_area_transf.x2 - marker_inner_area_transf.x1) / 4);
+    marker_inner_area_transf.x1 += dist;
+    marker_inner_area_transf.x2 -= dist;
+    marker_inner_area_transf.y1 += dist;
+    marker_inner_area_transf.y2 -= dist;
+
+    part_draw_dsc.rect_dsc = &indic_inner_dsc;
+    part_draw_dsc.class_p = ITEM_CLASS;
+    part_draw_dsc.type = LV_RADIOBTN_DRAW_PART_BOX_INNER;
+    part_draw_dsc.draw_area = &marker_inner_area_transf;
+    part_draw_dsc.part = LV_PART_CUSTOM_FIRST;
+
+    lv_event_send(obj, LV_EVENT_DRAW_PART_BEGIN, &part_draw_dsc);
+    lv_draw_rect(draw_ctx, &indic_inner_dsc, &marker_inner_area_transf);
+    lv_event_send(obj, LV_EVENT_DRAW_PART_END, &part_draw_dsc);
+
+    lv_coord_t line_space = lv_obj_get_style_text_line_space(obj, LV_PART_MAIN);
+    lv_coord_t letter_space = lv_obj_get_style_text_letter_space(obj, LV_PART_MAIN);
+
+    lv_point_t txt_size;
+    lv_txt_get_size(&txt_size, btn->txt, font, letter_space, line_space, LV_COORD_MAX, LV_TEXT_FLAG_NONE);
+
+    lv_draw_label_dsc_t txt_dsc;
+    lv_draw_label_dsc_init(&txt_dsc);
+    lv_obj_init_draw_label_dsc(obj, LV_PART_MAIN, &txt_dsc);
+
+    lv_coord_t y_ofs = (lv_area_get_height(&marker_area) - font_h) / 2;
+    lv_area_t txt_area;
+    txt_area.x1 = marker_area.x2 + bg_colp;
+    txt_area.x2 = txt_area.x1 + txt_size.x;
+    txt_area.y1 = obj->coords.y1 + bg_topp + y_ofs;
+    txt_area.y2 = txt_area.y1 + txt_size.y;
+
+    lv_draw_label(draw_ctx, &txt_dsc, &txt_area, btn->txt, NULL);
+}
+
+static void lv_radiobtn_event(const lv_obj_class_t * class_p, lv_event_t * e)
+{
+    LV_UNUSED(class_p);
+
+    lv_res_t res;
+    /*Call the ancestor's event handler*/
+    res = lv_obj_event_base(MY_CLASS, e);
+    if(res != LV_RES_OK) return;
+
+    lv_event_code_t code = lv_event_get_code(e);
+    lv_obj_t * obj = lv_event_get_current_target(e);
+    lv_obj_t * act_btn = lv_event_get_target(e);
+    if(code == LV_EVENT_CLICKED) {
+        lv_radiobtn_t * rb = (lv_radiobtn_t *)obj;
+        uint16_t active_id = rb->active_index;
+        lv_obj_t * old_btn = lv_radiobtn_get_item(obj, active_id);
+
+        if(act_btn == obj || old_btn == NULL) return;
+
+        lv_obj_clear_state(old_btn, LV_STATE_CHECKED);
+        lv_obj_add_state(act_btn, LV_STATE_CHECKED);
+
+        rb->active_index = lv_obj_get_index(act_btn);
+        rb->checked_txt = (char *)lv_radiobtn_get_item_text(obj, act_btn);
+
+        LV_LOG_USER("Selected radio buttons: %d ", (int)rb->active_index);
+    }
+
+}
+
+#endif /*LV_USE_RADIOBTN*/
diff --git a/src/extra/widgets/radiobtn/lv_radiobtn.h b/src/extra/widgets/radiobtn/lv_radiobtn.h
new file mode 100644
index 000000000..791612c04
--- /dev/null
+++ b/src/extra/widgets/radiobtn/lv_radiobtn.h
@@ -0,0 +1,137 @@
+/**
+ * @file lv_radiobtn.h
+ *
+ */
+
+#ifndef LV_RADIOBTN_H
+#define LV_RADIOBTN_H
+
+#ifdef __cplusplus
+extern "C" {
+#endif
+
+/*********************
+ *      INCLUDES
+ *********************/
+#include "../../../core/lv_obj.h"
+#include "../../layouts/flex/lv_flex.h"
+#include "../../../lv_conf_internal.h"
+
+#if LV_USE_RADIOBTN
+
+/*********************
+ *      DEFINES
+ *********************/
+
+/**********************
+ *      TYPEDEFS
+ **********************/
+typedef struct {
+    lv_obj_t obj;
+    char * checked_txt;
+    uint32_t active_index;
+} lv_radiobtn_t;
+
+typedef struct {
+    lv_obj_t obj;
+    char * txt;
+    uint32_t static_txt : 1;
+} lv_radiobtn_item_t;
+
+extern const lv_obj_class_t lv_radiobtn_class;
+extern const lv_obj_class_t lv_radiobtn_item_class;
+
+/**
+ * `type` field in `lv_obj_draw_part_dsc_t` if `class_p = lv_radiobtn_item_class`
+ * Used in `LV_EVENT_DRAW_PART_BEGIN` and `LV_EVENT_DRAW_PART_END`
+ */
+typedef enum {
+    LV_RADIOBTN_DRAW_PART_BOX,    /**< The tick box*/
+    LV_RADIOBTN_DRAW_PART_BOX_INNER, /**< The inner box of tich box*/
+} lv_radiobtn_draw_part_type_t;
+
+
+/**********************
+ * GLOBAL PROTOTYPES
+ **********************/
+
+/**
+ * Create a radio button object
+ * @param parent    pointer to an object, it will be the parent of radio button
+ * @return          pointer to the created radio button object
+ */
+lv_obj_t * lv_radiobtn_create(lv_obj_t * parent);
+
+/**
+ * Create a radiobtn item object like check box
+ * @param parent    pointer to an object, it will be the parent of the new button
+ * @return          pointer to the created radiobtn item
+ */
+lv_obj_t * lv_radiobtn_create_item(lv_obj_t * parent);
+
+/**
+ * Add new radiobtn item into radio button with text
+ * @param radiobtn  pointer to an object, it will be the parent of new radiobtn item
+ * @param txt       the text of new radiobtn item
+ * @return          pointer to created new radiobtn item
+ */
+lv_obj_t * lv_radiobtn_add_item(lv_obj_t * radiobtn, const char * txt);
+
+/*=====================
+ * Setter functions
+ *====================*/
+
+/**
+ * Set new text of a radiobtn item
+ * @param obj       pointer to an object, it is a radiobtn item to set text
+ * @param txt       the new text that you want to set for radiobtn item
+ * @return          pointer to chenged radiobtn item
+ */
+void lv_radiobtn_set_item_text(lv_obj_t * obj, const char * txt);
+
+/**
+ * Set the text of a radiobtn item. `txt` must not be deallocated during the life
+ * of this radiobtn.
+ * @param obj       pointer to an object, it is a radiobtn item to set static text
+ * @param txt       the static text of the radiobtn item.
+ */
+void lv_radiobtn_set_item_text_static(lv_obj_t * obj, const char * txt);
+
+/*=====================
+ * Getter functions
+ *====================*/
+
+/**
+ * Get radiobtn item from radio bytton by index
+ * @param radiobtn  pointer to an object, it's parent of the wanted radiobtn item
+ * @param index     the index of radiobtn item in radio button
+ * @return          pointer to the wanted radiobtn item
+ */
+lv_obj_t * lv_radiobtn_get_item(lv_obj_t * radiobtn, uint32_t index);
+
+/**
+ * Get the text of a radiobtn item
+ * @param radiobtn  pointer to an object, it is the parent of radiobtn item
+ * @param obj      pointer to an object, it is a radiobtn item to get text
+ * @return          the text of specified radiobtn item
+ */
+const char * lv_radiobtn_get_item_text(lv_obj_t * radiobtn, lv_obj_t * obj);
+
+/**
+ * Get the item number of a radio button
+ * @param radiobtn  pointer to an object, it is a radio button to get item number
+ * @return          the item number of radio button
+ */
+uint32_t lv_radiobtn_get_item_num(lv_obj_t * radiobtn);
+
+/**********************
+ *      MACROS
+ **********************/
+
+#endif /*LV_USE_RADIOBTN*/
+
+#ifdef __cplusplus
+} /*extern "C"*/
+#endif
+
+#endif /*LV_RADIOBTN_H*/
diff --git a/src/extra/widgets/textprogress/lv_textprogress.c b/src/extra/widgets/textprogress/lv_textprogress.c
new file mode 100644
index 000000000..c117ee7e6
--- /dev/null
+++ b/src/extra/widgets/textprogress/lv_textprogress.c
@@ -0,0 +1,140 @@
+/**
+ * @file lv_textprogress.c
+ *
+ */
+
+/*********************
+ *      INCLUDES
+ *********************/
+#include "lv_textprogress.h"
+
+#if LV_USE_TEXTPROGRESS != 0
+
+/*********************
+ *      DEFINES
+ *********************/
+#define MY_CLASS &lv_textprogress_class
+
+#define MULDIV(a,b,c) (a*b)/c
+
+/**********************
+ *      TYPEDEFS
+ **********************/
+
+/**********************
+ *  STATIC PROTOTYPES
+ **********************/
+static void lv_textprogress_constructor(const lv_obj_class_t * class_p, lv_obj_t * obj);
+
+/**********************
+ *  STATIC VARIABLES
+ **********************/
+
+const lv_obj_class_t lv_textprogress_class = {
+    .constructor_cb = lv_textprogress_constructor,
+    .instance_size = sizeof(lv_textprogress_t),
+    .base_class = &lv_label_class
+};
+
+/**********************
+ *      MACROS
+ **********************/
+
+/**********************
+ *   GLOBAL FUNCTIONS
+ **********************/
+
+lv_obj_t * lv_textprogress_create(lv_obj_t * parent)
+{
+    LV_LOG_INFO("begin");
+    lv_obj_t * obj = lv_obj_class_create_obj(MY_CLASS, parent);
+    lv_obj_class_init_obj(obj);
+
+    return obj;
+}
+
+/*=====================
+ * Setter functions
+ *====================*/
+
+void lv_textprogress_set_range_value(lv_obj_t * obj, uint32_t min, uint32_t max, uint32_t steps, uint32_t steps_min)
+{
+    lv_textprogress_t * tp = (lv_textprogress_t *)obj;
+
+    tp->range_min = min;
+    tp->range_max = max;
+    tp->range_steps_min = steps_min;
+    if(steps == 0) {
+        tp->range_steps = max - min;
+    }
+    else {
+        tp->range_steps = steps;
+    }
+}
+
+void lv_textprogress_set_decimal(lv_obj_t * obj, uint32_t decimal)
+{
+    lv_textprogress_t * tp = (lv_textprogress_t *)obj;
+
+    tp->decimals = (((2) < (decimal)) ? (2) : (decimal));
+}
+
+void lv_textprogress_set_value(lv_obj_t * obj, uint32_t value)
+{
+    lv_textprogress_t * tp = (lv_textprogress_t *)obj;
+
+    value = (((value) > (tp->range_min)) ? (value) : (tp->range_min));
+    value = (((value) < (tp->range_max)) ? (value) : (tp->range_max));
+    tp->value = value;
+    int range[3] = {1, 10, 100};
+    uint32_t progress;
+    char textbuffer[8];
+    progress = lv_textprogress_get_progress(obj, 100 * range[tp->decimals]);
+    if(tp->decimals > 0) {
+        lv_snprintf(textbuffer, sizeof(textbuffer), "%d.%0*d%%", (progress / range[tp->decimals]), tp->decimals,
+                    (progress % range[tp->decimals]));
+    }
+    else {
+        lv_snprintf(textbuffer, sizeof(textbuffer), "%d%%", progress);
+    }
+    lv_label_set_text(obj, textbuffer);
+}
+/*=====================
+ * Getter functions
+ *====================*/
+
+uint32_t lv_textprogress_get_progress(lv_obj_t * obj, uint32_t range)
+{
+    lv_textprogress_t * tp = (lv_textprogress_t *)obj;
+
+    int32_t step, prog;
+    step = tp->range_steps_min + MULDIV(((tp->value) - (tp->range_min)), ((tp->range_steps) - (tp->range_steps_min)),
+                                        ((tp->range_max) - (tp->range_min)));
+    prog = MULDIV(step, range, tp->range_steps);
+    return prog;
+}
+
+/**********************
+ *   STATIC FUNCTIONS
+ **********************/
+
+static void lv_textprogress_constructor(const lv_obj_class_t * class_p, lv_obj_t * obj)
+{
+    LV_UNUSED(class_p);
+    LV_TRACE_OBJ_CREATE("begin");
+
+    lv_textprogress_t * tp = (lv_textprogress_t *)obj;
+    tp->range_min = 0;
+    tp->range_max = 100;
+    tp->range_steps = 80;
+    tp->range_steps_min = 0;
+    tp->value = 0;
+    tp->decimals = 2;
+
+    lv_label_set_text(obj, "0.00%");
+
+    LV_TRACE_OBJ_CREATE("finished");
+}
+
+
+#endif /*LV_USE_TEXTPROGRESS*/
\ No newline at end of file
diff --git a/src/extra/widgets/textprogress/lv_textprogress.h b/src/extra/widgets/textprogress/lv_textprogress.h
new file mode 100644
index 000000000..9251620b0
--- /dev/null
+++ b/src/extra/widgets/textprogress/lv_textprogress.h
@@ -0,0 +1,100 @@
+/**
+ * @file lv_textprogress.h
+ *
+ */
+
+#ifndef LV_TEXTPROGRESS_H
+#define LV_TEXTPROGRESS_H
+
+#ifdef __cplusplus
+extern "C" {
+#endif
+
+/*********************
+ *      INCLUDES
+ *********************/
+#include "../../../lvgl.h"
+
+#if LV_USE_TEXTPROGRESS
+/*********************
+ *      DEFINES
+ *********************/
+
+/**********************
+ *      TYPEDEFS
+ **********************/
+typedef struct {
+    lv_label_t text;
+    uint32_t range_max;
+    uint32_t range_min;
+    uint32_t value;
+    uint32_t range_steps;
+    uint32_t range_steps_min;
+    uint16_t decimals;
+} lv_textprogress_t;
+
+extern const lv_obj_class_t lv_textprogress_class;
+
+/**********************
+ * GLOBAL PROTOTYPES
+ **********************/
+
+/**
+ * Create a textprogress object
+ * @param parent    pointer to an object, it will be the parent of textprogress
+ * @return          pointer to the created textprogress object
+ */
+lv_obj_t * lv_textprogress_create(lv_obj_t * parent);
+
+/*=====================
+ * Setter functions
+ *====================*/
+
+/**
+ * Set range value of a textprogress object
+ * @param obj       pointer to an object, it is a textprogress to set range value
+ * @param min       the minimum value of range
+ * @param max       the maximum value of range
+ * @param steps     the number of steps from min to max range
+ * @param steps_min the minimum steps of textprogress
+ * @return          pointer to chenged textprogress
+ */
+void lv_textprogress_set_range_value(lv_obj_t * obj, uint32_t min, uint32_t max, uint32_t steps, uint32_t steps_min);
+
+/**
+ * Set decimal place of a textprogress object
+ * @param obj       pointer to an object, it is a textprogress to set decimal place
+ * @param decimal   the decimal place of the textprogress
+ * @return          pointer to chenged textprogress
+ */
+void lv_textprogress_set_decimal(lv_obj_t * obj, uint32_t decimal);
+
+/**
+ * Set current value of a textprogress object
+ * @param obj       pointer to an object, it is a textprogress to set current value
+ * @param value     the value want to set for the textprogress
+ * @return          pointer to chenged textprogress
+ */
+void lv_textprogress_set_value(lv_obj_t * obj, uint32_t value);
+
+
+/*=====================
+ * Getter functions
+ *====================*/
+
+/**
+ * Get each step's progress of a textprogress object
+ * @param radiobtn  pointer to a textprogress object
+ * @param range     the range of textprogress object
+ * @return          the progresss number of textprogress
+ */
+uint32_t lv_textprogress_get_progress(lv_obj_t * obj, uint32_t range);
+
+
+#endif /*LV_USE_TEXTPROGRESS*/
+
+#ifdef __cplusplus
+} /*extern "C"*/
+#endif
+
+#endif /*LV_TEXTPROGRESS_H*/
\ No newline at end of file
diff --git a/src/extra/widgets/video/lv_video.c b/src/extra/widgets/video/lv_video.c
new file mode 100644
index 000000000..5288d5429
--- /dev/null
+++ b/src/extra/widgets/video/lv_video.c
@@ -0,0 +1,602 @@
+/**
+ * @file lv_video.c
+ *
+ */
+
+/*********************
+ *      INCLUDES
+ *********************/
+
+#include "lv_video.h"
+
+#if LV_USE_VIDEO != 0
+
+#include <stdio.h>
+#include <stdlib.h>
+#include <string.h>
+
+// for run on target function.
+#if !LV_USE_GUIDER_SIMULATOR
+    #include "FreeRTOS.h"
+    #include "task.h"
+    #include "sdcard.h"
+    #include "h264_dec.h"
+    #include "fsl_common.h"
+    #include "fsl_debug_console.h"
+    #include "fsl_pxp.h"
+    #include "fsl_cache.h"
+#else
+    #include "decoder.h"
+    #include <unistd.h>
+#endif
+
+/*********************
+ *      DEFINES
+ *********************/
+#define MY_CLASS &lv_video_class
+#define LV_COLOR_SIZE 16
+#ifndef DEMO_FILE_BUF_SIZE
+    #define DEMO_FILE_BUF_SIZE (16 * 1024)
+#endif
+#ifndef DEMO_DECODE_BUF_SIZE
+    #define DEMO_DECODE_BUF_SIZE (64 * 1024)
+#endif
+
+#pragma GCC diagnostic ignored "-Wdeprecated-declarations"
+
+
+/**********************
+ *      TYPEDEFS
+ **********************/
+
+/**********************
+ *  STATIC PROTOTYPES
+ **********************/
+
+static int16_t video_width = 0;
+static int16_t video_height = 0;
+static void lv_video_constructor(const lv_obj_class_t * class_p, lv_obj_t * obj);
+static void lv_video_destructor(const lv_obj_class_t * class_p, lv_obj_t * obj);
+
+#if !LV_USE_GUIDER_SIMULATOR
+/* PXP Usage */
+
+static void PXP_DisplayFrame(uint16_t width,
+                             uint16_t height,
+                             const uint8_t * Y,
+                             const uint8_t * U,
+                             const uint8_t * V,
+                             uint32_t Y_Stride,
+                             uint32_t UV_Stride,
+                             lv_obj_t * obj);
+#else
+static void yuv420pToRgb565(int oriWidth,
+                            int oriHeight,
+                            int width, int height,
+                            uint8_t * py, uint8_t * pu, uint8_t * pv,
+                            unsigned short * dst);
+static void CPU_DisplayFrame(SBufferInfo sDstBufInfo, unsigned char ** dst, lv_obj_t * obj);
+#endif
+/**********************
+ *  STATIC VARIABLES
+ **********************/
+const lv_obj_class_t lv_video_class = {
+    .base_class = &lv_img_class,
+    .instance_size = sizeof(lv_video_t),
+    .width_def = LV_SIZE_CONTENT,
+    .height_def = LV_SIZE_CONTENT,
+    .constructor_cb = lv_video_constructor,
+    .destructor_cb = lv_video_destructor,
+};
+
+#if !LV_USE_GUIDER_SIMULATOR
+    /* PXP Usage */
+    static pxp_output_buffer_config_t s_pxpOutputBufferConfig;
+    static pxp_ps_buffer_config_t s_pxpPsBufferConfig;
+    static volatile uint8_t s_lcdActiveFbIdx = 0;
+    static int buffer_byte_per_pixel = 2;
+    static uint8_t * s_lcdBuffer[2];
+#else
+    static int mallocInit = 0;
+    static uint8_t * rgb, *py, *pu, *pv;
+#endif
+
+static uint8_t s_decodeBuf[DEMO_DECODE_BUF_SIZE];
+
+/**********************
+ *      MACROS
+ **********************/
+
+/**********************
+ *   GLOBAL FUNCTIONS
+ **********************/
+
+/**
+ * Create an image button object
+ * @param parent pointer to an object, it will be the parent of the new image button
+ * @return pointer to the created image button
+ */
+lv_obj_t * lv_video_create(lv_obj_t * parent, int widgetWidth, int widgetHeight)
+{
+    LV_LOG_INFO("begin");
+
+    video_width = widgetWidth;
+    video_height = widgetHeight;
+
+#if !LV_USE_GUIDER_SIMULATOR
+    s_lcdBuffer[0] = malloc(widgetWidth * widgetHeight * buffer_byte_per_pixel);
+    s_lcdBuffer[1] = malloc(widgetWidth * widgetHeight * buffer_byte_per_pixel);
+#endif
+
+    lv_obj_t * obj = lv_obj_class_create_obj(MY_CLASS, parent);
+    lv_obj_class_init_obj(obj);
+    return obj;
+}
+
+void lv_video_play(lv_obj_t * obj)
+{
+    lv_video_t * video = (lv_video_t *)obj;
+    Read_HFile(video->file_name, obj);
+}
+
+static int search_nalu(const uint8_t * data, int32_t len)
+{
+    int i;
+    /* parse NALU 00 00 00 01 or 00 00 01 */
+    for(i = 1; i < len - 4; i++) {
+        if((data[i] == 0 && data[i + 1] == 0 && data[i + 2] == 0 && data[i + 3] == 1) ||
+           (data[i] == 0 && data[i + 1] == 0 && data[i + 2] == 1)) {
+            return i;
+        }
+    }
+
+    if(data[i] == 0 && data[i + 1] == 0 && data[i + 2] == 1) {
+        return i;
+    }
+
+    return -1;
+}
+
+int Decoder_Data(const uint8_t * data, int len, bool isStartOfFile, bool isEndOfFile, lv_obj_t * obj)
+{
+    lv_video_t * video = (lv_video_t *)obj;
+    SBufferInfo sDstBufInfo = {0};
+    int32_t copiedLen = 0;
+    int32_t sliceSize = 0;
+    uint8_t * dst[3];
+    int32_t num_of_frames_in_buffer = 0;
+    int32_t leftDataLen;
+
+    static int32_t decodeBufStart = 0;
+    static int32_t decodeBufEnd   = 0;
+
+    if(isStartOfFile) {
+        decodeBufStart         = 0;
+        decodeBufEnd           = 0;
+    }
+    leftDataLen = len;
+
+    while(leftDataLen > 0) {
+        copiedLen = MIN(leftDataLen, ((int32_t)sizeof(s_decodeBuf) - decodeBufEnd));
+
+        /* Copy the input data to the end of decode buffer. */
+        memcpy(&s_decodeBuf[decodeBufEnd], data, copiedLen);
+        decodeBufEnd += copiedLen;
+        data += copiedLen;
+        leftDataLen -= copiedLen;
+
+        while(decodeBufStart < decodeBufEnd) {
+            sliceSize = search_nalu(&s_decodeBuf[decodeBufStart], decodeBufEnd - decodeBufStart);
+            if(video->exist) {
+                return 0;
+            }
+            /* Could not find NALU. */
+            if(sliceSize < 0) {
+                /* This is the file end part, pass them all to H264 decoder. */
+                if(isEndOfFile && (0 == leftDataLen)) {
+                    sliceSize = decodeBufEnd - decodeBufStart;
+                }
+                else {
+                    /* After searching the full buffer, no slice found, then drop the data in buffer. */
+                    if((decodeBufStart == 0) && (decodeBufEnd == sizeof(s_decodeBuf))) {
+                        /* Drop the decode buffer, fill using left input data. */
+                        decodeBufEnd   = 0;
+                        decodeBufStart = 0;
+                    }
+
+                    /* Have processed all slice in the buffer. */
+                    break;
+                }
+            }
+            /* Slice size too small, skip it. */
+            else if(sliceSize < 4) {
+                decodeBufStart += sliceSize;
+                continue;
+            }
+            /* Found NALU, decode. */
+            if(OpenH264_Decode(&s_decodeBuf[decodeBufStart], sliceSize, dst, &sDstBufInfo) == 0) {
+                if(sDstBufInfo.iBufferStatus == 1) {
+#if LV_USE_GUIDER_SIMULATOR
+                    CPU_DisplayFrame(sDstBufInfo, dst, obj);
+#else
+                    PXP_DisplayFrame(
+                        sDstBufInfo.UsrData.sSystemBuffer.iWidth, sDstBufInfo.UsrData.sSystemBuffer.iHeight,
+                        sDstBufInfo.pDst[0], sDstBufInfo.pDst[1], sDstBufInfo.pDst[2],
+                        sDstBufInfo.UsrData.sSystemBuffer.iStride[0], sDstBufInfo.UsrData.sSystemBuffer.iStride[1], obj);
+#endif
+                }
+
+            }
+            else {
+                LV_LOG_ERROR("decode error\r\n");
+            }
+
+            decodeBufStart += sliceSize;
+        }
+
+        /* Move the left data in decode buffer to the start of decode buffer,
+         * left input data will be appended to the end.
+         */
+        memcpy(s_decodeBuf, &s_decodeBuf[decodeBufStart], decodeBufEnd - decodeBufStart);
+        decodeBufEnd -= decodeBufStart;
+        decodeBufStart = 0;
+    }
+
+    if(isEndOfFile) {
+        OpenH264_GetOption(&num_of_frames_in_buffer);
+        for(int32_t i = 0; i < num_of_frames_in_buffer; i++) {
+            dst[0] = NULL;
+            dst[1] = NULL;
+            dst[2] = NULL;
+
+            OpenH264_FlashFrame(dst, &sDstBufInfo);
+            if(sDstBufInfo.iBufferStatus == 1) {
+#if LV_USE_GUIDER_SIMULATOR
+                CPU_DisplayFrame(sDstBufInfo, dst, obj);
+#else
+                PXP_DisplayFrame(
+                    sDstBufInfo.UsrData.sSystemBuffer.iWidth, sDstBufInfo.UsrData.sSystemBuffer.iHeight,
+                    sDstBufInfo.pDst[0], sDstBufInfo.pDst[1], sDstBufInfo.pDst[2],
+                    sDstBufInfo.UsrData.sSystemBuffer.iStride[0], sDstBufInfo.UsrData.sSystemBuffer.iStride[1], obj);
+#endif
+            }
+        }
+    }
+
+    return 0;
+}
+
+void Read_HFile(const char * fileName, lv_obj_t * obj)
+{
+    lv_video_t * video = (lv_video_t *)obj;
+    uint32_t bytesRead;
+    while(1) {
+        int error = lv_fs_open(&video->h264File, video->file_name, LV_FS_MODE_RD);
+        if(error != LV_FS_RES_OK) {
+            break;
+        }
+        video->fileStart = true;
+        while(1) {
+            if(video->play_status == 1) {
+                error = lv_fs_read(&video->h264File, video->blk.data, DEMO_FILE_BUF_SIZE, &bytesRead);
+                if(error != LV_FS_RES_OK) {
+                    break;
+                }
+                video->blk.len           = bytesRead;
+                video->blk.isEndOfFile   = (DEMO_FILE_BUF_SIZE > bytesRead);
+                video->blk.isStartOfFile = video->fileStart;
+                video->fileStart         = false;
+                Decoder_Data(video->blk.data, video->blk.len, video->blk.isStartOfFile, video->blk.isEndOfFile, obj);
+                if(video->blk.isEndOfFile) {
+                    break;
+                }
+                if(video->exist) {
+                    video->blk.data = NULL;
+                    lv_fs_close(&video->h264File);
+                    return;
+                }
+            }
+            else {
+
+#if LV_USE_GUIDER_SIMULATOR
+                lv_task_handler();
+                usleep(5 * 1000);
+#else
+                vTaskDelay(5);
+#endif
+                continue;
+            }
+        }
+        lv_fs_close(&video->h264File);
+    }
+}
+
+#if !LV_USE_GUIDER_SIMULATOR
+int Video_InitPXP()
+{
+    /* Initialize variables. */
+
+
+    memset(&s_pxpPsBufferConfig, 0, sizeof(s_pxpPsBufferConfig));
+    memset(&s_pxpOutputBufferConfig, 0, sizeof(s_pxpOutputBufferConfig));
+    memset(s_lcdBuffer[0], 0, video_width * video_height * buffer_byte_per_pixel);
+    memset(s_lcdBuffer[1], 0, video_width * video_height * buffer_byte_per_pixel);
+    s_pxpPsBufferConfig.pixelFormat = kPXP_PsPixelFormatYVU420;
+    s_pxpPsBufferConfig.swapByte    = false,
+
+    s_pxpOutputBufferConfig.pixelFormat    = kPXP_OutputPixelFormatRGB565;
+    s_pxpOutputBufferConfig.interlacedMode = kPXP_OutputProgressive;
+    s_pxpOutputBufferConfig.buffer1Addr    = 0U,
+    s_pxpOutputBufferConfig.pitchBytes     = (video_width * buffer_byte_per_pixel);
+
+    /* Initialize hardware. */
+    PXP_Init(PXP);
+
+    PXP_SetProcessSurfaceBackGroundColor(PXP, 0U);
+
+    /* Disable AS. */
+    PXP_SetAlphaSurfacePosition(PXP, 0xFFFFU, 0xFFFFU, 0U, 0U);
+
+    PXP_SetCsc1Mode(PXP, kPXP_Csc1YCbCr2RGB);
+    PXP_EnableCsc1(PXP, true);
+
+    return 0;
+}
+
+static void PXP_DisplayFrame(uint16_t width,
+                             uint16_t height,
+                             const uint8_t * Y,
+                             const uint8_t * U,
+                             const uint8_t * V,
+                             uint32_t Y_Stride,
+                             uint32_t UV_Stride,
+                             lv_obj_t * obj)
+{
+    lv_video_t * video = (lv_video_t *)obj;
+    void * lcdFrameAddr;
+    bool rotate                    = false;
+    static uint16_t oldInputWidth  = 0U;
+    static uint16_t oldInputHeight = 0U;
+
+    uint16_t lcdWidth  = video_width;
+    uint16_t lcdHeight = video_height;
+
+    DCACHE_CleanInvalidateByRange((uint32_t)Y, height * Y_Stride);
+    DCACHE_CleanInvalidateByRange((uint32_t)U, height * UV_Stride / 2);
+    DCACHE_CleanInvalidateByRange((uint32_t)V, height * UV_Stride / 2);
+
+    /* PS configure. */
+    s_pxpPsBufferConfig.bufferAddr  = (uint32_t)Y;
+    s_pxpPsBufferConfig.bufferAddrU = (uint32_t)U;
+    s_pxpPsBufferConfig.bufferAddrV = (uint32_t)V;
+    s_pxpPsBufferConfig.pitchBytes  = Y_Stride;
+
+    PXP_SetProcessSurfaceBufferConfig(PXP, &s_pxpPsBufferConfig);
+
+    /* Input frame size changed. */
+    if((oldInputHeight != height) || (oldInputWidth != width)) {
+
+        rotate = (height > width);
+
+        if(rotate) {
+            s_pxpOutputBufferConfig.width  = lcdHeight;
+            s_pxpOutputBufferConfig.height = lcdWidth;
+
+            PXP_SetRotateConfig(PXP, kPXP_RotateOutputBuffer, kPXP_Rotate90, kPXP_FlipDisable);
+            PXP_SetProcessSurfaceScaler(PXP, width, height, lcdHeight, lcdWidth);
+            PXP_SetProcessSurfacePosition(PXP, 0, 0, lcdHeight - 1, lcdWidth - 1);
+        }
+        else {
+            s_pxpOutputBufferConfig.width  = lcdWidth;
+            s_pxpOutputBufferConfig.height = lcdHeight;
+
+            PXP_SetProcessSurfaceScaler(PXP, width, height, lcdWidth, lcdHeight);
+            PXP_SetProcessSurfacePosition(PXP, 0, 0, lcdWidth - 1, lcdHeight - 1);
+        }
+
+        oldInputHeight = height;
+        oldInputWidth  = width;
+    }
+
+    lcdFrameAddr                              = s_lcdBuffer[s_lcdActiveFbIdx ^ 1];
+    s_pxpOutputBufferConfig.buffer0Addr = (uint32_t)lcdFrameAddr;
+
+    PXP_SetOutputBufferConfig(PXP, &s_pxpOutputBufferConfig);
+    PXP_Start(PXP);
+
+    while(0U == (kPXP_CompleteFlag & PXP_GetStatusFlags(PXP))) {
+    }
+    PXP_ClearStatusFlags(PXP, kPXP_CompleteFlag);
+
+    video->frameImage.data = lcdFrameAddr;
+    s_lcdActiveFbIdx ^= 1;
+    lv_img_set_src(obj, &video->frameImage);
+}
+#else
+static void CPU_DisplayFrame(SBufferInfo sDstBufInfo, unsigned char ** dst, lv_obj_t * obj)
+{
+    lv_video_t * video = (lv_video_t *)obj;
+    int width = 0;
+    int height = 0;
+    if(sDstBufInfo.iBufferStatus == 1) {
+        width = sDstBufInfo.UsrData.sSystemBuffer.iWidth;
+        height = sDstBufInfo.UsrData.sSystemBuffer.iHeight;
+        int YStride = sDstBufInfo.UsrData.sSystemBuffer.iStride[0];
+        int UVStride = sDstBufInfo.UsrData.sSystemBuffer.iStride[1];
+        unsigned char * pPtr = NULL;
+        if(mallocInit == 0) {
+            rgb = malloc(2 * width * height);
+            py = malloc(width * height);
+            pu = malloc(width * height / 4);
+            pv = malloc(width * height / 4);
+            mallocInit = 1;
+        }
+        pPtr = dst[0];
+        for(int i = 0; i < height; i++) {
+            memcpy(py + i * width, pPtr, width);
+            pPtr += YStride;
+        }
+
+        height = height / 2;
+        width = width / 2;
+        pPtr = dst[1];
+        for(int i = 0; i < height; i++) {
+            memcpy(pu + i * width, pPtr, width);
+            pPtr += UVStride;
+        }
+        pPtr = dst[2];
+        for(int i = 0; i < height; i++) {
+            memcpy(pv + i * width, pPtr, width);
+            pPtr += UVStride;
+        }
+        yuv420pToRgb565(width * 2, height * 2, video_width, video_height, py, pu, pv, (uint16_t *)rgb);
+        // update the image data.
+        video->frameImage.data = rgb;
+        lv_img_set_src(obj, &video->frameImage);
+        lv_img_cache_invalidate_src(lv_img_get_src(obj));
+        lv_obj_invalidate(obj);
+        lv_task_handler();
+        usleep(30 * 1000);
+    }
+}
+
+static void yuv420pToRgb565(int oriWidth, int oriHeight, int width, int height, uint8_t * py, uint8_t * pu,
+                            uint8_t * pv, unsigned short * dst)
+{
+    int y, u, v, yy, vr, ug, vg, ub;
+    int r, g, b;
+    int oriLine, oriCol;
+
+    for(int i = 0; i < height; i++) {
+        oriLine = i * oriHeight / height;
+        for(int j = 0; j < width; j++) {
+            oriCol = j * oriWidth / width;
+            y = py[oriLine * oriWidth + oriCol];
+            yy = y << 8;
+            u = pu[(oriLine / 2) * (oriWidth / 2) + (oriCol / 2)] - 128;
+            ug = 88 * u;
+            ub = 454 * u;
+            v = pv[(oriLine / 2) * (oriWidth / 2) + (oriCol / 2)] - 128;
+            vg = 183 * v;
+            vr = 359 * v;
+
+            r = (yy + vr) >> 8;
+            g = (yy - ug - vg) >> 8;
+            b = (yy + ub) >> 8;
+            if(r < 0)
+                r = 0;
+            if(r > 255)
+                r = 255;
+            if(g < 0)
+                g = 0;
+            if(g > 255)
+                g = 255;
+            if(b < 0)
+                b = 0;
+            if(b > 255)
+                b = 255;
+            dst[i * width + j] = (((unsigned short)r >> 3) << 11) | (((unsigned short)g >> 2) << 5) | (((
+                                                                                                            unsigned short)b >> 3) << 0);
+        }
+    }
+}
+#endif
+
+/*=====================
+ * Setter functions
+ *====================*/
+
+void lv_video_set_src(lv_obj_t * obj, const char * src)
+{
+    LV_ASSERT_OBJ(obj, MY_CLASS);
+
+    lv_video_t * video = (lv_video_t *)obj;
+
+    video->file_name = src;
+}
+
+void lv_video_set_status(lv_obj_t * obj, int status)
+{
+    LV_ASSERT_OBJ(obj, MY_CLASS);
+
+    lv_video_t * video = (lv_video_t *)obj;
+
+    video->play_status = status;
+}
+
+/*=====================
+ * Getter functions
+ *====================*/
+int lv_video_get_status(lv_obj_t * obj)
+{
+    LV_ASSERT_OBJ(obj, MY_CLASS);
+
+    lv_video_t * video = (lv_video_t *)obj;
+
+    return video->play_status;
+}
+
+
+const char * lv_video_get_src(lv_obj_t * obj)
+{
+    LV_ASSERT_OBJ(obj, MY_CLASS);
+
+    lv_video_t * video = (lv_video_t *)obj;
+
+    return video->file_name;
+}
+
+
+/**********************
+ *   STATIC FUNCTIONS
+ **********************/
+
+static void lv_video_constructor(const lv_obj_class_t * class_p, lv_obj_t * obj)
+{
+    LV_UNUSED(class_p);
+    lv_video_t * video = (lv_video_t *)obj;
+
+    video->play_status = 1;
+    video->exist = false;
+    OpenH264_Init();
+    video->frameImage.header.always_zero = 0;
+    video->frameImage.header.cf = LV_IMG_CF_TRUE_COLOR;
+    video->frameImage.header.w = video_width;
+    video->frameImage.header.h = video_height;
+    video->frameImage.data_size = video_width * video_height * LV_COLOR_SIZE / 8;
+    video->blk.data = (uint8_t *)malloc(DEMO_FILE_BUF_SIZE + 4);
+    video->frameImage.data = NULL;
+    video->fileStart = true;
+    LV_TRACE_OBJ_CREATE("finished");
+    lv_obj_update_layout(obj);
+}
+
+static void lv_video_destructor(const lv_obj_class_t * class_p, lv_obj_t * obj)
+{
+    LV_UNUSED(class_p);
+    lv_video_t * video = (lv_video_t *)obj;
+    video->exist = true;
+    if(video->blk.data) {
+        free(video->blk.data);
+        video->blk.data = NULL;
+    }
+
+#if LV_USE_GUIDER_SIMULATOR
+    mallocInit = 0;
+    if(rgb) {
+        free(py);
+        free(pu);
+        free(pv);
+        free(rgb);
+    }
+#else
+    if(s_lcdBuffer[0] != NULL && s_lcdBuffer[1] != NULL) {
+        free(s_lcdBuffer[0]);
+        free(s_lcdBuffer[1]);
+        s_lcdBuffer[0] = NULL;
+        s_lcdBuffer[1] = NULL;
+    }
+#endif
+    lv_img_cache_invalidate_src(lv_img_get_src(obj));
+}
+#endif
diff --git a/src/extra/widgets/video/lv_video.h b/src/extra/widgets/video/lv_video.h
new file mode 100644
index 000000000..b13af20d7
--- /dev/null
+++ b/src/extra/widgets/video/lv_video.h
@@ -0,0 +1,114 @@
+/**
+ * @file lv_video.h
+ *
+ */
+
+#ifndef LV_VIDEO_H
+#define LV_VIDEO_H
+#if !defined(MIN)
+    #define MIN(a, b) (((a) < (b)) ? (a) : (b))
+#endif
+
+#ifdef __cplusplus
+extern "C" {
+#endif
+
+/*********************
+ *      INCLUDES
+ *********************/
+#include "../../../lvgl.h"
+
+#if LV_USE_VIDEO != 0
+
+/*********************
+ *      DEFINES
+ *********************/
+
+/**********************
+ *      TYPEDEFS
+ **********************/
+
+typedef struct {
+    uint8_t * data;     /* Pointer to data. */
+    uint32_t len;        /* Length of the data. */
+    bool isEndOfFile;   /* Has reached file end. */
+    bool isStartOfFile; /* Is start of file. */
+} file_data_block_t;
+
+typedef struct {
+    lv_obj_t obj;
+    const void * src; /*Image source: Pointer to an array or a file or a symbol*/
+    lv_point_t offset;
+    lv_coord_t w;          /*Width of the image (Handled by the library)*/
+    lv_coord_t h;          /*Height of the image (Handled by the library)*/
+    uint16_t angle;    /*rotation angle of the image*/
+    lv_point_t pivot;     /*rotation center of the image*/
+    uint16_t zoom;         /*256 means no zoom, 512 double size, 128 half size*/
+    uint8_t src_type : 2;  /*See: lv_img_src_t*/
+    uint8_t cf : 5;        /*Color format from `lv_img_color_format_t`*/
+    uint8_t antialias : 1; /*Apply anti-aliasing in transformations (rotate, zoom)*/
+    uint8_t obj_size_mode: 2; /*Image size mode when image size and object size is different.*/
+    int play_status;
+    const char * file_name;
+    lv_img_dsc_t frameImage;
+    lv_fs_file_t h264File;
+    file_data_block_t blk;
+    bool  fileStart;
+    bool exist;
+} lv_video_t;
+
+extern const lv_obj_class_t lv_video_class;
+
+/**********************
+ * GLOBAL PROTOTYPES
+ **********************/
+void lv_video_play(lv_obj_t * obj);
+
+void Read_HFile(const char * fileName, lv_obj_t * obj);
+int Video_InitPXP();
+/**
+ * Create an image button object
+ * @param parent pointer to an object, it will be the parent of the new image button
+ * @return pointer to the created image button
+ */
+lv_obj_t * lv_video_create(lv_obj_t * parent, int widgetWidth, int widgetHeight);
+
+
+/*======================
+ * Add/remove functions
+ *=====================*/
+
+/*=====================
+ * Setter functions
+ *====================*/
+
+/**
+ * Set images for a state of the image button
+ * @param obj pointer to an image button object
+ * @param state for which state set the new image
+ */
+void lv_video_set_src(lv_obj_t * obj, const char * src);
+
+void lv_video_set_status(lv_obj_t * obj, int status);
+/*=====================
+ * Getter functions
+ *====================*/
+
+int lv_video_get_status(lv_obj_t * obj);
+const char * lv_video_get_src(lv_obj_t * obj);
+
+/*=====================
+ * Other functions
+ *====================*/
+
+/**********************
+ *      MACROS
+ **********************/
+
+#endif /*LV_USE_VIDEO*/
+
+#ifdef __cplusplus
+} /*extern "C"*/
+#endif
+
+#endif /*LV_VIDEO_H*/
diff --git a/src/lv_conf_internal.h b/src/lv_conf_internal.h
index 0c297081c..e349fdf8c 100644
--- a/src/lv_conf_internal.h
+++ b/src/lv_conf_internal.h
@@ -1499,6 +1499,39 @@
     #endif
 #endif
 
+#ifndef LV_USE_DCLOCK
+    #ifdef _LV_KCONFIG_PRESENT
+        #ifdef CONFIG_LV_USE_DCLOCK
+            #define LV_USE_DCLOCK CONFIG_LV_USE_DCLOCK
+        #else
+            #define LV_USE_DCLOCK 0
+        #endif
+    #else
+        #define LV_USE_DCLOCK      1
+    #endif
+#endif
+#if LV_USE_DCLOCK
+    #ifndef LV_DCLOCK_TEXT_SELECTION
+        #ifdef CONFIG_LV_DCLOCK_TEXT_SELECTION
+            #define LV_DCLOCK_TEXT_SELECTION CONFIG_LV_DCLOCK_TEXT_SELECTION
+        #else
+            #define LV_DCLOCK_TEXT_SELECTION 1 /*Enable selecting text of the dclock*/
+        #endif
+    #endif
+#endif
+
+#ifndef LV_USE_VIDEO
+    #ifdef _LV_KCONFIG_PRESENT
+        #ifdef CONFIG_LV_USE_VIDEO
+            #define LV_USE_VIDEO CONFIG_LV_USE_VIDEO
+        #else
+            #define LV_USE_VIDEO 0
+        #endif
+    #else
+        #define LV_USE_VIDEO      1
+    #endif
+#endif
+
 #ifndef LV_USE_LINE
     #ifdef _LV_KCONFIG_PRESENT
         #ifdef CONFIG_LV_USE_LINE
@@ -1676,6 +1709,18 @@
     #endif
 #endif  /*LV_USE_CALENDAR*/
 
+#ifndef LV_USE_CAROUSEL
+    #ifdef _LV_KCONFIG_PRESENT
+        #ifdef CONFIG_LV_USE_CAROUSEL
+            #define LV_USE_CAROUSEL CONFIG_LV_USE_CAROUSEL
+        #else
+            #define LV_USE_CAROUSEL 0
+        #endif
+    #else
+        #define LV_USE_CAROUSEL  1
+    #endif
+#endif
+
 #ifndef LV_USE_CHART
     #ifdef _LV_KCONFIG_PRESENT
         #ifdef CONFIG_LV_USE_CHART
@@ -1724,6 +1769,27 @@
     #endif
 #endif
 
+#ifndef LV_USE_ZH_KEYBOARD
+    #ifdef _LV_KCONFIG_PRESENT
+        #ifdef CONFIG_LV_USE_ZH_KEYBOARD
+            #define LV_USE_ZH_KEYBOARD CONFIG_LV_USE_ZH_KEYBOARD
+        #else
+            #define LV_USE_ZH_KEYBOARD 0
+        #endif
+    #else
+        #define LV_USE_ZH_KEYBOARD   1
+    #endif
+#endif
+#if LV_USE_ZH_KEYBOARD
+    #ifndef LV_ZH_KEYBOARD_MINI
+        #ifdef CONFIG_LV_ZH_KEYBOARD_MINI
+            #define LV_ZH_KEYBOARD_MINI CONFIG_LV_ZH_KEYBOARD_MINI
+        #else
+            #define LV_ZH_KEYBOARD_MINI   1
+        #endif
+    #endif
+#endif
+
 #ifndef LV_USE_LED
     #ifdef _LV_KCONFIG_PRESENT
         #ifdef CONFIG_LV_USE_LED
@@ -1772,6 +1838,14 @@
     #endif
 #endif
 
+#ifndef LV_USE_ANALOGCLOCK
+#  ifdef CONFIG_LV_USE_ANALOGCLOCK
+#    define LV_USE_ANALOGCLOCK CONFIG_LV_USE_ANALOGCLOCK
+#  else
+#    define  LV_USE_ANALOGCLOCK        1
+#  endif
+#endif
+
 #ifndef LV_USE_MSGBOX
     #ifdef _LV_KCONFIG_PRESENT
         #ifdef CONFIG_LV_USE_MSGBOX
@@ -1784,6 +1858,18 @@
     #endif
 #endif
 
+#ifndef LV_USE_RADIOBTN
+    #ifdef _LV_KCONFIG_PRESENT
+        #ifdef CONFIG_LV_USE_RADIOBTN
+            #define LV_USE_RADIOBTN CONFIG_LV_USE_RADIOBTN
+        #else
+            #define LV_USE_RADIOBTN 0
+        #endif
+    #else
+        #define LV_USE_RADIOBTN   1
+    #endif
+#endif
+
 #ifndef LV_USE_SPAN
     #ifdef _LV_KCONFIG_PRESENT
         #ifdef CONFIG_LV_USE_SPAN
@@ -1842,6 +1928,18 @@
     #endif
 #endif
 
+#ifndef LV_USE_TEXTPROGRESS
+    #ifdef _LV_KCONFIG_PRESENT
+        #ifdef CONFIG_LV_USE_TEXTPROGRESS
+            #define LV_USE_TEXTPROGRESS CONFIG_LV_USE_TEXTPROGRESS
+        #else
+            #define LV_USE_TEXTPROGRESS 0
+        #endif
+    #else
+        #define LV_USE_TEXTPROGRESS 1
+    #endif
+#endif
+
 #ifndef LV_USE_TILEVIEW
     #ifdef _LV_KCONFIG_PRESENT
         #ifdef CONFIG_LV_USE_TILEVIEW
@@ -2124,6 +2222,33 @@
     #endif
 #endif
 
+/*API for RAWFS (needs to be added separately).*/
+#ifndef LV_USE_FS_RAWFS
+    #ifdef CONFIG_LV_USE_FS_RAWFS
+        #define LV_USE_FS_RAWFS CONFIG_LV_USE_FS_RAWFS
+    #else
+        #define LV_USE_FS_RAWFS 0
+    #endif
+#endif
+#if LV_USE_FS_RAWFS
+    #ifndef LV_FS_RAWFS_LETTER
+        #ifdef CONFIG_LV_FS_RAWFS_LETTER
+            #define LV_FS_RAWFS_LETTER CONFIG_LV_FS_RAWFS_LETTER
+        #else
+            #define LV_FS_RAWFS_LETTER 'F'
+        #endif
+    #endif
+    #if LV_FS_RAWFS_XIP
+        #ifndef LV_FS_RAWFS_XIP_BASE_ADDR
+            #ifdef CONFIG_LV_FS_RAWFS_XIP_BASE_ADDR
+                #define LV_FS_RAWFS_XIP_BASE_ADDR CONFIG_LV_FS_RAWFS_XIP_BASE_ADDR
+            #else
+                #define LV_FS_RAWFS_XIP_BASE_ADDR 0xFFFFFFFF
+            #endif
+        #endif
+    #endif
+#endif
+
 /*PNG decoder library*/
 #ifndef LV_USE_PNG
     #ifdef CONFIG_LV_USE_PNG
@@ -2170,6 +2295,15 @@
     #endif
 #endif
 
+/*Barcode library*/
+#ifndef LV_USE_BARCODE
+    #ifdef CONFIG_LV_USE_BARCODE
+        #define LV_USE_BARCODE CONFIG_LV_USE_BARCODE
+    #else
+        #define LV_USE_BARCODE 0
+    #endif
+#endif
+
 /*FreeType library*/
 #ifndef LV_USE_FREETYPE
     #ifdef CONFIG_LV_USE_FREETYPE
diff --git a/src/misc/lv_anim.c b/src/misc/lv_anim.c
index 4e4253a6e..4caa70566 100644
--- a/src/misc/lv_anim.c
+++ b/src/misc/lv_anim.c
@@ -71,6 +71,7 @@ void lv_anim_init(lv_anim_t * a)
     a->repeat_cnt = 1;
     a->path_cb = lv_anim_path_linear;
     a->early_apply = 1;
+    a->anim_pause = false;
 }
 
 lv_anim_t * lv_anim_start(const lv_anim_t * a)
@@ -135,6 +136,45 @@ uint32_t lv_anim_get_playtime(lv_anim_t * a)
     return playtime;
 }
 
+bool lv_anim_pause(void * var, lv_anim_exec_xcb_t exec_cb)
+{
+    lv_anim_t * a;
+    lv_anim_t * a_next;
+    bool pause = false;
+    a        = _lv_ll_get_head(&LV_GC_ROOT(_lv_anim_ll));
+    while(a != NULL) {
+        a_next = _lv_ll_get_next(&LV_GC_ROOT(_lv_anim_ll), a);
+
+        if((a->var == var || var == NULL) && (a->exec_cb == exec_cb || exec_cb == NULL)) {
+            a->anim_pause = true;
+            anim_mark_list_change();
+            pause = true;
+        }
+
+        a = a_next;
+    }
+    return pause;
+}
+
+bool lv_anim_resume(void * var, lv_anim_exec_xcb_t exec_cb)
+{
+    lv_anim_t * a;
+    lv_anim_t * a_next;
+    bool resume = false;
+    a        = _lv_ll_get_head(&LV_GC_ROOT(_lv_anim_ll));
+    while(a != NULL) {
+        a_next = _lv_ll_get_next(&LV_GC_ROOT(_lv_anim_ll), a);
+
+        if((a->var == var || var == NULL) && (a->exec_cb == exec_cb || exec_cb == NULL)) {
+            a->anim_pause = false;
+            anim_mark_list_change();
+            resume = true;
+        }
+        a = a_next;
+    }
+    return resume;
+}
+
 bool lv_anim_del(void * var, lv_anim_exec_xcb_t exec_cb)
 {
     lv_anim_t * a;
@@ -365,7 +405,8 @@ static void anim_timer(lv_timer_t * param)
          */
         anim_list_changed = false;
 
-        if(a->run_round != anim_run_round) {
+        /*Add the animation pause flag check*/
+        if(a->run_round != anim_run_round && !a->anim_pause) {
             a->run_round = anim_run_round; /*The list readying might be reset so need to know which anim has run already*/
 
             /*The animation will run now for the first time. Call `start_cb`*/
diff --git a/src/misc/lv_anim.h b/src/misc/lv_anim.h
index faef72787..dcd8378cd 100644
--- a/src/misc/lv_anim.h
+++ b/src/misc/lv_anim.h
@@ -96,6 +96,7 @@ typedef struct _lv_anim_t {
     uint8_t playback_now : 1; /**< Play back is in progress*/
     uint8_t run_round : 1;    /**< Indicates the animation has run in this round*/
     uint8_t start_cb_called : 1;    /**< Indicates that the `start_cb` was already called*/
+    bool anim_pause;
 } lv_anim_t;
 
 /**********************
@@ -347,6 +348,24 @@ static inline void * lv_anim_get_user_data(lv_anim_t * a)
  */
 bool lv_anim_del(void * var, lv_anim_exec_xcb_t exec_cb);
 
+/**
+ * Pause an animation of a variable with a given animator function
+ * @param var       pointer to variable
+ * @param exec_cb   a function pointer which is animating 'var',
+ *                  or NULL to ignore it and delete all the animations of 'var
+ * @return          true: at least 1 animation is stoped, false: no animation is stoped
+ */
+bool lv_anim_pause(void * var, lv_anim_exec_xcb_t exec_cb);
+
+/**
+ * Resume an animation of a variable with a given animator function
+ * @param var       pointer to variable
+ * @param exec_cb   a function pointer which is animating 'var',
+ *                  or NULL to ignore it and delete all the animations of 'var
+ * @return          true: at least 1 animation is resumed, false: no animation is resumed
+ */
+bool lv_anim_resume(void * var, lv_anim_exec_xcb_t exec_cb);
+
 /**
  * Delete all the animations
  */
diff --git a/src/widgets/lv_arc.c b/src/widgets/lv_arc.c
index 63cf57740..3bb6e3514 100644
--- a/src/widgets/lv_arc.c
+++ b/src/widgets/lv_arc.c
@@ -553,6 +553,11 @@ static void lv_arc_event(const lv_obj_class_t * class_p, lv_event_t * e)
         /*Set the new value*/
         int16_t old_value = arc->value;
         int16_t new_value = lv_map(angle, arc->bg_angle_start, bg_end, arc->min_value, arc->max_value);
+        if(arc->type == LV_ARC_MODE_REVERSE) {
+            new_value = arc->max_value - new_value + arc->min_value;
+        }
+
+
         if(new_value != lv_arc_get_value(obj)) {
             arc->last_tick = lv_tick_get(); /*Cache timestamp for the next iteration*/
             lv_arc_set_value(obj, new_value); /*set_value caches the last_angle for the next iteration*/
diff --git a/src/widgets/lv_btnmatrix.c b/src/widgets/lv_btnmatrix.c
index fd14e56e9..1b0cdc398 100644
--- a/src/widgets/lv_btnmatrix.c
+++ b/src/widgets/lv_btnmatrix.c
@@ -104,13 +104,22 @@ void lv_btnmatrix_set_map(lv_obj_t * obj, const char * map[])
     lv_base_dir_t base_dir = lv_obj_get_style_base_dir(obj, LV_PART_MAIN);
 
     /*Set size and positions of the buttons*/
+    lv_coord_t bwidth = lv_obj_get_style_border_width(obj, LV_PART_MAIN);
+    lv_coord_t bside = lv_obj_get_style_border_side(obj, LV_PART_MAIN);
     lv_coord_t pleft = lv_obj_get_style_pad_left(obj, LV_PART_MAIN);
+    lv_coord_t sleft = (bside & LV_BORDER_SIDE_LEFT) ? pleft + bwidth : pleft;
     lv_coord_t ptop = lv_obj_get_style_pad_top(obj, LV_PART_MAIN);
+    lv_coord_t stop = (bside & LV_BORDER_SIDE_TOP) ? ptop + bwidth : ptop;
+    lv_coord_t pright = lv_obj_get_style_pad_right(obj, LV_PART_MAIN);
+    lv_coord_t sright = (bside & LV_BORDER_SIDE_RIGHT) ? pright + bwidth : pright;
+    lv_coord_t pbottom = lv_obj_get_style_pad_bottom(obj, LV_PART_MAIN);
+    lv_coord_t sbottom = (bside & LV_BORDER_SIDE_BOTTOM) ? pbottom + bwidth : pbottom;
+
     lv_coord_t prow = lv_obj_get_style_pad_row(obj, LV_PART_MAIN);
     lv_coord_t pcol = lv_obj_get_style_pad_column(obj, LV_PART_MAIN);
 
-    lv_coord_t max_w            = lv_obj_get_content_width(obj);
-    lv_coord_t max_h            = lv_obj_get_content_height(obj);
+    lv_coord_t max_w            = lv_obj_get_width(obj) - sleft - sright;
+    lv_coord_t max_h            = lv_obj_get_height(obj) - stop - sbottom;
 
     /*Calculate the position of each row*/
     lv_coord_t max_h_no_gap = max_h - (prow * (btnm->row_cnt - 1));
@@ -138,8 +147,8 @@ void lv_btnmatrix_set_map(lv_obj_t * obj, const char * map[])
             continue;
         }
 
-        lv_coord_t row_y1 = ptop + (max_h_no_gap * row) / btnm->row_cnt + row * prow;
-        lv_coord_t row_y2 = ptop + (max_h_no_gap * (row + 1)) / btnm->row_cnt + row * prow - 1;
+        lv_coord_t row_y1 = stop + (max_h_no_gap * row) / btnm->row_cnt + row * prow;
+        lv_coord_t row_y2 = stop + (max_h_no_gap * (row + 1)) / btnm->row_cnt + row * prow - 1;
 
         /*Set the button size and positions*/
         lv_coord_t max_w_no_gap = max_w - (pcol * (btn_cnt - 1));
@@ -163,8 +172,8 @@ void lv_btnmatrix_set_map(lv_obj_t * obj, const char * map[])
                 btn_x2 = max_w - btn_x2;
             }
 
-            btn_x1 += pleft;
-            btn_x2 += pleft;
+            btn_x1 += sleft;
+            btn_x2 += sleft;
 
             lv_area_set(&btnm->button_areas[btn_tot_i], btn_x1, row_y1, btn_x2, row_y2);
 
-- 
2.34.1

