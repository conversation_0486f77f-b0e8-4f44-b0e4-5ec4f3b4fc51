spi_lcd_320x240_2.0inch/lv_font_fmt_txt.o: \
  ..\lvgl\src\font\lv_font_fmt_txt.c ..\lvgl\src\font\lv_font.h \
  ..\lvgl\src\font\..\lv_conf_internal.h \
  ..\lvgl\src\font\..\lv_conf_kconfig.h ..\lvgl\lv_conf.h \
  ..\lvgl\src\font\lv_symbol_def.h ..\lvgl\src\font\..\misc\lv_area.h \
  ..\lvgl\src\font\..\misc\..\lv_conf_internal.h \
  ..\lvgl\src\font\lv_font_fmt_txt.h \
  ..\lvgl\src\font\..\misc\lv_assert.h ..\lvgl\src\font\..\misc\lv_log.h \
  ..\lvgl\src\font\..\misc\lv_types.h ..\lvgl\src\font\..\misc\lv_mem.h \
  ..\lvgl\src\font\..\misc\lv_gc.h ..\lvgl\src\font\..\misc\lv_ll.h \
  ..\lvgl\src\font\..\misc\lv_timer.h \
  ..\lvgl\src\font\..\misc\..\hal\lv_hal_tick.h \
  ..\lvgl\src\font\..\misc\..\hal\..\lv_conf_internal.h \
  ..\lvgl\src\font\..\misc\..\draw\lv_img_cache.h \
  ..\lvgl\src\font\..\misc\..\draw\lv_img_decoder.h \
  ..\lvgl\src\font\..\misc\..\draw\..\lv_conf_internal.h \
  ..\lvgl\src\font\..\misc\..\draw\lv_img_buf.h \
  ..\lvgl\src\font\..\misc\..\draw\..\misc\lv_color.h \
  ..\lvgl\src\font\..\misc\..\draw\..\misc\..\lv_conf_internal.h \
  ..\lvgl\src\font\..\misc\..\draw\..\misc\lv_assert.h \
  ..\lvgl\src\font\..\misc\..\draw\..\misc\lv_math.h \
  ..\lvgl\src\font\..\misc\..\draw\..\misc\lv_types.h \
  ..\lvgl\src\font\..\misc\..\draw\..\misc\lv_area.h \
  ..\lvgl\src\font\..\misc\..\draw\..\misc\lv_fs.h \
  ..\lvgl\src\font\..\misc\..\draw\lv_draw_mask.h \
  ..\lvgl\src\font\..\misc\..\core\lv_obj_pos.h \
  ..\lvgl\src\font\..\misc\..\core\..\misc\lv_area.h \
  ..\lvgl\src\font\..\misc\lv_utils.h
