/*
* Copyright 2023 NXP
* NXP Proprietary. This software is owned or controlled by NXP and may only be used strictly in
* accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
* activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
* comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
* terms, then you may not retain, install, activate or otherwise use the software.
*/


/*********************
 *      INCLUDES
 *********************/
#include <stdio.h>
#include "lvgl.h"
#include "custom.h"
//hhh
/*********************
 *      DEFINES
 *********************/
unsigned int screen_count=0;
/**********************
 *      TYPEDEFS
 **********************/

/**********************   
 *  STATIC PROTOTYPES
 **********************/

/**********************
 *  STATIC VARIABLES
 **********************/

/**
 * Create a demo application
 */

void custom_init(lv_ui *ui)
{
    /* Add your codes here */
}

// #include "lvgl.h"
// #include <stdio.h>
// #include "gui_guider.h"
// #include "events_init.h"
// #include "widgets_init.h"
// #include "custom.h"



// void menu_group()
// {
// 		extern lv_indev_t * indev_keypad;
// 		lv_group_t *group=lv_group_create();
//     lv_indev_set_group(indev_keypad, group);        //将组绑定到输入设备
 
//     lv_group_set_editing(group, false);   //导航模式
//     lv_group_add_obj(group ,guider_ui.screen_2_btn_10);
//     lv_group_add_obj(group ,guider_ui.screen_2_btn_2);
//     lv_group_add_obj(group ,guider_ui.screen_2_btn_11);
//     lv_group_add_obj(group ,guider_ui.screen_2_btn_17);
// 		lv_group_add_obj(group ,guider_ui.screen_2_btn_6);
//     lv_group_add_obj(group ,guider_ui.screen_2_btn_12);
//     lv_group_add_obj(group ,guider_ui.screen_2_btn_13);
//     lv_group_add_obj(group ,guider_ui.screen_2_btn_18);
// 		lv_group_add_obj(group ,guider_ui.screen_2_btn_14);
//     lv_group_add_obj(group ,guider_ui.screen_2_btn_15);
//     lv_group_add_obj(group ,guider_ui.screen_2_btn_19);
//     lv_group_add_obj(group ,guider_ui.screen_2_btn_16);
// }

// /**
//  * @brief 时间设置界面的LVGL导航组初始化函数
//  * 为screen_5界面的时间设置控件创建group并设置焦点导航
//  */
// void time_setting_group()
// {
//     extern lv_indev_t * indev_keypad;
//     lv_group_t *group = lv_group_create();
//     lv_indev_set_group(indev_keypad, group);        // 将组绑定到输入设备

//     lv_group_set_editing(group, false);   // 导航模式

//     // 按照界面布局顺序添加时间设置控件到group
//     // 第一行：年、月
//     lv_group_add_obj(group, guider_ui.screen_5_ddlist_1);  // 年份下拉框
//     lv_group_add_obj(group, guider_ui.screen_5_ddlist_2);  // 月份下拉框

//     // 第二行：日、时
//     lv_group_add_obj(group, guider_ui.screen_5_ddlist_3);  // 日期下拉框
//     lv_group_add_obj(group, guider_ui.screen_5_ddlist_4);  // 小时下拉框（如果存在）

//     // 第三行：分、秒
//     lv_group_add_obj(group, guider_ui.screen_5_ddlist_6);  // 分钟下拉框
//     lv_group_add_obj(group, guider_ui.screen_5_ddlist_5);  // 秒钟下拉框

//     // 最后添加返回按钮
//     lv_group_add_obj(group, guider_ui.screen_5_btn_1);     // 返回按钮

//     // 设置初始焦点到年份下拉框
//     lv_group_focus_obj(guider_ui.screen_5_ddlist_1);
// }

// /**
//  * @brief screen_4界面的LVGL导航组初始化函数
//  * 为screen_4界面的控件创建group并设置焦点导航
//  */
// void screen_4_group()
// {
//     extern lv_indev_t * indev_keypad;
//     lv_group_t *group = lv_group_create();
//     lv_indev_set_group(indev_keypad, group);        // 将组绑定到输入设备

//     lv_group_set_editing(group, false);   // 导航模式

//     // 添加进度条（如果需要导航）
//     lv_group_add_obj(group, guider_ui.screen_4_bar_1);     // 进度条

//     // 添加返回按钮
//     lv_group_add_obj(group, guider_ui.screen_4_btn_1);     // 返回按钮

//     // 设置初始焦点到返回按钮
//     lv_group_focus_obj(guider_ui.screen_4_bar_1);
// }

// /**
//  * @brief screen_6界面的LVGL导航组初始化函数
//  * 为screen_6界面的控件创建group并设置焦点导航
//  */
// void screen_6_group()
// {
//     extern lv_indev_t * indev_keypad;
//     lv_group_t *group = lv_group_create();
//     lv_indev_set_group(indev_keypad, group);        // 将组绑定到输入设备

//     lv_group_set_editing(group, false);   // 导航模式

//     // 根据screen_6的实际控件添加到group
//     // 假设有按钮控件，请根据实际情况调整
//     if (guider_ui.screen_6_btn_1 != NULL) {
//         lv_group_add_obj(group, guider_ui.screen_6_btn_1);
//     }
// 		lv_group_add_obj(group, guider_ui.screen_6_ddlist_1);
// 		lv_group_add_obj(group, guider_ui.screen_6_ddlist_2);
// 		lv_group_add_obj(group, guider_ui.screen_6_ddlist_3);
//     // 如果有其他可导航控件，继续添加
//     // lv_group_add_obj(group, guider_ui.screen_6_xxx);

//     // 设置初始焦点（根据实际控件调整）
//     if (guider_ui.screen_6_btn_1 != NULL) {
//         lv_group_focus_obj(guider_ui.screen_6_ddlist_1);
//     }
// }

