/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.h
  * @brief          : Header for main.c file.
  *                   This file contains the common defines of the application.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2024 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MAIN_H
#define __MAIN_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx_hal.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Exported types ------------------------------------------------------------*/
/* USER CODE BEGIN ET */

/* USER CODE END ET */

/* Exported constants --------------------------------------------------------*/
/* USER CODE BEGIN EC */

/* USER CODE END EC */

/* Exported macro ------------------------------------------------------------*/
/* USER CODE BEGIN EM */

/* USER CODE END EM */

/* Exported functions prototypes ---------------------------------------------*/
void Error_Handler(void);

/* USER CODE BEGIN EFP */

/* USER CODE END EFP */

/* Private defines -----------------------------------------------------------*/
#define KEY_OK_Pin GPIO_PIN_2
#define KEY_OK_GPIO_Port GPIOE
#define KEY_OK_EXTI_IRQn EXTI2_IRQn
#define KEY_Left_Pin GPIO_PIN_3
#define KEY_Left_GPIO_Port GPIOE
#define KEY_Left_EXTI_IRQn EXTI3_IRQn
#define KEY_Down_Pin GPIO_PIN_4
#define KEY_Down_GPIO_Port GPIOE
#define KEY_Down_EXTI_IRQn EXTI4_IRQn
#define KEY_Power_Pin GPIO_PIN_5
#define KEY_Power_GPIO_Port GPIOE
#define KEY_Power_EXTI_IRQn EXTI9_5_IRQn
#define KEY_Return_Pin GPIO_PIN_6
#define KEY_Return_GPIO_Port GPIOE
#define KEY_Return_EXTI_IRQn EXTI9_5_IRQn
#define LCD_SCK_Pin GPIO_PIN_5
#define LCD_SCK_GPIO_Port GPIOA
#define LCD_DC_Pin GPIO_PIN_6
#define LCD_DC_GPIO_Port GPIOA
#define LCD_SDA_Pin GPIO_PIN_7
#define LCD_SDA_GPIO_Port GPIOA
#define Motor_switch_Pin GPIO_PIN_0
#define Motor_switch_GPIO_Port GPIOB
#define Voice_out_Pin GPIO_PIN_7
#define Voice_out_GPIO_Port GPIOE
#define Voice_CLK_Pin GPIO_PIN_8
#define Voice_CLK_GPIO_Port GPIOE
#define Power_out_Pin GPIO_PIN_9
#define Power_out_GPIO_Port GPIOE
#define GPIO_Charge_Pin GPIO_PIN_10
#define GPIO_Charge_GPIO_Port GPIOE
#define RGB_G_Pin GPIO_PIN_12
#define RGB_G_GPIO_Port GPIOD
#define RGB_Y_Pin GPIO_PIN_13
#define RGB_Y_GPIO_Port GPIOD
#define RGB_R_Pin GPIO_PIN_14
#define RGB_R_GPIO_Port GPIOD
#define Pump_switch_Pin GPIO_PIN_7
#define Pump_switch_GPIO_Port GPIOC
#define LCD_RST_Pin GPIO_PIN_3
#define LCD_RST_GPIO_Port GPIOB
#define LCD_BLK_Pin GPIO_PIN_4
#define LCD_BLK_GPIO_Port GPIOB
#define LCD_CS_Pin GPIO_PIN_5
#define LCD_CS_GPIO_Port GPIOB
#define KEY_UP_Pin GPIO_PIN_0
#define KEY_UP_GPIO_Port GPIOE
#define KEY_UP_EXTI_IRQn EXTI0_IRQn
#define KEY_Right_Pin GPIO_PIN_1
#define KEY_Right_GPIO_Port GPIOE
#define KEY_Right_EXTI_IRQn EXTI1_IRQn

/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */

#ifdef __cplusplus
}
#endif

#endif /* __MAIN_H */
