spi_lcd_320x240_2.0inch/lv_menu.o: \
  ..\lvgl\src\extra\widgets\menu\lv_menu.c \
  ..\lvgl\src\extra\widgets\menu\lv_menu.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\lv_obj.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\lv_conf_internal.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\lv_conf_kconfig.h \
  ..\lvgl\lv_conf.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\misc\lv_style.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\misc\..\font\lv_font.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\misc\..\font\..\lv_conf_internal.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\misc\..\font\lv_symbol_def.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\misc\..\font\..\misc\lv_area.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\misc\..\font\..\misc\..\lv_conf_internal.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\misc\lv_color.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\misc\..\lv_conf_internal.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\misc\lv_assert.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\misc\lv_log.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\misc\lv_types.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\misc\lv_mem.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\misc\lv_math.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\misc\lv_area.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\misc\lv_anim.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\misc\lv_txt.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\misc\lv_printf.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\misc\lv_bidi.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\misc\lv_style_gen.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\lv_hal.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\lv_hal_disp.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\draw\lv_draw.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\draw\..\lv_conf_internal.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\draw\..\misc\lv_style.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\draw\..\misc\lv_txt.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\draw\lv_img_decoder.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\draw\lv_img_buf.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\draw\..\misc\lv_color.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\draw\..\misc\lv_area.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\draw\..\misc\lv_fs.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\draw\..\misc\..\lv_conf_internal.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\draw\..\misc\lv_types.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\draw\lv_img_cache.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\draw\lv_draw_rect.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\draw\sw\lv_draw_sw_gradient.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\draw\sw\..\..\misc\lv_color.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\draw\sw\..\..\misc\lv_style.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\draw\sw\lv_draw_sw_dither.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\draw\sw\..\..\core\lv_obj_pos.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\draw\sw\..\..\core\..\misc\lv_area.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\draw\lv_draw_label.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\draw\..\misc\lv_bidi.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\draw\lv_draw_img.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\draw\lv_draw_line.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\draw\lv_draw_triangle.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\draw\lv_draw_arc.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\draw\lv_draw_mask.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\draw\..\misc\lv_math.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\draw\lv_draw_transform.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\draw\lv_draw_layer.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\misc\lv_color.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\misc\lv_area.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\misc\lv_ll.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\misc\lv_timer.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\misc\..\lv_conf_internal.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\misc\..\hal\lv_hal_tick.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\misc\..\hal\..\lv_conf_internal.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\lv_hal_indev.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\..\lv_conf_internal.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\hal\lv_hal_tick.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\lv_obj_tree.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\lv_obj_pos.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\lv_obj_scroll.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\lv_obj_style.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\lv_obj_style_gen.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\lv_obj_draw.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\draw\lv_draw.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\lv_obj_class.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\lv_event.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\lv_group.h \
  ..\lvgl\src\extra\widgets\menu\..\..\..\core\..\misc\lv_ll.h
