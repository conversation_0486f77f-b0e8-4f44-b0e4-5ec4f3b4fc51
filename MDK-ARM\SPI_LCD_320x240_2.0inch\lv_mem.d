spi_lcd_320x240_2.0inch/lv_mem.o: ..\lvgl\src\misc\lv_mem.c \
  ..\lvgl\src\misc\lv_mem.h ..\lvgl\src\misc\..\lv_conf_internal.h \
  ..\lvgl\src\misc\..\lv_conf_kconfig.h ..\lvgl\lv_conf.h \
  ..\lvgl\src\misc\lv_types.h ..\lvgl\src\misc\lv_tlsf.h \
  ..\lvgl\src\misc\lv_gc.h ..\lvgl\src\misc\lv_ll.h \
  ..\lvgl\src\misc\lv_timer.h ..\lvgl\src\misc\..\hal\lv_hal_tick.h \
  ..\lvgl\src\misc\..\hal\..\lv_conf_internal.h \
  ..\lvgl\src\misc\..\draw\lv_img_cache.h \
  ..\lvgl\src\misc\..\draw\lv_img_decoder.h \
  ..\lvgl\src\misc\..\draw\..\lv_conf_internal.h \
  ..\lvgl\src\misc\..\draw\lv_img_buf.h \
  ..\lvgl\src\misc\..\draw\..\misc\lv_color.h \
  ..\lvgl\src\misc\..\draw\..\misc\..\lv_conf_internal.h \
  ..\lvgl\src\misc\..\draw\..\misc\lv_assert.h \
  ..\lvgl\src\misc\..\draw\..\misc\lv_log.h \
  ..\lvgl\src\misc\..\draw\..\misc\lv_types.h \
  ..\lvgl\src\misc\..\draw\..\misc\lv_mem.h \
  ..\lvgl\src\misc\..\draw\..\misc\lv_math.h \
  ..\lvgl\src\misc\..\draw\..\misc\lv_area.h \
  ..\lvgl\src\misc\..\draw\..\misc\lv_fs.h \
  ..\lvgl\src\misc\..\draw\lv_draw_mask.h \
  ..\lvgl\src\misc\..\core\lv_obj_pos.h \
  ..\lvgl\src\misc\..\core\..\misc\lv_area.h \
  ..\lvgl\src\misc\lv_assert.h ..\lvgl\src\misc\lv_log.h
