#include "main.h"  // 包含GPIO定义
#include "key_control.h"
#include "tim.h" 
#include "lvgl.h"
#include <stdio.h>
#include "gui_guider.h"
#include "widgets_init.h"
/* 按键有效持续时间 (ms) */
#define KEY_VALID_DURATION 20U

/* 全局按键控制结构体 */
key_control_t g_key_ctrl = {0};

/* 按键事件队列，用于中断安全处理 */
static volatile key_type_t pending_key = KEY_MAX;
static volatile uint8_t key_event_pending = 0;

/**
 * @brief 初始化按键控制系统
 */
void key_control_init(void)
{
    uint8_t i;
    
    /* 清零按键时间记录和状态 */
    for (i = 0U; i < KEY_MAX; i++) {
        g_key_ctrl.key_press_time[i] = 0U;
        g_key_ctrl.key_press_state[i] = 0U;  /* 0=未按下, 1=已按下 */
    }
}

/**
 * @brief 按键中断处理函数 - 双边沿触发消抖
 * @param key 按键类型
 */
void key_interrupt_handler(key_type_t key)
{
    uint32_t current_time = HAL_GetTick();
    uint8_t current_pin_state;
    uint32_t press_duration;
    
    /* 读取当前引脚状态 */
    switch (key) {
        case KEY_UP:
            current_pin_state = HAL_GPIO_ReadPin(KEY_UP_GPIO_Port, KEY_UP_Pin);
            break;
        case KEY_LEFT:
            current_pin_state = HAL_GPIO_ReadPin(KEY_Left_GPIO_Port, KEY_Left_Pin);
            break;
        case KEY_OK:
            current_pin_state = HAL_GPIO_ReadPin(KEY_OK_GPIO_Port, KEY_OK_Pin);
            break;
        case KEY_RIGHT:
            current_pin_state = HAL_GPIO_ReadPin(KEY_Right_GPIO_Port, KEY_Right_Pin);
            break;
        case KEY_BACK:
            current_pin_state = HAL_GPIO_ReadPin(KEY_Return_GPIO_Port, KEY_Return_Pin);
            break;
        case KEY_DOWN:
            current_pin_state = HAL_GPIO_ReadPin(KEY_Down_GPIO_Port, KEY_Down_Pin);
            break;
        case KEY_POWER:
            current_pin_state = HAL_GPIO_ReadPin(KEY_Power_GPIO_Port, KEY_Power_Pin);
            break;
        default:
            return;
    }
    
    if (current_pin_state == 0U && g_key_ctrl.key_press_state[key] == 0U) {
        /* 下降沿：按键按下，记录按下时间 */
        g_key_ctrl.key_press_state[key] = 1U;
        g_key_ctrl.key_press_time[key] = current_time;
    } else if (current_pin_state == 1U && g_key_ctrl.key_press_state[key] == 1U) {
        /* 上升沿：按键松开，检查持续时间 */
        g_key_ctrl.key_press_state[key] = 0U;
        press_duration = current_time - g_key_ctrl.key_press_time[key];
        
        /* 只有持续时间≥20ms才算有效按键 */
        if (press_duration >= KEY_VALID_DURATION) {
            /* 在中断中只设置标志，不直接处理按键事件 */
            if (!key_event_pending) {
                pending_key = key;
                key_event_pending = 1;
            }
        }
    }
}

/**
 * @brief 在主循环中处理挂起的按键事件（中断安全）
 */
void key_process_pending_events(void)
{
    if (key_event_pending) {
        key_type_t key = pending_key;
        key_event_pending = 0;  // 清除标志

        // 在主循环上下文中安全地处理按键事件
        process_key_press(key);
    }
}

/**
 * @brief 处理有效按键按下事件 - 用户自定义
 * @param key 按键类型
 */
unsigned char key_flag=0;
uint32_t key_num = 0;
void process_key_press(key_type_t key)
{
	switch (key) {
								case KEY_UP:
										/* 处理上键 */
								//__HAL_TIM_SetCompare(&htim3, TIM_CHANNEL_1, 100); // 设置LCD背光亮度
								key_num = 2;
										break;
								case KEY_DOWN:
										/* 处理下键 */
								//__HAL_TIM_SetCompare(&htim3, TIM_CHANNEL_1, 500); // 设置LCD背光亮度
								key_num = 1;
										break;
								case KEY_LEFT:
										/* 处理左键 */
								//__HAL_TIM_SetCompare(&htim3, TIM_CHANNEL_1, 900); // 设置LCD背光亮度
								key_num = 3;
										break;
								case KEY_RIGHT:
										/* 处理右键 */
								//__HAL_TIM_SetCompare(&htim3, TIM_CHANNEL_1, 100); // 设置LCD背光亮度
								key_num = 4;
										break;
								case KEY_OK:
										/* 处理确认键 */
								//__HAL_TIM_SetCompare(&htim3, TIM_CHANNEL_1, 500); // 设置LCD背光亮度
								key_num = 5;
										break;
								case KEY_BACK:
										/* 处理返回键 */
								//__HAL_TIM_SetCompare(&htim3, TIM_CHANNEL_1, 900); // 设置LCD背光亮度
								key_num = 6;
										break;
								case KEY_POWER:
										/* 处理开机键 */
								//__HAL_TIM_SetCompare(&htim3, TIM_CHANNEL_1, 100); // 设置LCD背光亮度
								key_num = 0;
										break;
								default:
										break;
			}
    /* 在这里添加您的具体按键处理逻辑 */
		switch (g_key_ctrl.current_screen) {
        case SCREEN_STARTUP:
            break;
        case SCREEN_MAIN:
            /* 主界面：只处理OK键和开机键 */
				//key_GPS(key);
            if (key == KEY_OK) {
                /* 安全地发送LVGL事件 */
                if (guider_ui.screen_3_btn_1 != NULL) {
                    lv_event_send(guider_ui.screen_3_btn_1, LV_EVENT_CLICKED, NULL);
                }
								g_key_ctrl.current_screen = SCREEN_MENU;
            } else if (key == KEY_POWER) {
                /* 可以添加关机逻辑 */
            }
            break;
//						case SCREEN_MENU:
//							//key_GPS(key);
//							break;
				default:
						break;
					}

}

//void key_GPS(key_type_t key)
//{
//	 switch (key) {
//								case KEY_UP:
//										/* 处理上键 */
//								__HAL_TIM_SetCompare(&htim3, TIM_CHANNEL_1, 100); // 设置LCD背光亮度
//										break;
//								case KEY_DOWN:
//										/* 处理下键 */
//								__HAL_TIM_SetCompare(&htim3, TIM_CHANNEL_1, 500); // 设置LCD背光亮度
//										break;
//								case KEY_LEFT:
//										/* 处理左键 */
//								__HAL_TIM_SetCompare(&htim3, TIM_CHANNEL_1, 900); // 设置LCD背光亮度
//										break;
//								case KEY_RIGHT:
//										/* 处理右键 */
//								__HAL_TIM_SetCompare(&htim3, TIM_CHANNEL_1, 100); // 设置LCD背光亮度
//										break;
//								case KEY_OK:
//										/* 处理确认键 */
//								__HAL_TIM_SetCompare(&htim3, TIM_CHANNEL_1, 500); // 设置LCD背光亮度
//										break;
//								case KEY_BACK:
//										/* 处理返回键 */
//								__HAL_TIM_SetCompare(&htim3, TIM_CHANNEL_1, 900); // 设置LCD背光亮度
//										break;
//								case KEY_POWER:
//										/* 处理开机键 */
//								__HAL_TIM_SetCompare(&htim3, TIM_CHANNEL_1, 100); // 设置LCD背光亮度
//										break;
//								default:
//										break;
//			}
//}