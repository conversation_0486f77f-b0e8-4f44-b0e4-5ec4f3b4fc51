/*
 * RTC时钟显示模块 - 超简化版本
 * 功能：从RTC读取时间并更新LVGL标签
 */

#include "rtc.h"
#include "gui_guider.h"
#include "main.h"
#include <stdio.h>
#include "key_control.h"
/* 外部变量 */
extern lv_ui guider_ui;

/* 简单的全局变量 */
static uint32_t last_update_time = 0;

/**
 * @brief 超简单的RTC时钟更新函数
 * 在主循环中调用，每秒更新一次时间显示
 */
void rtc_update_display(void)
{
    uint32_t current_time = HAL_GetTick();
    RTC_TimeTypeDef sTime;
    RTC_DateTypeDef sDate;
    char time_str[16];

    // 每1秒更新一次
    if ((current_time - last_update_time) < 1000) {
        return;
    }
    last_update_time = current_time;

    // 检查标签是否存在
    if (guider_ui.screen_3_label_2 == NULL) {
        return;
    }

    // 读取RTC时间
    if (HAL_RTC_GetTime(&hrtc, &sTime, RTC_FORMAT_BCD) != HAL_OK) {
        return;
    }

    // 读取RTC日期（必须的，用于解锁影子寄存器）
    if (HAL_RTC_GetDate(&hrtc, &sDate, RTC_FORMAT_BCD) != HAL_OK) {
        return;
    }

    // BCD转十进制
    int hours = ((sTime.Hours >> 4) * 10) + (sTime.Hours & 0x0F);
    int minutes = ((sTime.Minutes >> 4) * 10) + (sTime.Minutes & 0x0F);
    int seconds = ((sTime.Seconds >> 4) * 10) + (sTime.Seconds & 0x0F);

    // 格式化时间字符串
    snprintf(time_str, sizeof(time_str), "%02d:%02d:%02d", hours, minutes, seconds);

    // 更新标签
    if(g_key_ctrl.current_screen==SCREEN_MAIN)
    lv_label_set_text(guider_ui.screen_3_label_2, time_str);
}

/* 文件已简化，只保留核心功能 */
