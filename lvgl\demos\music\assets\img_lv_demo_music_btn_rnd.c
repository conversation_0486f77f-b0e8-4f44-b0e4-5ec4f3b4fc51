#include "../lv_demo_music.h"
#if LV_USE_DEMO_MUSIC  && !LV_DEMO_MUSIC_LARGE

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

const LV_ATTRIBUTE_MEM_ALIGN uint8_t img_lv_demo_music_btn_rnd_map[] = {
#if LV_COLOR_DEPTH == 1 || LV_COLOR_DEPTH == 8
  /*Pixel format: Alpha 8 bit, Red: 3 bit, Green: 3 bit, Blue: 2 bit*/
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00,
  0xff, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x03, 0x4a, 0x47, 0x4a, 0x93, 0x4a, 0xc7, 0x4a, 0xdc, 0x4a, 0xd4, 0x4a, 0xbb, 0x4a, 0x8f, 0x4a, 0x3f, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0xff, 0x00,
  0xff, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x47, 0x4a, 0xd0, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xc4, 0x4a, 0x38, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0xff, 0x00,
  0xff, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x03, 0x4a, 0x88, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xfc, 0x4a, 0x73, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0xff, 0x00,
  0xff, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0xa3, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0x87, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0xff, 0x00,
  0xff, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x6b, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0x50, 0x4a, 0x00, 0x4a, 0x00, 0xff, 0x00,
  0xff, 0x00, 0x4a, 0x00, 0x4a, 0x14, 0x4a, 0xf0, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xe4, 0x4a, 0x08, 0x4a, 0x00, 0xff, 0x00,
  0xff, 0x00, 0x4a, 0x00, 0x4a, 0x80, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0xb7, 0xff, 0xdb, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0x68, 0x4a, 0x00, 0xff, 0x00,
  0xff, 0x00, 0x4a, 0x00, 0x4a, 0xd7, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0xb7, 0xff, 0xdb, 0xff, 0x92, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x6e, 0xff, 0xdb, 0xff, 0xff, 0xff, 0xff, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xbf, 0x4a, 0x00, 0xff, 0x00,
  0xff, 0x00, 0x4a, 0x14, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0xdb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb7, 0xff, 0x4a, 0xff, 0x92, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xf4, 0x4a, 0x00, 0xff, 0x00,
  0xff, 0x00, 0x4a, 0x34, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x93, 0xff, 0xff, 0xff, 0xb3, 0xff, 0xff, 0xff, 0xb7, 0xff, 0x92, 0xff, 0xff, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0x13, 0xff, 0x00,
  0xff, 0x00, 0x4a, 0x3c, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x92, 0xff, 0xff, 0xff, 0xb7, 0xff, 0x4a, 0xff, 0x92, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0x20, 0xff, 0x00,
  0xff, 0x00, 0x4a, 0x2b, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x92, 0xff, 0xff, 0xff, 0xb7, 0xff, 0xdb, 0xff, 0xb7, 0xff, 0x92, 0xff, 0xdb, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0x13, 0xff, 0x00,
  0xff, 0x00, 0x4a, 0x07, 0x4a, 0xf8, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0xdb, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xb7, 0xff, 0x4a, 0xff, 0x92, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xe7, 0x4a, 0x00, 0xff, 0x00,
  0xff, 0x00, 0x4a, 0x00, 0x4a, 0xb8, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0xb7, 0xff, 0xdb, 0xff, 0x92, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x6e, 0xff, 0xdb, 0xff, 0xff, 0xff, 0xff, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xa0, 0x4a, 0x00, 0xff, 0x00,
  0xff, 0x00, 0x4a, 0x00, 0x4a, 0x54, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0xb7, 0xff, 0xdb, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0x3c, 0x4a, 0x00, 0xff, 0x00,
  0xff, 0x00, 0x4a, 0x00, 0x4a, 0x03, 0x4a, 0xcc, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xb7, 0x4a, 0x00, 0x4a, 0x00, 0xff, 0x00,
  0xff, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x2c, 0x4a, 0xf4, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xeb, 0x4a, 0x1c, 0x4a, 0x00, 0x4a, 0x00, 0xff, 0x00,
  0xff, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x4b, 0x4a, 0xf3, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xec, 0x4a, 0x37, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0xff, 0x00,
  0xff, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x2f, 0x4a, 0xdb, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xd0, 0x4a, 0x23, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0xff, 0x00,
  0xff, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x07, 0x4a, 0x70, 0x4a, 0xe0, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xd8, 0x4a, 0x63, 0x4a, 0x03, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0xff, 0x00,
  0xff, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x03, 0x4a, 0x30, 0x4a, 0x63, 0x4a, 0x77, 0x4a, 0x70, 0x4a, 0x57, 0x4a, 0x2b, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0x4a, 0x00, 0xff, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00,
  0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00,
#endif
#if LV_COLOR_DEPTH == 16 && LV_COLOR_16_SWAP == 0
  /*Pixel format: Alpha 8 bit, Red: 5 bit, Green: 6 bit, Blue: 5 bit*/
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x03, 0x6e, 0x52, 0x47, 0x6e, 0x52, 0x93, 0x6e, 0x52, 0xc7, 0x6e, 0x52, 0xdc, 0x6e, 0x52, 0xd4, 0x6e, 0x52, 0xbb, 0x6e, 0x52, 0x8f, 0x6e, 0x52, 0x3f, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x47, 0x6e, 0x52, 0xd0, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xc4, 0x6e, 0x52, 0x38, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x03, 0x6e, 0x52, 0x88, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xfc, 0x6e, 0x52, 0x73, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0xa3, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0x87, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x6b, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0x50, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x14, 0x6e, 0x52, 0xf0, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xe4, 0x6e, 0x52, 0x08, 0x6e, 0x52, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x80, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0xd6, 0x9c, 0xff, 0x7b, 0xd6, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0x68, 0x6e, 0x52, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0xd7, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x77, 0xb5, 0xff, 0xf9, 0xc5, 0xff, 0xd2, 0x7b, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x30, 0x6b, 0xff, 0x7b, 0xd6, 0xff, 0xff, 0xff, 0xff, 0xbb, 0xd6, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xbf, 0x6e, 0x52, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x6e, 0x52, 0x14, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0xf9, 0xbd, 0xff, 0x9b, 0xd6, 0xff, 0xff, 0xff, 0xff, 0x16, 0xa5, 0xff, 0x6e, 0x52, 0xff, 0x91, 0x73, 0xff, 0xff, 0xff, 0xff, 0xbb, 0xd6, 0xff, 0xff, 0xff, 0xff, 0x3d, 0xef, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xf4, 0x6e, 0x52, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x6e, 0x52, 0x34, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x74, 0x94, 0xff, 0xdc, 0xde, 0xff, 0x94, 0x94, 0xff, 0xff, 0xff, 0xff, 0xb5, 0x9c, 0xff, 0x33, 0x8c, 0xff, 0x9b, 0xd6, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0x13, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x6e, 0x52, 0x3c, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x13, 0x84, 0xff, 0xff, 0xff, 0xff, 0x36, 0xa5, 0xff, 0x6e, 0x52, 0xff, 0xb2, 0x7b, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0x20, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x6e, 0x52, 0x2b, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x54, 0x8c, 0xff, 0xff, 0xff, 0xff, 0xb5, 0x9c, 0xff, 0xf9, 0xbd, 0xff, 0xd5, 0x9c, 0xff, 0x33, 0x8c, 0xff, 0x9b, 0xd6, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0x13, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x6e, 0x52, 0x07, 0x6e, 0x52, 0xf8, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0xd9, 0xbd, 0xff, 0x9b, 0xd6, 0xff, 0xff, 0xff, 0xff, 0xb5, 0x9c, 0xff, 0x6e, 0x52, 0xff, 0x33, 0x8c, 0xff, 0xff, 0xff, 0xff, 0xbb, 0xde, 0xff, 0xff, 0xff, 0xff, 0x1d, 0xe7, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xe7, 0x6e, 0x52, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0xb8, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x77, 0xb5, 0xff, 0xf9, 0xc5, 0xff, 0xd2, 0x83, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x50, 0x6b, 0xff, 0x7b, 0xd6, 0xff, 0xff, 0xff, 0xff, 0xbc, 0xde, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xa0, 0x6e, 0x52, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x54, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0xd6, 0x9c, 0xff, 0x7b, 0xd6, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0x3c, 0x6e, 0x52, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x03, 0x6e, 0x52, 0xcc, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xb7, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x2c, 0x6e, 0x52, 0xf4, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xeb, 0x6e, 0x52, 0x1c, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x4b, 0x6e, 0x52, 0xf3, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xec, 0x6e, 0x52, 0x37, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x2f, 0x6e, 0x52, 0xdb, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xd0, 0x6e, 0x52, 0x23, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x07, 0x6e, 0x52, 0x70, 0x6e, 0x52, 0xe0, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xff, 0x6e, 0x52, 0xd8, 0x6e, 0x52, 0x63, 0x6e, 0x52, 0x03, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x03, 0x6e, 0x52, 0x30, 0x6e, 0x52, 0x63, 0x6e, 0x52, 0x77, 0x6e, 0x52, 0x70, 0x6e, 0x52, 0x57, 0x6e, 0x52, 0x2b, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0x6e, 0x52, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00,
#endif
#if LV_COLOR_DEPTH == 16 && LV_COLOR_16_SWAP != 0
  /*Pixel format: Alpha 8 bit, Red: 5 bit, Green: 6 bit, Blue: 5 bit  BUT the 2  color bytes are swapped*/
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x03, 0x52, 0x6e, 0x47, 0x52, 0x6e, 0x93, 0x52, 0x6e, 0xc7, 0x52, 0x6e, 0xdc, 0x52, 0x6e, 0xd4, 0x52, 0x6e, 0xbb, 0x52, 0x6e, 0x8f, 0x52, 0x6e, 0x3f, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x47, 0x52, 0x6e, 0xd0, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xc4, 0x52, 0x6e, 0x38, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x03, 0x52, 0x6e, 0x88, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xfc, 0x52, 0x6e, 0x73, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0xa3, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0x87, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x6b, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0x50, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x14, 0x52, 0x6e, 0xf0, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xe4, 0x52, 0x6e, 0x08, 0x52, 0x6e, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x80, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x9c, 0xd6, 0xff, 0xd6, 0x7b, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0x68, 0x52, 0x6e, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0xd7, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0xb5, 0x77, 0xff, 0xc5, 0xf9, 0xff, 0x7b, 0xd2, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x6b, 0x30, 0xff, 0xd6, 0x7b, 0xff, 0xff, 0xff, 0xff, 0xd6, 0xbb, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xbf, 0x52, 0x6e, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x52, 0x6e, 0x14, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0xbd, 0xf9, 0xff, 0xd6, 0x9b, 0xff, 0xff, 0xff, 0xff, 0xa5, 0x16, 0xff, 0x52, 0x6e, 0xff, 0x73, 0x91, 0xff, 0xff, 0xff, 0xff, 0xd6, 0xbb, 0xff, 0xff, 0xff, 0xff, 0xef, 0x3d, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xf4, 0x52, 0x6e, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x52, 0x6e, 0x34, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x94, 0x74, 0xff, 0xde, 0xdc, 0xff, 0x94, 0x94, 0xff, 0xff, 0xff, 0xff, 0x9c, 0xb5, 0xff, 0x8c, 0x33, 0xff, 0xd6, 0x9b, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0x13, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x52, 0x6e, 0x3c, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x84, 0x13, 0xff, 0xff, 0xff, 0xff, 0xa5, 0x36, 0xff, 0x52, 0x6e, 0xff, 0x7b, 0xb2, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0x20, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x52, 0x6e, 0x2b, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x8c, 0x54, 0xff, 0xff, 0xff, 0xff, 0x9c, 0xb5, 0xff, 0xbd, 0xf9, 0xff, 0x9c, 0xd5, 0xff, 0x8c, 0x33, 0xff, 0xd6, 0x9b, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0x13, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x52, 0x6e, 0x07, 0x52, 0x6e, 0xf8, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0xbd, 0xd9, 0xff, 0xd6, 0x9b, 0xff, 0xff, 0xff, 0xff, 0x9c, 0xb5, 0xff, 0x52, 0x6e, 0xff, 0x8c, 0x33, 0xff, 0xff, 0xff, 0xff, 0xde, 0xbb, 0xff, 0xff, 0xff, 0xff, 0xe7, 0x1d, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xe7, 0x52, 0x6e, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0xb8, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0xb5, 0x77, 0xff, 0xc5, 0xf9, 0xff, 0x83, 0xd2, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x6b, 0x50, 0xff, 0xd6, 0x7b, 0xff, 0xff, 0xff, 0xff, 0xde, 0xbc, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xa0, 0x52, 0x6e, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x54, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x9c, 0xd6, 0xff, 0xd6, 0x7b, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0x3c, 0x52, 0x6e, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x03, 0x52, 0x6e, 0xcc, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xb7, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x2c, 0x52, 0x6e, 0xf4, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xeb, 0x52, 0x6e, 0x1c, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x4b, 0x52, 0x6e, 0xf3, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xec, 0x52, 0x6e, 0x37, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x2f, 0x52, 0x6e, 0xdb, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xd0, 0x52, 0x6e, 0x23, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x07, 0x52, 0x6e, 0x70, 0x52, 0x6e, 0xe0, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xff, 0x52, 0x6e, 0xd8, 0x52, 0x6e, 0x63, 0x52, 0x6e, 0x03, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x03, 0x52, 0x6e, 0x30, 0x52, 0x6e, 0x63, 0x52, 0x6e, 0x77, 0x52, 0x6e, 0x70, 0x52, 0x6e, 0x57, 0x52, 0x6e, 0x2b, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0x52, 0x6e, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00,
  0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0xff, 0x00,
#endif
#if LV_COLOR_DEPTH == 32
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00,
  0xff, 0xff, 0xff, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x03, 0x6d, 0x4d, 0x50, 0x47, 0x6d, 0x4d, 0x50, 0x93, 0x6d, 0x4d, 0x50, 0xc7, 0x6d, 0x4d, 0x50, 0xdc, 0x6d, 0x4d, 0x50, 0xd4, 0x6d, 0x4d, 0x50, 0xbb, 0x6d, 0x4d, 0x50, 0x8f, 0x6d, 0x4d, 0x50, 0x3f, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0xff, 0xff, 0xff, 0x00,
  0xff, 0xff, 0xff, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x47, 0x6d, 0x4d, 0x50, 0xd0, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xc4, 0x6d, 0x4d, 0x50, 0x38, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0xff, 0xff, 0xff, 0x00,
  0xff, 0xff, 0xff, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x03, 0x6d, 0x4d, 0x50, 0x88, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xfc, 0x6d, 0x4d, 0x50, 0x73, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0xff, 0xff, 0xff, 0x00,
  0xff, 0xff, 0xff, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0xa3, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0x87, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0xff, 0xff, 0xff, 0x00,
  0xff, 0xff, 0xff, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x6b, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0x50, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0xff, 0xff, 0xff, 0x00,
  0xff, 0xff, 0xff, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x14, 0x6d, 0x4d, 0x50, 0xf0, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xe4, 0x6d, 0x4d, 0x50, 0x08, 0x6d, 0x4d, 0x50, 0x00, 0xff, 0xff, 0xff, 0x00,
  0xff, 0xff, 0xff, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x80, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0xad, 0x9a, 0x9c, 0xff, 0xd6, 0xcd, 0xce, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0x68, 0x6d, 0x4d, 0x50, 0x00, 0xff, 0xff, 0xff, 0x00,
  0xff, 0xff, 0xff, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0xd7, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0xbb, 0xad, 0xae, 0xff, 0xc9, 0xbd, 0xbe, 0xff, 0x92, 0x7a, 0x7c, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x82, 0x66, 0x69, 0xff, 0xd6, 0xcd, 0xce, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdb, 0xd3, 0xd4, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xbf, 0x6d, 0x4d, 0x50, 0x00, 0xff, 0xff, 0xff, 0x00,
  0xff, 0xff, 0xff, 0x00, 0x6d, 0x4d, 0x50, 0x14, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0xc7, 0xbb, 0xbc, 0xff, 0xda, 0xd2, 0xd2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb2, 0xa1, 0xa2, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x8b, 0x71, 0x74, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdc, 0xd4, 0xd4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe9, 0xe4, 0xe5, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xf4, 0x6d, 0x4d, 0x50, 0x00, 0xff, 0xff, 0xff, 0x00,
  0xff, 0xff, 0xff, 0x00, 0x6d, 0x4d, 0x50, 0x34, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0xa2, 0x8e, 0x90, 0xff, 0xde, 0xd7, 0xd8, 0xff, 0xa4, 0x90, 0x92, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa8, 0x95, 0x97, 0xff, 0x9a, 0x84, 0x86, 0xff, 0xda, 0xd2, 0xd3, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0x13, 0xff, 0xff, 0xff, 0x00,
  0xff, 0xff, 0xff, 0x00, 0x6d, 0x4d, 0x50, 0x3c, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x96, 0x7f, 0x81, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb3, 0xa3, 0xa4, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x8e, 0x75, 0x78, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0x20, 0xff, 0xff, 0xff, 0x00,
  0xff, 0xff, 0xff, 0x00, 0x6d, 0x4d, 0x50, 0x2b, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x9e, 0x88, 0x8a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa9, 0x96, 0x97, 0xff, 0xc7, 0xbb, 0xbc, 0xff, 0xac, 0x9a, 0x9b, 0xff, 0x9a, 0x83, 0x86, 0xff, 0xd7, 0xcf, 0xd0, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0x13, 0xff, 0xff, 0xff, 0x00,
  0xff, 0xff, 0xff, 0x00, 0x6d, 0x4d, 0x50, 0x07, 0x6d, 0x4d, 0x50, 0xf8, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0xc6, 0xb9, 0xba, 0xff, 0xd9, 0xd0, 0xd1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa7, 0x94, 0x96, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x9b, 0x85, 0x87, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdc, 0xd4, 0xd5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe8, 0xe2, 0xe3, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xe7, 0x6d, 0x4d, 0x50, 0x00, 0xff, 0xff, 0xff, 0x00,
  0xff, 0xff, 0xff, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0xb8, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0xbc, 0xad, 0xaf, 0xff, 0xc9, 0xbd, 0xbe, 0xff, 0x92, 0x7a, 0x7d, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x83, 0x68, 0x6a, 0xff, 0xd5, 0xcc, 0xcd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdd, 0xd5, 0xd6, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xa0, 0x6d, 0x4d, 0x50, 0x00, 0xff, 0xff, 0xff, 0x00,
  0xff, 0xff, 0xff, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x54, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0xad, 0x9a, 0x9c, 0xff, 0xd6, 0xcd, 0xce, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0x3c, 0x6d, 0x4d, 0x50, 0x00, 0xff, 0xff, 0xff, 0x00,
  0xff, 0xff, 0xff, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x03, 0x6d, 0x4d, 0x50, 0xcc, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xb7, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0xff, 0xff, 0xff, 0x00,
  0xff, 0xff, 0xff, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x2c, 0x6d, 0x4d, 0x50, 0xf4, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xeb, 0x6d, 0x4d, 0x50, 0x1c, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0xff, 0xff, 0xff, 0x00,
  0xff, 0xff, 0xff, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x4b, 0x6d, 0x4d, 0x50, 0xf3, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xec, 0x6d, 0x4d, 0x50, 0x37, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0xff, 0xff, 0xff, 0x00,
  0xff, 0xff, 0xff, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x2f, 0x6d, 0x4d, 0x50, 0xdb, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xd0, 0x6d, 0x4d, 0x50, 0x23, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0xff, 0xff, 0xff, 0x00,
  0xff, 0xff, 0xff, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x07, 0x6d, 0x4d, 0x50, 0x70, 0x6d, 0x4d, 0x50, 0xe0, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xff, 0x6d, 0x4d, 0x50, 0xd8, 0x6d, 0x4d, 0x50, 0x63, 0x6d, 0x4d, 0x50, 0x03, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0xff, 0xff, 0xff, 0x00,
  0xff, 0xff, 0xff, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x03, 0x6d, 0x4d, 0x50, 0x30, 0x6d, 0x4d, 0x50, 0x63, 0x6d, 0x4d, 0x50, 0x77, 0x6d, 0x4d, 0x50, 0x70, 0x6d, 0x4d, 0x50, 0x57, 0x6d, 0x4d, 0x50, 0x2b, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0x6d, 0x4d, 0x50, 0x00, 0xff, 0xff, 0xff, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00,
  0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00,
#endif
};

lv_img_dsc_t img_lv_demo_music_btn_rnd = {
  .header.always_zero = 0,
  .header.w = 24,
  .header.h = 24,
  .data_size = 576 * LV_IMG_PX_SIZE_ALPHA_BYTE,
  .header.cf = LV_IMG_CF_TRUE_COLOR_ALPHA,
  .data = img_lv_demo_music_btn_rnd_map,
};

#endif /*LV_USE_DEMO_MUSIC*/


