#ifndef KEY_CONTROL_H
#define KEY_CONTROL_H

#include "main.h"
#include "lvgl.h"
#include "gui_guider.h"

extern uint32_t key_num;

/* 按键定义 (根据您的GPIO配置) */
typedef enum {
    KEY_UP = 0,      /* PE0 - 上键 */
		KEY_RIGHT,       /* PE1 - 右键 */ 
    KEY_OK,          /* PE2 - 确认键 */
    KEY_LEFT,        /* PE3 - 左键 */
    KEY_DOWN,        /* PE4 - 下键 */
    KEY_POWER,       /* PE5 - 开机键 */
		KEY_BACK,        /* PE6 - 返回键 */
    KEY_MAX
} key_type_t;

/* 界面状态定义 */
typedef enum {
    SCREEN_STARTUP = 0,  /* 开机界面 */
    SCREEN_MAIN,         /* 主界面 */
    SCREEN_MENU,         /* 菜单界面 */
    SCREEN_SUB,          /* 子界面 */
    SCREEN_MAX
} screen_state_t;

/* 按键控制结构体 */
typedef struct {
		screen_state_t current_screen;      /* 当前界面状态 */
    //uint8_t menu_selected_index;        /* 菜单选中的按钮索引 */
    //lv_ui *ui;                          /* UI结构体指针 */
    uint32_t key_press_time[KEY_MAX];   /* 按键按下时间记录 */
    uint8_t key_press_state[KEY_MAX];   /* 按键按下状态记录 */
} key_control_t;

/* 全局变量声明 */
extern key_control_t g_key_ctrl;

/* 函数声明 */
void key_control_init(void);
void key_interrupt_handler(key_type_t key);
void process_key_press(key_type_t key);
void key_process_pending_events(void);  // 新增：处理挂起的按键事件
void key_GPS(key_type_t key);
#endif /* KEY_CONTROL_H */