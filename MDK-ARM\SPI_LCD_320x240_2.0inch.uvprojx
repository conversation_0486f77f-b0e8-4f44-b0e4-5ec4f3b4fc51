<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>SPI_LCD_320x240_2.0inch</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>6230000::V6.23::ARMCLANG</pCCUsed>
      <uAC6>1</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>STM32F407VETx</Device>
          <Vendor>STMicroelectronics</Vendor>
          <PackID>Keil.STM32F4xx_DFP.2.16.0</PackID>
          <PackURL>http://www.keil.com/pack/</PackURL>
          <Cpu>IRAM(0x20000000-0x2001BFFF) IRAM2(0x2001C000-0x2001FFFF) IROM(0x8000000-0x807FFFF) CLOCK(25000000) FPU2 CPUTYPE("Cortex-M4") TZ</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll></FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile></RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:STM32F407VETx$CMSIS\SVD\STM32F407.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>SPI_LCD_320x240_2.0inch\</OutputDirectory>
          <OutputName>SPI_LCD_320x240_2.0inch</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath></ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>1</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>0</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments>-REMAP -MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments>-MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4101</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2V8M.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <nBranchProt>0</nBranchProt>
            <hadIRAM2>1</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>4</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>1</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x1c000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x80000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x80000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x1c000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x2001c000</StartAddress>
                <Size>0x4000</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>4</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>3</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>3</v6Lang>
            <v6LangP>3</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>USE_HAL_DRIVER,STM32F407xx</Define>
              <Undefine></Undefine>
              <IncludePath>../Core/Inc;../Drivers/STM32F4xx_HAL_Driver/Inc;../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy;../Drivers/CMSIS/Device/ST/STM32F4xx/Include;../Drivers/CMSIS/Include;../BSP/LCD_ST7789;../lvgl;../lvgl/src;../lvgl/examples/porting;../guider/src/generated;../guider/src/custom;../guider/src/generated/guider_customer_fonts;../guider/analogclock;../guider/dclock;../lvgl/src/misc;../lvgl/src/hal;../lvgl/src/core;../lvgl/src/font;../lvgl/design_my;..\guider\keyboard;..\lvgl\src\widgets</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>1</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange></TextAddressRange>
            <DataAddressRange></DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>Application/MDK-ARM</GroupName>
          <Files>
            <File>
              <FileName>startup_stm32f407xx.s</FileName>
              <FileType>2</FileType>
              <FilePath>startup_stm32f407xx.s</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Application/User/Core</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Core/Src/main.c</FilePath>
            </File>
            <File>
              <FileName>gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Core/Src/gpio.c</FilePath>
            </File>
            <File>
              <FileName>iwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Core/Src/iwdg.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>1</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <uGnu>2</uGnu>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>2</vShortEn>
                    <vShortWch>2</vShortWch>
                    <v6Lto>2</v6Lto>
                    <v6WtE>2</v6WtE>
                    <v6Rtti>2</v6Rtti>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Core/Src/rtc.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>1</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <uGnu>2</uGnu>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>2</vShortEn>
                    <vShortWch>2</vShortWch>
                    <v6Lto>2</v6Lto>
                    <v6WtE>2</v6WtE>
                    <v6Rtti>2</v6Rtti>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Core/Src/spi.c</FilePath>
            </File>
            <File>
              <FileName>tim.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Core/Src/tim.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>1</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <uGnu>2</uGnu>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>2</vShortEn>
                    <vShortWch>2</vShortWch>
                    <v6Lto>2</v6Lto>
                    <v6WtE>2</v6WtE>
                    <v6Rtti>2</v6Rtti>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>stm32f4xx_it.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Core/Src/stm32f4xx_it.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_msp.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Core/Src/stm32f4xx_hal_msp.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Drivers/STM32F4xx_HAL_Driver</GroupName>
          <Files>
            <File>
              <FileName>stm32f4xx_hal_iwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_iwdg.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>1</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <uGnu>2</uGnu>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>2</vShortEn>
                    <vShortWch>2</vShortWch>
                    <v6Lto>2</v6Lto>
                    <v6WtE>2</v6WtE>
                    <v6Rtti>2</v6Rtti>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>stm32f4xx_hal_rcc.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_rcc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_flash_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_flash_ramfunc.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_dma_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_pwr.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_pwr_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_cortex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_exti.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rtc.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>1</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <uGnu>2</uGnu>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>2</vShortEn>
                    <vShortWch>2</vShortWch>
                    <v6Lto>2</v6Lto>
                    <v6WtE>2</v6WtE>
                    <v6Rtti>2</v6Rtti>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>stm32f4xx_hal_rtc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rtc_ex.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>1</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <uGnu>2</uGnu>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>2</vShortEn>
                    <vShortWch>2</vShortWch>
                    <v6Lto>2</v6Lto>
                    <v6WtE>2</v6WtE>
                    <v6Rtti>2</v6Rtti>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>stm32f4xx_hal_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_spi.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_tim.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_tim_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Drivers/CMSIS</GroupName>
          <Files>
            <File>
              <FileName>system_stm32f4xx.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Core/Src/system_stm32f4xx.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>LCD</GroupName>
          <Files>
            <File>
              <FileName>lcd_core.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\LCD_ST7789\lcd_core.c</FilePath>
            </File>
            <File>
              <FileName>lcd_font.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\LCD_ST7789\lcd_font.c</FilePath>
            </File>
            <File>
              <FileName>lcd_port.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\LCD_ST7789\lcd_port.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>LVGL_myGui</GroupName>
          <Files>
            <File>
              <FileName>key_control.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\design_my\key_control.c</FilePath>
            </File>
            <File>
              <FileName>rtc_clock_display.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\design_my\rtc_clock_display.c</FilePath>
            </File>
            <File>
              <FileName>group_add.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\design_my\group_add.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>LVGL_conf</GroupName>
          <Files>
            <File>
              <FileName>lv_conf.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\lvgl\lv_conf.h</FilePath>
            </File>
            <File>
              <FileName>lvgl.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\lvgl\lvgl.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>LVGL_porting</GroupName>
          <Files>
            <File>
              <FileName>lv_port_disp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\examples\porting\lv_port_disp.c</FilePath>
            </File>
            <File>
              <FileName>lv_port_disp.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\lvgl\examples\porting\lv_port_disp.h</FilePath>
            </File>
            <File>
              <FileName>lv_port_indev.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\examples\porting\lv_port_indev.c</FilePath>
            </File>
            <File>
              <FileName>lv_port_indev.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\lvgl\examples\porting\lv_port_indev.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>LVGL_src</GroupName>
          <Files>
            <File>
              <FileName>lv_disp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\core\lv_disp.c</FilePath>
            </File>
            <File>
              <FileName>lv_event.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\core\lv_event.c</FilePath>
            </File>
            <File>
              <FileName>lv_group.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\core\lv_group.c</FilePath>
            </File>
            <File>
              <FileName>lv_indev.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\core\lv_indev.c</FilePath>
            </File>
            <File>
              <FileName>lv_indev_scroll.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\core\lv_indev_scroll.c</FilePath>
            </File>
            <File>
              <FileName>lv_obj.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\core\lv_obj.c</FilePath>
            </File>
            <File>
              <FileName>lv_obj_class.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\core\lv_obj_class.c</FilePath>
            </File>
            <File>
              <FileName>lv_obj_draw.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\core\lv_obj_draw.c</FilePath>
            </File>
            <File>
              <FileName>lv_obj_pos.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\core\lv_obj_pos.c</FilePath>
            </File>
            <File>
              <FileName>lv_obj_scroll.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\core\lv_obj_scroll.c</FilePath>
            </File>
            <File>
              <FileName>lv_obj_style.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\core\lv_obj_style.c</FilePath>
            </File>
            <File>
              <FileName>lv_obj_style_gen.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\core\lv_obj_style_gen.c</FilePath>
            </File>
            <File>
              <FileName>lv_obj_tree.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\core\lv_obj_tree.c</FilePath>
            </File>
            <File>
              <FileName>lv_refr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\core\lv_refr.c</FilePath>
            </File>
            <File>
              <FileName>lv_theme.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\core\lv_theme.c</FilePath>
            </File>
            <File>
              <FileName>lv_gpu_arm2d.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\arm2d\lv_gpu_arm2d.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_pxp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\nxp\pxp\lv_draw_pxp.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_pxp_blend.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\nxp\pxp\lv_draw_pxp_blend.c</FilePath>
            </File>
            <File>
              <FileName>lv_gpu_nxp_pxp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\nxp\pxp\lv_gpu_nxp_pxp.c</FilePath>
            </File>
            <File>
              <FileName>lv_gpu_nxp_pxp_osa.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\nxp\pxp\lv_gpu_nxp_pxp_osa.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_vglite.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\nxp\vglite\lv_draw_vglite.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_vglite_arc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\nxp\vglite\lv_draw_vglite_arc.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_vglite_blend.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\nxp\vglite\lv_draw_vglite_blend.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_vglite_line.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\nxp\vglite\lv_draw_vglite_line.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_vglite_rect.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\nxp\vglite\lv_draw_vglite_rect.c</FilePath>
            </File>
            <File>
              <FileName>lv_vglite_buf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\nxp\vglite\lv_vglite_buf.c</FilePath>
            </File>
            <File>
              <FileName>lv_vglite_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\nxp\vglite\lv_vglite_utils.c</FilePath>
            </File>
            <File>
              <FileName>lv_gpu_d2_draw_label.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\renesas\lv_gpu_d2_draw_label.c</FilePath>
            </File>
            <File>
              <FileName>lv_gpu_d2_ra6m3.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\renesas\lv_gpu_d2_ra6m3.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sdl.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\sdl\lv_draw_sdl.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sdl_arc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\sdl\lv_draw_sdl_arc.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sdl_bg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\sdl\lv_draw_sdl_bg.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sdl_composite.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\sdl\lv_draw_sdl_composite.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sdl_img.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\sdl\lv_draw_sdl_img.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sdl_label.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\sdl\lv_draw_sdl_label.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sdl_layer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\sdl\lv_draw_sdl_layer.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sdl_line.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\sdl\lv_draw_sdl_line.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sdl_mask.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\sdl\lv_draw_sdl_mask.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sdl_polygon.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\sdl\lv_draw_sdl_polygon.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sdl_rect.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\sdl\lv_draw_sdl_rect.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sdl_stack_blur.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\sdl\lv_draw_sdl_stack_blur.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sdl_texture_cache.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\sdl\lv_draw_sdl_texture_cache.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sdl_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\sdl\lv_draw_sdl_utils.c</FilePath>
            </File>
            <File>
              <FileName>lv_gpu_stm32_dma2d.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\stm32_dma2d\lv_gpu_stm32_dma2d.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\sw\lv_draw_sw.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_arc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\sw\lv_draw_sw_arc.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_blend.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\sw\lv_draw_sw_blend.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_dither.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\sw\lv_draw_sw_dither.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_gradient.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\sw\lv_draw_sw_gradient.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_img.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\sw\lv_draw_sw_img.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_layer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\sw\lv_draw_sw_layer.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_letter.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\sw\lv_draw_sw_letter.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_line.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\sw\lv_draw_sw_line.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_polygon.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\sw\lv_draw_sw_polygon.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_rect.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\sw\lv_draw_sw_rect.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_transform.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\sw\lv_draw_sw_transform.c</FilePath>
            </File>
            <File>
              <FileName>lv_gpu_swm341_dma2d.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\swm341_dma2d\lv_gpu_swm341_dma2d.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\lv_draw.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_arc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\lv_draw_arc.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_img.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\lv_draw_img.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_label.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\lv_draw_label.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_layer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\lv_draw_layer.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_line.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\lv_draw_line.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_mask.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\lv_draw_mask.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_rect.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\lv_draw_rect.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_transform.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\lv_draw_transform.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_triangle.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\lv_draw_triangle.c</FilePath>
            </File>
            <File>
              <FileName>lv_img_buf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\lv_img_buf.c</FilePath>
            </File>
            <File>
              <FileName>lv_img_cache.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\lv_img_cache.c</FilePath>
            </File>
            <File>
              <FileName>lv_img_decoder.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\draw\lv_img_decoder.c</FilePath>
            </File>
            <File>
              <FileName>lv_flex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\layouts\flex\lv_flex.c</FilePath>
            </File>
            <File>
              <FileName>lv_grid.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\layouts\grid\lv_grid.c</FilePath>
            </File>
            <File>
              <FileName>lv_bmp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\libs\bmp\lv_bmp.c</FilePath>
            </File>
            <File>
              <FileName>lv_ffmpeg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\libs\ffmpeg\lv_ffmpeg.c</FilePath>
            </File>
            <File>
              <FileName>lv_freetype.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\libs\freetype\lv_freetype.c</FilePath>
            </File>
            <File>
              <FileName>lv_fs_fatfs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\libs\fsdrv\lv_fs_fatfs.c</FilePath>
            </File>
            <File>
              <FileName>lv_fs_littlefs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\libs\fsdrv\lv_fs_littlefs.c</FilePath>
            </File>
            <File>
              <FileName>lv_fs_posix.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\libs\fsdrv\lv_fs_posix.c</FilePath>
            </File>
            <File>
              <FileName>lv_fs_stdio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\libs\fsdrv\lv_fs_stdio.c</FilePath>
            </File>
            <File>
              <FileName>lv_fs_win32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\libs\fsdrv\lv_fs_win32.c</FilePath>
            </File>
            <File>
              <FileName>gifdec.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\libs\gif\gifdec.c</FilePath>
            </File>
            <File>
              <FileName>lv_gif.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\libs\gif\lv_gif.c</FilePath>
            </File>
            <File>
              <FileName>lodepng.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\libs\png\lodepng.c</FilePath>
            </File>
            <File>
              <FileName>lv_png.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\libs\png\lv_png.c</FilePath>
            </File>
            <File>
              <FileName>lv_qrcode.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\libs\qrcode\lv_qrcode.c</FilePath>
            </File>
            <File>
              <FileName>qrcodegen.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\libs\qrcode\qrcodegen.c</FilePath>
            </File>
            <File>
              <FileName>lv_rlottie.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\libs\rlottie\lv_rlottie.c</FilePath>
            </File>
            <File>
              <FileName>lv_sjpg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\libs\sjpg\lv_sjpg.c</FilePath>
            </File>
            <File>
              <FileName>tjpgd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\libs\sjpg\tjpgd.c</FilePath>
            </File>
            <File>
              <FileName>lv_tiny_ttf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\libs\tiny_ttf\lv_tiny_ttf.c</FilePath>
            </File>
            <File>
              <FileName>lv_fragment.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\others\fragment\lv_fragment.c</FilePath>
            </File>
            <File>
              <FileName>lv_fragment_manager.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\others\fragment\lv_fragment_manager.c</FilePath>
            </File>
            <File>
              <FileName>lv_gridnav.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\others\gridnav\lv_gridnav.c</FilePath>
            </File>
            <File>
              <FileName>lv_ime_pinyin.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\others\ime\lv_ime_pinyin.c</FilePath>
            </File>
            <File>
              <FileName>lv_imgfont.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\others\imgfont\lv_imgfont.c</FilePath>
            </File>
            <File>
              <FileName>lv_monkey.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\others\monkey\lv_monkey.c</FilePath>
            </File>
            <File>
              <FileName>lv_msg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\others\msg\lv_msg.c</FilePath>
            </File>
            <File>
              <FileName>lv_snapshot.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\others\snapshot\lv_snapshot.c</FilePath>
            </File>
            <File>
              <FileName>lv_theme_basic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\themes\basic\lv_theme_basic.c</FilePath>
            </File>
            <File>
              <FileName>lv_theme_default.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\themes\default\lv_theme_default.c</FilePath>
            </File>
            <File>
              <FileName>lv_theme_mono.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\themes\mono\lv_theme_mono.c</FilePath>
            </File>
            <File>
              <FileName>lv_animimg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\widgets\animimg\lv_animimg.c</FilePath>
            </File>
            <File>
              <FileName>lv_calendar.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\widgets\calendar\lv_calendar.c</FilePath>
            </File>
            <File>
              <FileName>lv_calendar_header_arrow.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\widgets\calendar\lv_calendar_header_arrow.c</FilePath>
            </File>
            <File>
              <FileName>lv_calendar_header_dropdown.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\widgets\calendar\lv_calendar_header_dropdown.c</FilePath>
            </File>
            <File>
              <FileName>lv_chart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\widgets\chart\lv_chart.c</FilePath>
            </File>
            <File>
              <FileName>lv_colorwheel.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\widgets\colorwheel\lv_colorwheel.c</FilePath>
            </File>
            <File>
              <FileName>lv_imgbtn.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\widgets\imgbtn\lv_imgbtn.c</FilePath>
            </File>
            <File>
              <FileName>lv_keyboard.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\widgets\keyboard\lv_keyboard.c</FilePath>
            </File>
            <File>
              <FileName>lv_led.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\widgets\led\lv_led.c</FilePath>
            </File>
            <File>
              <FileName>lv_list.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\widgets\list\lv_list.c</FilePath>
            </File>
            <File>
              <FileName>lv_menu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\widgets\menu\lv_menu.c</FilePath>
            </File>
            <File>
              <FileName>lv_meter.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\widgets\meter\lv_meter.c</FilePath>
            </File>
            <File>
              <FileName>lv_msgbox.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\widgets\msgbox\lv_msgbox.c</FilePath>
            </File>
            <File>
              <FileName>lv_span.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\widgets\span\lv_span.c</FilePath>
            </File>
            <File>
              <FileName>lv_spinbox.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\widgets\spinbox\lv_spinbox.c</FilePath>
            </File>
            <File>
              <FileName>lv_spinner.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\widgets\spinner\lv_spinner.c</FilePath>
            </File>
            <File>
              <FileName>lv_tabview.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\widgets\tabview\lv_tabview.c</FilePath>
            </File>
            <File>
              <FileName>lv_tileview.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\widgets\tileview\lv_tileview.c</FilePath>
            </File>
            <File>
              <FileName>lv_win.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\widgets\win\lv_win.c</FilePath>
            </File>
            <File>
              <FileName>lv_extra.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\extra\lv_extra.c</FilePath>
            </File>
            <File>
              <FileName>lv_font.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\font\lv_font.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_dejavu_16_persian_hebrew.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\font\lv_font_dejavu_16_persian_hebrew.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_fmt_txt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\font\lv_font_fmt_txt.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_loader.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\font\lv_font_loader.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_8.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\font\lv_font_montserrat_8.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_10.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\font\lv_font_montserrat_10.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_12.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\font\lv_font_montserrat_12.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_12_subpx.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\font\lv_font_montserrat_12_subpx.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_14.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\font\lv_font_montserrat_14.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_16.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\font\lv_font_montserrat_16.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_18.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\font\lv_font_montserrat_18.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_20.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\font\lv_font_montserrat_20.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_22.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\font\lv_font_montserrat_22.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_24.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\font\lv_font_montserrat_24.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_26.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\font\lv_font_montserrat_26.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_28.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\font\lv_font_montserrat_28.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_28_compressed.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\font\lv_font_montserrat_28_compressed.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_30.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\font\lv_font_montserrat_30.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\font\lv_font_montserrat_32.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_34.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\font\lv_font_montserrat_34.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_36.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\font\lv_font_montserrat_36.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_38.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\font\lv_font_montserrat_38.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_40.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\font\lv_font_montserrat_40.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_42.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\font\lv_font_montserrat_42.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_44.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\font\lv_font_montserrat_44.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_46.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\font\lv_font_montserrat_46.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_48.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\font\lv_font_montserrat_48.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_simsun_16_cjk.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\font\lv_font_simsun_16_cjk.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_unscii_8.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\font\lv_font_unscii_8.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_unscii_16.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\font\lv_font_unscii_16.c</FilePath>
            </File>
            <File>
              <FileName>lv_hal_disp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\hal\lv_hal_disp.c</FilePath>
            </File>
            <File>
              <FileName>lv_hal_indev.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\hal\lv_hal_indev.c</FilePath>
            </File>
            <File>
              <FileName>lv_hal_tick.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\hal\lv_hal_tick.c</FilePath>
            </File>
            <File>
              <FileName>lv_anim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\misc\lv_anim.c</FilePath>
            </File>
            <File>
              <FileName>lv_anim_timeline.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\misc\lv_anim_timeline.c</FilePath>
            </File>
            <File>
              <FileName>lv_area.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\misc\lv_area.c</FilePath>
            </File>
            <File>
              <FileName>lv_async.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\misc\lv_async.c</FilePath>
            </File>
            <File>
              <FileName>lv_bidi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\misc\lv_bidi.c</FilePath>
            </File>
            <File>
              <FileName>lv_color.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\misc\lv_color.c</FilePath>
            </File>
            <File>
              <FileName>lv_fs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\misc\lv_fs.c</FilePath>
            </File>
            <File>
              <FileName>lv_gc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\misc\lv_gc.c</FilePath>
            </File>
            <File>
              <FileName>lv_ll.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\misc\lv_ll.c</FilePath>
            </File>
            <File>
              <FileName>lv_log.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\misc\lv_log.c</FilePath>
            </File>
            <File>
              <FileName>lv_lru.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\misc\lv_lru.c</FilePath>
            </File>
            <File>
              <FileName>lv_math.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\misc\lv_math.c</FilePath>
            </File>
            <File>
              <FileName>lv_mem.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\misc\lv_mem.c</FilePath>
            </File>
            <File>
              <FileName>lv_printf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\misc\lv_printf.c</FilePath>
            </File>
            <File>
              <FileName>lv_style.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\misc\lv_style.c</FilePath>
            </File>
            <File>
              <FileName>lv_style_gen.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\misc\lv_style_gen.c</FilePath>
            </File>
            <File>
              <FileName>lv_templ.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\misc\lv_templ.c</FilePath>
            </File>
            <File>
              <FileName>lv_timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\misc\lv_timer.c</FilePath>
            </File>
            <File>
              <FileName>lv_tlsf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\misc\lv_tlsf.c</FilePath>
            </File>
            <File>
              <FileName>lv_txt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\misc\lv_txt.c</FilePath>
            </File>
            <File>
              <FileName>lv_txt_ap.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\misc\lv_txt_ap.c</FilePath>
            </File>
            <File>
              <FileName>lv_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\misc\lv_utils.c</FilePath>
            </File>
            <File>
              <FileName>lv_arc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\widgets\lv_arc.c</FilePath>
            </File>
            <File>
              <FileName>lv_bar.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\widgets\lv_bar.c</FilePath>
            </File>
            <File>
              <FileName>lv_btn.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\widgets\lv_btn.c</FilePath>
            </File>
            <File>
              <FileName>lv_btnmatrix.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\widgets\lv_btnmatrix.c</FilePath>
            </File>
            <File>
              <FileName>lv_canvas.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\widgets\lv_canvas.c</FilePath>
            </File>
            <File>
              <FileName>lv_checkbox.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\widgets\lv_checkbox.c</FilePath>
            </File>
            <File>
              <FileName>lv_dropdown.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\widgets\lv_dropdown.c</FilePath>
            </File>
            <File>
              <FileName>lv_img.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\widgets\lv_img.c</FilePath>
            </File>
            <File>
              <FileName>lv_label.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\widgets\lv_label.c</FilePath>
            </File>
            <File>
              <FileName>lv_line.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\widgets\lv_line.c</FilePath>
            </File>
            <File>
              <FileName>lv_objx_templ.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\widgets\lv_objx_templ.c</FilePath>
            </File>
            <File>
              <FileName>lv_roller.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\widgets\lv_roller.c</FilePath>
            </File>
            <File>
              <FileName>lv_slider.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\widgets\lv_slider.c</FilePath>
            </File>
            <File>
              <FileName>lv_switch.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\widgets\lv_switch.c</FilePath>
            </File>
            <File>
              <FileName>lv_table.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\widgets\lv_table.c</FilePath>
            </File>
            <File>
              <FileName>lv_textarea.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lvgl\src\widgets\lv_textarea.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Guider</GroupName>
          <Files>
            <File>
              <FileName>custom.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\guider\src\custom\custom.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_SourceHanSerifSC_Regular_20.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\guider\src\generated\guider_fonts\lv_font_SourceHanSerifSC_Regular_20.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_SourceHanSerifSC_Regular_48.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\guider\src\generated\guider_fonts\lv_font_SourceHanSerifSC_Regular_48.c</FilePath>
            </File>
            <File>
              <FileName>_LOGO_alpha_180x180.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\guider\src\generated\images\_LOGO_alpha_180x180.c</FilePath>
            </File>
            <File>
              <FileName>events_init.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\guider\src\generated\events_init.c</FilePath>
            </File>
            <File>
              <FileName>gui_guider.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\guider\src\generated\gui_guider.c</FilePath>
            </File>
            <File>
              <FileName>setup_scr_screen_1.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\guider\src\generated\setup_scr_screen_1.c</FilePath>
            </File>
            <File>
              <FileName>setup_scr_screen_2.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\guider\src\generated\setup_scr_screen_2.c</FilePath>
            </File>
            <File>
              <FileName>setup_scr_screen_3.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\guider\src\generated\setup_scr_screen_3.c</FilePath>
            </File>
            <File>
              <FileName>setup_scr_screen_4.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\guider\src\generated\setup_scr_screen_4.c</FilePath>
            </File>
            <File>
              <FileName>setup_scr_screen_5.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\guider\src\generated\setup_scr_screen_5.c</FilePath>
            </File>
            <File>
              <FileName>setup_scr_screen_6.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\guider\src\generated\setup_scr_screen_6.c</FilePath>
            </File>
            <File>
              <FileName>setup_scr_screen_7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\guider\src\generated\setup_scr_screen_7.c</FilePath>
            </File>
            <File>
              <FileName>setup_scr_screen_8.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\guider\src\generated\setup_scr_screen_8.c</FilePath>
            </File>
            <File>
              <FileName>setup_scr_screen_9.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\guider\src\generated\setup_scr_screen_9.c</FilePath>
            </File>
            <File>
              <FileName>setup_scr_screen_10.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\guider\src\generated\setup_scr_screen_10.c</FilePath>
            </File>
            <File>
              <FileName>setup_scr_screen_11.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\guider\src\generated\setup_scr_screen_11.c</FilePath>
            </File>
            <File>
              <FileName>setup_scr_screen_12.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\guider\src\generated\setup_scr_screen_12.c</FilePath>
            </File>
            <File>
              <FileName>setup_scr_screen_13.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\guider\src\generated\setup_scr_screen_13.c</FilePath>
            </File>
            <File>
              <FileName>setup_scr_screen_14.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\guider\src\generated\setup_scr_screen_14.c</FilePath>
            </File>
            <File>
              <FileName>setup_scr_screen_15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\guider\src\generated\setup_scr_screen_15.c</FilePath>
            </File>
            <File>
              <FileName>widgets_init.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\guider\src\generated\widgets_init.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>::CMSIS</GroupName>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components>
      <component Cclass="CMSIS" Cgroup="CORE" Cvendor="ARM" Cversion="6.1.0" condition="ARMv6_7_8-M Device">
        <package name="CMSIS" schemaVersion="1.7.36" url="https://www.keil.com/pack/" vendor="ARM" version="6.1.0"/>
        <targetInfos>
          <targetInfo name="SPI_LCD_320x240_2.0inch"/>
        </targetInfos>
      </component>
    </components>
    <files/>
  </RTE>

  <LayerInfo>
    <Layers>
      <Layer>
        <LayName>SPI_LCD_320x240_2</LayName>
        <LayPrjMark>1</LayPrjMark>
      </Layer>
    </Layers>
  </LayerInfo>

</Project>
