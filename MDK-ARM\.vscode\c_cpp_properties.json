{"configurations": [{"name": "SPI_LCD_320x240_2.0inch", "includePath": ["e:\\keilf4\\SPI_LCD_320x240_2.0inch\\Core\\Inc", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\Drivers\\STM32F4xx_HAL_Driver\\Inc", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\Drivers\\STM32F4xx_HAL_Driver\\Inc\\Legacy", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\Drivers\\CMSIS\\Device\\ST\\STM32F4xx\\Include", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\Drivers\\CMSIS\\Include", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\BSP\\LCD_ST7789", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\examples\\porting", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\guider\\src\\generated", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\guider\\src\\custom", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\guider\\src\\generated\\guider_customer_fonts", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\guider\\analogclock", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\guider\\dclock", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\MDK-ARM", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\Core\\Src", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\Drivers\\STM32F4xx_HAL_Driver\\Src", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\core", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\draw\\arm2d", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\draw\\nxp\\pxp", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\draw\\nxp\\vglite", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\draw\\renesas", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\draw\\sdl", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\draw\\stm32_dma2d", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\draw\\sw", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\draw\\swm341_dma2d", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\draw", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\layouts\\flex", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\layouts\\grid", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\libs\\bmp", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\libs\\ffmpeg", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\libs\\freetype", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\libs\\fsdrv", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\libs\\gif", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\libs\\png", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\libs\\qrcode", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\libs\\rlottie", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\libs\\sjpg", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\libs\\tiny_ttf", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\others\\fragment", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\others\\gridnav", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\others\\ime", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\others\\imgfont", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\others\\monkey", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\others\\msg", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\others\\snapshot", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\themes\\basic", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\themes\\default", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\themes\\mono", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\widgets\\animimg", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\widgets\\calendar", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\widgets\\chart", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\widgets\\colorwheel", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\widgets\\imgbtn", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\widgets\\keyboard", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\widgets\\led", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\widgets\\list", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\widgets\\menu", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\widgets\\meter", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\widgets\\msgbox", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\widgets\\span", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\widgets\\spinbox", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\widgets\\spinner", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\widgets\\tabview", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\widgets\\tileview", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra\\widgets\\win", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\extra", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\font", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\hal", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\misc", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\lvgl\\src\\widgets", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\guider\\src\\generated\\guider_fonts", "e:\\keilf4\\SPI_LCD_320x240_2.0inch\\guider\\src\\generated\\images"], "defines": ["USE_HAL_DRIVER", "STM32F407xx", "__alignof__(x)=", "__asm(x)=", "__asm__(x)=", "__forceinline=", "__restrict=", "__volatile__=", "__inline=", "__inline__=", "__declspec(x)=", "__attribute__(x)=", "__nonnull__(x)=", "__unaligned=", "__promise(x)=", "__irq=", "__swi=", "__weak=", "__register=", "__pure=", "__value_in_regs=", "__breakpoint(x)=", "__current_pc()=0U", "__current_sp()=0U", "__disable_fiq()=", "__disable_irq()=", "__enable_fiq()=", "__enable_irq()=", "__force_stores()=", "__memory_changed()=", "__schedule_barrier()=", "__semihost(x,y)=0", "__vfp_status(x,y)=0", "__builtin_arm_nop()=", "__builtin_arm_wfi()=", "__builtin_arm_wfe()=", "__builtin_arm_sev()=", "__builtin_arm_sevl()=", "__builtin_arm_yield()=", "__builtin_arm_isb(x)=", "__builtin_arm_dsb(x)=", "__builtin_arm_dmb(x)=", "__builtin_bswap32(x)=0U", "__builtin_bswap16(x)=0U", "__builtin_arm_rbit(x)=0U", "__builtin_clz(x)=0U", "__builtin_arm_ldrex(x)=0U", "__builtin_arm_strex(x,y)=0U", "__builtin_arm_clrex()=", "__builtin_arm_ssat(x,y)=0U", "__builtin_arm_usat(x,y)=0U", "__builtin_arm_ldaex(x)=0U", "__builtin_arm_stlex(x,y)=0U", "__GNUC__=4", "__GNUC_MINOR__=2", "__GNUC_PATCHLEVEL__=1"], "intelliSenseMode": "${default}"}], "version": 4}