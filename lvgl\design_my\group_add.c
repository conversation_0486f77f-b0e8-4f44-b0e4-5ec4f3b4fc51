#include "lvgl.h"
#include <stdio.h>
#include "gui_guider.h"
#include "events_init.h"
#include "widgets_init.h"
#include "custom.h"
#include "group_add.h"
#include "key_control.h"



void menu_group()
{
		extern lv_indev_t * indev_keypad;
		lv_group_t *group=lv_group_create();
    lv_indev_set_group(indev_keypad, group);        //将组绑定到输入设备
 
    lv_group_set_editing(group, false);   //导航模式
    lv_group_add_obj(group ,guider_ui.screen_2_btn_1);
    lv_group_add_obj(group ,guider_ui.screen_2_btn_2);
    lv_group_add_obj(group ,guider_ui.screen_2_btn_3);
    lv_group_add_obj(group ,guider_ui.screen_2_btn_4);
		lv_group_add_obj(group ,guider_ui.screen_2_btn_5);
    lv_group_add_obj(group ,guider_ui.screen_2_btn_6);
    lv_group_add_obj(group ,guider_ui.screen_2_btn_7);
    lv_group_add_obj(group ,guider_ui.screen_2_btn_8);
		lv_group_add_obj(group ,guider_ui.screen_2_btn_9);
    lv_group_add_obj(group ,guider_ui.screen_2_btn_10);
    lv_group_add_obj(group ,guider_ui.screen_2_btn_11);
    lv_group_add_obj(group ,guider_ui.screen_2_btn_12);
}

/**
 * @brief 时间设置界面的LVGL导航组初始化函数
 * 为screen_5界面的时间设置控件创建group并设置焦点导航
 */
void time_setting_group()
{
    extern lv_indev_t * indev_keypad;
    lv_group_t *group = lv_group_create();
    lv_indev_set_group(indev_keypad, group);        // 将组绑定到输入设备

    lv_group_set_editing(group, false);   // 导航模式

    // 按照界面布局顺序添加时间设置控件到group
    // 第一行：年、月
    lv_group_add_obj(group, guider_ui.screen_5_ddlist_1);  // 年份下拉框
    lv_group_add_obj(group, guider_ui.screen_5_ddlist_2);  // 月份下拉框

    // 第二行：日、时
    lv_group_add_obj(group, guider_ui.screen_5_ddlist_3);  // 日期下拉框
    lv_group_add_obj(group, guider_ui.screen_5_ddlist_4);  // 小时下拉框（如果存在）

    // 第三行：分、秒
    lv_group_add_obj(group, guider_ui.screen_5_ddlist_6);  // 分钟下拉框
    lv_group_add_obj(group, guider_ui.screen_5_ddlist_5);  // 秒钟下拉框

    // 最后添加返回按钮
    lv_group_add_obj(group, guider_ui.screen_5_btn_1);     // 返回按钮

    // 设置初始焦点到年份下拉框
    lv_group_focus_obj(guider_ui.screen_5_ddlist_1);
}

/**
 * @brief screen_4界面的LVGL导航组初始化函数
 * 为screen_4界面的控件创建group并设置焦点导航
 */
void screen_4_group()
{
    extern lv_indev_t * indev_keypad;
    lv_group_t *group = lv_group_create();
    lv_indev_set_group(indev_keypad, group);        // 将组绑定到输入设备

    lv_group_set_editing(group, false);   // 导航模式

    // 添加进度条（如果需要导航）
    lv_group_add_obj(group, guider_ui.screen_4_slider_1);     // 进度条

    // 添加返回按钮
    lv_group_add_obj(group, guider_ui.screen_4_btn_1);     // 返回按钮

    // 设置初始焦点到返回按钮
    lv_group_focus_obj(guider_ui.screen_4_slider_1);
}

/**
 * @brief screen_6界面的LVGL导航组初始化函数
 * 为screen_6界面的控件创建group并设置焦点导航
 */
void screen_6_group()
{
    extern lv_indev_t * indev_keypad;
    lv_group_t *group = lv_group_create();
    lv_indev_set_group(indev_keypad, group);        // 将组绑定到输入设备

    lv_group_set_editing(group, false);   // 导航模式

    // 根据screen_6的实际控件添加到group
    // 假设有按钮控件，请根据实际情况调整
    if (guider_ui.screen_6_btn_1 != NULL) {
        lv_group_add_obj(group, guider_ui.screen_6_btn_1);
    }
		lv_group_add_obj(group, guider_ui.screen_6_ddlist_1);
		lv_group_add_obj(group, guider_ui.screen_6_ddlist_2);
		lv_group_add_obj(group, guider_ui.screen_6_ddlist_3);
    // 如果有其他可导航控件，继续添加
    // lv_group_add_obj(group, guider_ui.screen_6_xxx);

    // 设置初始焦点（根据实际控件调整）
    if (guider_ui.screen_6_btn_1 != NULL) {
        lv_group_focus_obj(guider_ui.screen_6_ddlist_1);
    }
}



/* 外部变量 */
extern lv_ui guider_ui;
extern key_control_t g_key_ctrl;

/* 私有变量 */
static screen_state_t last_detected_state = SCREEN_MAX;
static unsigned char state_changed_flag = 0;

/**
 * @brief 获取当前LVGL活动界面对应的状态
 */
screen_state_t screen_detector_get_current_state(void)
{
    // 获取当前活动的屏幕对象
    lv_obj_t *current_screen = lv_scr_act();
    
    if (current_screen == NULL) {
        return SCREEN_MAX;  // 无效状态
    }
    
    // 通过比较屏幕对象指针来确定当前界面
    if (current_screen == guider_ui.screen_1) {
        return SCREEN_STARTUP;  // 开机界面
    }
    else if (current_screen == guider_ui.screen_3) {
        return SCREEN_MAIN;     // 主界面
    }
    else if (current_screen == guider_ui.screen_2) {
        return SCREEN_MENU;     // 菜单界面
    }
    else if (current_screen == guider_ui.screen_5) {
        return SCREEN_SUB;      // 时间设置界面（作为子界面）
    }
    else if (current_screen == guider_ui.screen_4) {
        return SCREEN_SUB;      // 其他子界面
    }
    
    // 如果都不匹配，返回未知状态
    return SCREEN_MAX;
}

/**
 * @brief 自动检测当前LVGL界面并更新状态
 */
void screen_detector_update(void)
{
    screen_state_t current_state = screen_detector_get_current_state();
    
    // 检查状态是否有效
    if (current_state == SCREEN_MAX) {
        return;  // 无效状态，不更新
    }
    
    // 检查状态是否发生变化
    if (current_state != last_detected_state) {
        last_detected_state = current_state;
        state_changed_flag = 1;
        
        // 自动更新全局状态变量
        g_key_ctrl.current_screen = current_state;
    }
}