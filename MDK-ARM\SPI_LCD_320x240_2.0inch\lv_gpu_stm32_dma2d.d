spi_lcd_320x240_2.0inch/lv_gpu_stm32_dma2d.o: \
  ..\lvgl\src\draw\stm32_dma2d\lv_gpu_stm32_dma2d.c \
  ..\lvgl\src\draw\stm32_dma2d\lv_gpu_stm32_dma2d.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\misc\lv_color.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\misc\..\lv_conf_internal.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\misc\..\lv_conf_kconfig.h \
  ..\lvgl\lv_conf.h ..\lvgl\src\draw\stm32_dma2d\..\..\misc\lv_assert.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\misc\lv_log.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\misc\lv_types.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\misc\lv_mem.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\misc\lv_math.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\lv_hal_disp.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\lv_hal.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\lv_hal_indev.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\lv_conf_internal.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\misc\lv_area.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\misc\..\lv_conf_internal.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\misc\lv_timer.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\misc\..\hal\lv_hal_tick.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\misc\..\hal\..\lv_conf_internal.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\lv_hal_tick.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\lv_draw.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\..\lv_conf_internal.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\..\misc\lv_style.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\..\misc\..\font\lv_font.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\..\misc\..\font\..\lv_conf_internal.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\..\misc\..\font\lv_symbol_def.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\..\misc\..\font\..\misc\lv_area.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\..\misc\lv_color.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\..\misc\lv_area.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\..\misc\lv_anim.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\..\misc\..\lv_conf_internal.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\..\misc\lv_txt.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\..\misc\lv_printf.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\..\misc\lv_types.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\..\misc\lv_assert.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\..\misc\lv_bidi.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\..\misc\lv_style_gen.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\lv_img_decoder.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\lv_img_buf.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\..\misc\lv_fs.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\lv_img_cache.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\lv_draw_rect.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\sw\lv_draw_sw_gradient.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\sw\..\..\misc\lv_color.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\sw\..\..\misc\lv_style.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\sw\lv_draw_sw_dither.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\sw\..\..\core\lv_obj_pos.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\sw\..\..\core\..\misc\lv_area.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\lv_draw_label.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\lv_draw_img.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\lv_draw_line.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\lv_draw_triangle.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\lv_draw_arc.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\lv_draw_mask.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\..\misc\lv_math.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\lv_draw_transform.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\draw\lv_draw_layer.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\misc\lv_color.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\hal\..\misc\lv_ll.h \
  ..\lvgl\src\draw\stm32_dma2d\..\sw\lv_draw_sw.h \
  ..\lvgl\src\draw\stm32_dma2d\..\sw\lv_draw_sw_blend.h \
  ..\lvgl\src\draw\stm32_dma2d\..\sw\..\..\misc\lv_color.h \
  ..\lvgl\src\draw\stm32_dma2d\..\sw\..\..\misc\lv_area.h \
  ..\lvgl\src\draw\stm32_dma2d\..\sw\..\..\misc\lv_style.h \
  ..\lvgl\src\draw\stm32_dma2d\..\sw\..\lv_draw_mask.h \
  ..\lvgl\src\draw\stm32_dma2d\..\sw\..\lv_draw.h \
  ..\lvgl\src\draw\stm32_dma2d\..\sw\..\..\hal\lv_hal_disp.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\core\lv_refr.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\core\lv_obj.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\core\..\lv_conf_internal.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\core\..\misc\lv_style.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\core\..\misc\lv_types.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\core\..\misc\lv_area.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\core\..\misc\lv_color.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\core\..\misc\lv_assert.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\core\..\hal\lv_hal.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\core\lv_obj_tree.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\core\lv_obj_pos.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\core\lv_obj_scroll.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\core\..\misc\lv_anim.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\core\lv_obj_style.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\core\..\misc\lv_bidi.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\core\lv_obj_style_gen.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\core\lv_obj_draw.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\core\..\draw\lv_draw.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\core\lv_obj_class.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\core\lv_event.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\core\lv_group.h \
  ..\lvgl\src\draw\stm32_dma2d\..\..\core\..\misc\lv_ll.h
